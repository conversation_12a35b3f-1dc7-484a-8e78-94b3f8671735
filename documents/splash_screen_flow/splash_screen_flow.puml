@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
title Splash Screen Flow

Actor User as user
participant Evo<PERSON><PERSON> as app
participant EvoGateway as be

user -> app: open app
app --> user: display Splash Screen
app -> app: check device security
app -> be: log event mobile_data
    note over be
        ref: [[https://trustingsocial1.atlassian.net/browse/EMA-1349 EMA-1349]]
    end note
alt #mistyrose insecure device
    app -> app: delete all data of local storage
    app --> user: display insecure device dialog
    opt #white user tap dialog's close button
        user -> app: tap close button on the dialog
        app -> app: exit app
    end opt
else #technology secure device
    alt #white toggle RemoteConfig enabled
        app -> app: init RemoteConfig
    end alt
    app -> app: get AppStatus
    app -> app: load all data of local storage into memory
    app -> app: update biometrics status
    alt #white AppStatus.tutorial
        alt toggle NonUserHomePageVersion.version_2 enabled
            app --> user: display Main Screen (isLoggedIn = false)
        else #whitesmoke
            app --> user: display Tutorial Screen
        end alt
    else #whitesmoke
        alt #white accessToken is expired && refreshToken is NOT expired
            app -> be: refresh token | POST: v3/user/signin/verify
                note over be
                    data = {
                        "type": TypeLogin.token,
                        "token": token,
                    }
                    [[https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/SignIn/VerifySignIn API docs]]
                end note
            be --> app: API response
            app -> app: save authentication info to local storage
        end alt
        app -> app: get access token from local storage
        app -> app: check accessToken to know if user is logged in
        alt #white AppStatus.loggedIn
            app --> user: display Main Screen (isLoggedIn = true)
        else #whitesmoke
            app -> app: clear old token data
            app --> user: display Main Screen (isLoggedIn = false)
        end alt
     end alt
     app -> app: set BiometricsTypeUIModel
end alt

@enduml