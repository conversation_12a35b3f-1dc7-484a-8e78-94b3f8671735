@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"

actor User as user
participant E<PERSON><PERSON><PERSON> as app
participant EvoGateway as gateway

title Face Auth - Mock Flow

user -> app: click **Start Selfie** button
app --> app: check if MockTestFlow is enabled

note left
    mockTestFlow conditions:
        - isDebugMode
        - flavor == UAT or STAG
    Refer: [[https://trustingsocial1.atlassian.net/wiki/x/JQDhxQ Evo App Mock Test Config]]
end note
alt #Salmon mockTestFlow is NOT enabled
    app --> user: back to normal flow
    user -> app: do FaceAuth - normal flow
else #LightGreen mockTestFlow is enabled
    app --> user: show popup to request Storage permission
    user -> app: User perform Grant or Deny

    alt #Salmon deny Storage permission
        app --> user: back to normal flow
        user -> app: do FaceAuth - normal flow

    else #LightGreen  grant Storage permission
        app --> app: read MockFaceAuthData from local storage
        note left of app
            * manual_link_card_flow:
                - **enable**: true/false
                - **data**: {
                    - **image_ids**: List<String>
                    - **video_ids**: List<String>
                }
            * sign_in_flow:
                - **enable**: true/false
                - **data**: {
                    - **image_ids**: List<String>
                    - **video_ids**: List<String>
                }
        end note
        alt #lightBlue MockFaceAuth: enable == true && data is not null
            app --> user: go to step **Verify FaceAuth** of FaceAuthFlow.puml with image_ids and video_ids
        else #salmon
            user -> app: do FaceAuth - normal flow
        end alt

    end
end
@enduml
