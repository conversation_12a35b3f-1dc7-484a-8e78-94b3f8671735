@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title DOP Native - Employment Dialog

user -> app:  open **DOPNativeEmploymentDialog**

opt user click "X" icon or tap outside dialog
    app --> user: close dialog
end opt

== Select Employment status ==
app -> be: GET dop/api/metadata

note left
    params: {
        "type": EMPLOYMENT_STATUS
    }
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Metadata-Data-API API docs]]**
end note

be --> app: Response

note left
    "data": [
        {
            "code": String,
            "name": String,
            "level": String
        },
    ]
end note

opt user click back "<" icon
    app --> user: close dialog
end opt

alt #lightpink list employment status empty
    app --> user: show empty list
    else #lightyellow list employment status not empty

        app --> user: show list employment status

        opt user search employment status
            app --> user: display filtered employment status
        end opt

        user -> app: select employment status
        app --> user: redirect to **Select employment**
end alt
== Select Employment ==

opt user click back "<" icon
    app --> user: return to **Select Employment status**
end opt

app -> be: GET dop/api/metadata

note left
    params: {
        "type": EMPLOYMENT,
        "code": String,
        "parentType": EMPLOYMENT_STATUS
    }
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Metadata-Data-API API docs]]**
end note

be --> app: Response

note left
    "data": [
        {
            "code": String,
            "name": String,
            "level": String
        },
    ]
end note

alt #lightpink list employment empty
    app --> user: close dialog with employment status code
    else #lightyellow list employment not empty

        app --> user: show list employment

        opt user search employment
            app --> user: display filtered employment
        end opt

        user -> app: select employment
        app --> user: close dialog with employment code
end alt

@enduml