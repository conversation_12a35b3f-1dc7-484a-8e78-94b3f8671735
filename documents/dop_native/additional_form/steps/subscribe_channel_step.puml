@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title DOP Native - Additional Form - Subscribe Channel

app --> user: Show UI screen with form_step = "af.addinfo.subscribe_channel"
app -> be: GET /dop/api/metadata?type=SUBSCRIBE_CHANNEL
note left
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Metadata-Data-API API docs]]**
end note
be --> app: Response

alt #Salmon status code != 200
    app --> user: Go to Error screen
    note right
        Refer: **dop_native_error_screen.puml**
    end note
else #LightGreen
    app --> user: Show channel options UI
    note right
        Default option: **"Zalo"** & **"Số điện thoại hiện tại"**
    end note
end alt

opt #cyan User press back button
  user -> app: User press back button
  app --> user: Go to previous step: **Emergency Contact**
  note right
       Refer File **emergency_contact_step.puml**
  end note
end opt

loop listen event user change selected channels
user -> app: User select channel options
    opt User select channel option: "Zalo"
        app --> user: Show section: "Tài khoản Zalo của bạn"
        note right
            Tài khoản Zalo của bạn
            * Số điện thoại hiện tại **(default)**
            * Số điện thoại khác
        end note
        opt User select option: "Số điện thoại khác"
        app --> user: Show TextField input other phone number
        user -> app: User input input other phone number
        app -> app: Validate phone number
            note left
               Refer: **[[https://docs.google.com/spreadsheets/d/1aCdNd3wrExqfgvFiz378txeLr4SWaYmD/edit?gid=1562654135#gid=1562654135&range=24:24 Validation rules]]**
            end note
            alt #Salmon Phone number isn't validated
                app --> user: Show TextField error message + Disable CTA button
                else  #LightGreen
                app --> user: Enable CTA button
            end alt
        end
    end
    alt #LightGray Selected channels is empty
        app --> user: Disable CTA button
        else #LightGreen
            app --> user: Enable CTA button
    end alt
end

user -> app: Press CTA button
app -> be: POST /dop/api/application/form/submit
note left
    Refer: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Application-Submit-Form-Data-API API docs]]**
end note
be --> app: Response

alt #Salmon status code != 200
    app --> user: Go to Error screen
    note right
        Refer: **dop_native_error_screen.puml**
    end note
else #White
    app -> be: GET /dop/application/state
    note left
        Refer: **[[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3544678490/ES+Combine+API+get+application+state+and+get+bootstrap#API-spec API docs]]**
    end note
    be --> app: Response
    alt #Salmon status code != 200
        app --> user: Go to Error screen
        note right
            Refer: **dop_native_error_screen.puml**
        end note
    else #LightGreen
        app --> user: Go to next step
        note right
            Refer: [[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3544744596/Appendix+Essential+source application state navigation mapping]]
        end note
    end alt
end alt
@enduml