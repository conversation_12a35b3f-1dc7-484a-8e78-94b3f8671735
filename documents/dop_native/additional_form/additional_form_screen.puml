@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title DOP Native - Additional Form
app--> user: current_step == "app_form.additional_info"
user -> app: go to DOPNativeAdditionalFormScreen
opt #cyan User press close button
  user -> app: Close button on AppBar of the screen
  app --> user: Behaviour as **Close DOP flow**
  note right
       Refer File **close_dop_flow.puml**
  end note
end opt
app -> be: GET dop/api/application/form_data

be --> app: Response
note left
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#1.-Get-form-data API docs]]**
end note
alt #coral statusCode != 200
    app --> user: Show error
    note right
        Refer File **dop_native_error_screen.puml**
    end note
else #lightgreen statusCode == 200
    app --> user: Show UI with form_step
    alt #lavender form_step == "af.addinfo.secret_question"
        app --> user: Show UI screen with form_step == "af.addinfo.secret_question"
        note right
            Refer File **secret_question_step.puml**
        end note
    else #orange form_step == "af.addinfo.address"
        app --> user: Show UI screen with form_step == "af.addinfo.address"
        note right
            Refer File **address_info_step.puml**
        end note
    else #strategy form_step == "af.addinfo.emergency_contact"
        app --> user: Show UI screen with form_step == "af.addinfo.emergency_contact"
        note right
            Refer File **emergency_contact_step.puml**
        end note
    else #Thistle form_step == "af.addinfo.subscribe_channel"
        app --> user: Show UI screen with form_step == "af.addinfo.subscribe_channel"
        note right
            Refer File **subscribe_channel_step.puml**
        end note
    end alt
end alt

@enduml