@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title DOP Native - Quick and Second appraising flow
note right user
 User enter DOP Flow with state:
 * appraising.quick_approval
 * OR appraising.second_approval
 OR user finished inputting data at **DOPNativeEkycConfirmAdditionalInfoScreen**
end note
app --> user: redirect to **DOPNativeQuickAndSecondApprisingVerificationScreen**

opt user taps on CTA (X) on AppBar
    ref over app, user
        **DOP Native - Close DOP flow**
    end ref
end opt

== Polling for Appraising status==
app -> be: GET dop/api/appraising/status
be --> app: return
note right app
    response:
    * status
    * status_code
Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-Appraising-status API docs]]**
end note
alt status_code == 200
    alt status == success
        app -> be: GET /dop/application/state
        note left be
            request:
            * token
            * flow_selected_at
            Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Application-State-API API docs]]**
        end note
        be --> app: return
        note right app
            response:
            * status_code
            * current_step
        end note
        alt #LightYellow status_code == 200
            alt current_step == appraising.second_approval
                app --> user: back to **Polling for Appraising status** steps
            else current_step == ekyc.selfie.flash
                app --> user: redirect to **AppraisingResultSuccessScreen**
                ...delay 10s...
                app --> user: redirect to **DOPNativeSelfieCaptureIntroductionScreen**
                ref over app, user
                    **DOP Native - EKYC Flash Selfie** diagram
                end ref
            else other cases
                app --> user: redirect to next state
            end alt
        else #LightPink status_code != 200
            app --> user: redirect to **AppraisingResultErrorScreen**
        end alt
    else status == pending || status == in_progress
        ...delay 1s...
        app --> user: back to **Polling for Appraising status** steps
    end alt
else #LightPink status_code != 200
    app --> user: redirect to **common error screen**
end alt

@enduml