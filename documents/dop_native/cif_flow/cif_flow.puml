@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title DOP Native - CIF Flow

note right user
User enter DOP Flow with
* state **cif.confirm**
* ui_version
   - v9.4.4.0: difPhone
   - v9.4.5.0: difCif
   - v9.4.6.0: difNationId
   - v9.4.7.0: difInfo
   - v9.4.8.0: cifReopen
end note
app --> user: redirect to **DOPNativeCifConfirmScreen**

opt user taps on CTA (X) on AppBar
    ref over user, app
        **DOP Native - Close DOP flow**
    end ref
end opt

app -> be: GET dop/api/application/form_data
be --> app: return
note right app
    response
    * status_code
    * cifInfo.useNewCif
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#1.-Get-form-data API docs]]**
end note

alt status_code == 200
    alt cifInfo.useNewCif != null
        app --> user: go to **Cif UI with useNewCif**
    else cifInfo.useNewCif == null
        app --> user: go to **Cif UI without useNewCif**
    end
else #LightPink status_code != 200
    app -> user: redirect to **Error Screen**
end

== Cif UI with useNewCif==
alt ui_version == v9.4.6.0
    app --> user: show UI **difNationIdWithCifInfo**
    opt user taps on CTA to open card
        app --> user: go to **Confirm CIF** steps with useNewCif = true
    else user taps on CTA to confirm info updated
        app --> user: go to **Confirm CIF** steps with useNewCif = false
    else user taps on CTA to view nearest branches
        app --> user: redirect to **WebView** to show map with nearest branches
    end opt
else #Khaki ui_version == v9.4.4.0 || v9.4.5.0 || v9.4.7.0 || v9.4.8.0
    app --> user: show UI **otherDifWithCifInfo**
    ref over user, app
        **CTA Confirm CIF & Nearest branch** steps
    end ref
else #LightPink other ui_version
    app -> user: redirect to **Error Screen**
end alt

== Cif UI without useNewCif==
alt ui_version == v9.4.4.0
    app --> user: show UI **difPhone**
    ref over user, app
        **CTA Confirm CIF & Nearest branch** steps
    end ref
else #Khaki ui_version == v9.4.5.0
    app --> user: show UI **difCif**
    ref over user, app
        **CTA Confirm CIF & Nearest branch** steps
    end ref
else #LightBlue ui_version == v9.4.6.0
    app --> user: show UI **difNationIdWithoutCifInfo**
    opt user taps on CTA to open card
        app --> user: go to **Confirm CIF** steps with useNewCif = true
    else user taps on CTA to confirm info updated
        app --> user: go to **Confirm CIF** steps with useNewCif = false
    end opt
else #LightYellow ui_version == v9.4.7.0
    app --> user: show UI **difInfo**
    ref over user, app
        **CTA Confirm CIF & Nearest branch** steps
    end ref
else #LightGrey ui_version == v9.4.8.0
    app --> user: show UI **cifReopen**
    ref over user, app
        **CTA Confirm CIF & Nearest branch** steps
    end ref
else #LightPink other ui_version
    app -> user: redirect to **Error Screen**
end alt

== Confirm CIF==
app -> be: POST dop/api/cif/confirm
note left be
    request
    * use_new_cif: bool
    Refer: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3565223937/CIF+Flow#In-state-cif.confirm API docs]]**
end note
be --> app: return
alt status_code == 200
    app -> be: GET **dop/api/ekyc/status**
    note left be
        request:
            - job_id
        Refer: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-Ekyc-status API docs]]**
    end note
    be --> app: return
    alt status_code == 200
        app --> user: redirect to next DOP step
    else #LightPink status_code != 200
        app -> user: redirect to **Error Screen**
    end alt
else #LightPink status_code != 200
    app -> user: redirect to **Error Screen**
end alt

== CTA Confirm CIF & Nearest branch==
opt user taps on CTA to confirm info updated
    app --> user: go to **Confirm CIF** steps with useNewCif = false
else user taps on CTA to view nearest branches
    app --> user: redirect to **WebView** to show map with nearest branches
end opt

@enduml