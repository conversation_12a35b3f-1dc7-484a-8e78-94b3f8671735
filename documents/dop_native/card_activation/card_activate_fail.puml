@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"

actor "**User**" as User
participant "**EvoApp**" as App
title DOP Native - Card Activate Fail

note right User
User go to DOP flow with
*state is **card_status.information**
*activation_status is not in **(null || temporary_blocked || permanent_blocked || lock_card || activated)**
*can_activate_card is **false**
Refer to **card_activation_processing.puml** Diagram
end note

opt User presses CTA (X) on app bar
    ref over User, App
        **Close DOP flow** diagram
    end ref
end opt

App --> User: show banner with CTA **Tải ngay**

opt
   User -> App: click on CTA ** Tải ngay**
   App --> User: launch **download TPBank app** url
end opt

opt
   User -> App: click **Xem hợp đồng điện tử**
   App --> User: redirect to **DOPNativeEContractDownloadScreen**
   note right
      Refer: **download_e_contract_flow.puml**
   end note
end opt

opt User click on the phone number in **Tổng đài TPBank ...**
    User -> App: Press phone number
    App --> User: Launch phone dial
end opt

opt User click **ProtectCVVDetail (Xem chi tiết: Tại đây)**
    User -> App: click show detail **ProtectCVVDetail (Xem chi tiết: Tại đây)**
    App --> User: redirect to WebView with TPBank's protect cvv url
end opt

@enduml