@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

note right user
    Refer ticket: [[https://trustingsocial1.atlassian.net/browse/EMA-4745 EMA-4745]]
end note

title EvoApp - Activate POS Limit Screen

note right user
 User press continue from **SetupPosLimitIntroductionScreen** or **ActivateCardIntroductionScreen**
 App will redirect to ActivatePOSLimitScreen with params:
     entryPoint, // activate_card_screen or set_pos_limit_screen
     posLimitAllow,
     creditLimit

 Refer: [[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3787620432/ES+CO+Dive-in+Card+activation+within+payment+flow BE dive-in]], **activate_pos_limit_introduction_screens.puml**
end note


note left

end note

opt user press Back button
    user -> app: press **back** button
    app --> user: Back to previous screen
end

loop User input amount
    user -> app: Input POS limit amount
    app -> app: validate POS limit amount
    note right app
        Refer validate rule: [[https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/3773464803/Activate+card+enable+POS+limit+during+payment+process+on+EVO+App#5.-Acceptance-criteria PRD]]
    end note
    app --> user: display amount suggestion
    note right app
        Refer: [[https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/3773464803/Activate+card+enable+POS+limit+during+payment+process+on+EVO+App#5.-Acceptance-criteria PRD suggestion]]
    end note
    opt user press suggestion item
        user -> app: press suggestion item
        app -> app: fill suggestion item into text field
    end opt

    alt user input invalid amount
        app --> user: disable CTA "Xác nhận"
    else user input valid amount
        app --> user: enable CTA "Xác nhận"
    end alt
end

user -> app: press **Xác nhận** CTA

alt user back from 3D secure screen AND within 60s countdown
    app --> user: Show bottom sheet "Waiting 60s"
    note right user
        Ref: **[[https://trustingsocial1.atlassian.net/browse/EMA-5294 Waiting 60s bottom sheet]]**, **activate_pos_limit_waiting_bottom_sheet.puml**

    end note
else entryPoint == set_pos_limit_screen
    app -> be: POST /card/set-pos-limit
    note right
        body:
            {
                pos_limit
            }
        Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3787620432/ES+CO+Dive-in+Card+activation+within+payment+flow API docs]]**
    end note

    be --> app: API response

    alt status_code == 400 && verdict == redirect_to_tpb
        app -> user: Redirect to **ActivateCardGuidanceScreen**
        app -> app: clear **ActivatedPOSLimitState**
    else status_code == 200
        app -> user: Pop to **ActivatedPOSLimitState.entryPoint**, continue process payment
        app -> app: clear **ActivatedPOSLimitState**
    else  #mistyrose status_code != 200
        app -> user: Show error message
    end alt
else #Azure entryPoint == activate_card_screen
    app -> be: POST /card/activate
    note right
        body:
            {
                pos_limit
            }
        Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3787620432/ES+CO+Dive-in+Card+activation+within+payment+flow API docs]]**
    end note

    be --> app: API response

    alt #white status_code == 429 && verdict == invalid_state
        app -> be: GET /card/activation-status
            note right
                Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3787620432/ES+CO+Dive-in+Card+activation+within+payment+flow API docs]]**
            end note
        be --> app: return response
        alt status_code == 200
            alt card_status == activated
                app -> user: Pop to **ActivatedPOSLimitState.entryPoint**, continue process payment
                app -> app: clear **ActivatedPOSLimitState**
            else activation_status != activated && allow_activate_card == true
                app -> user: Redirect to **ActivateCardIntroductionScreen**
            else
                app -> user: Redirect to **ActivateCardGuidanceScreen**
            end
        else  #mistyrose status_code != 200
            app -> user: Show error message
        end
    else status_code == 200
        note right app
            API response: {
                redirect_url
            }
        end note

        app -> user: Redirect to **ActivatePosLimitThreeDSecureScreen**
        note right user
            params: {
                redirect_url
            }

            Refer: activate_pos_limit_three_d_secure_screen.puml
        end note
    else status_code == 400 && verdict == redirect_to_tpb
            app -> user: Redirect to **ActivateCardGuidanceScreen**
            app -> app: clear **ActivatedPOSLimitState**
    else #mistyrose status_code != 200
        app -> user: Show error message
    end alt

end alt



@enduml