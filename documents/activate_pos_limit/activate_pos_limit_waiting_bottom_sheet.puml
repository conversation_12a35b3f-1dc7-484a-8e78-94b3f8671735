@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app

title EvoApp - Activate POS Limit Waiting Bottom Sheet

note right
    This bottom sheet will be shown when user back from 3D secure screen AND within 60s countdown
    Or after user input 3DS screen, goto payment but within 60s from 3DS success

    To avoid issue request 3DS two times in 60s
end note

app --> user: Display bottom sheet "Waiting 60s"

opt user press CTA "Đã hiểu"
    user -> app: press CTA "Đã hiểu"
    app --> user: close bottom sheet
end opt

opt user press close button
    user -> app: press close button
    app --> user: close bottom sheet
end opt

loop 60s countdown
    app -> app: countdown 60s

    alt countdown is over
        app --> user: Hide bottom sheet
        app -> app: clear saved data
        note right app
            App clear saved time in local storage
            With key: **time_need_to_wait_after_card_activation**
        end note
    else countdown is not over
        app --> user: Update countdown timer
    end alt
end

@enduml