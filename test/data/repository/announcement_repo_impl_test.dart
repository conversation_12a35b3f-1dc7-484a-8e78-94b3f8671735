import 'package:evoapp/data/repository/announcement_repo.dart';
import 'package:evoapp/data/repository/announcement_repo_impl.dart';
import 'package:evoapp/data/request/reward_request.dart';
import 'package:evoapp/data/request/transaction_request.dart';
import 'package:evoapp/data/response/announcement_list_entity.dart';
import 'package:evoapp/data/response/announcement_status_entity.dart';
import 'package:evoapp/data/response/transaction_list_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/test_util.dart';
import 'mock_common_http_client.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  late MockCommonHttpClient httpClient;
  late AnnouncementRepo announcementRepo;
  late LoggingRepo mockLoggingRepo;

  const Map<String, dynamic> fakeUnSerializableData = <String, dynamic>{'data': 123};
  final BaseResponse responseUnSerializableData =
      BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: fakeUnSerializableData);

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton(() => CommonUtilFunction());

    httpClient = MockCommonHttpClient();
    announcementRepo = AnnouncementRepoImpl(httpClient);

    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();

    when(() => mockLoggingRepo.logErrorEvent(
        errorType: any(named: 'errorType'), args: any(named: 'args'))).thenAnswer((_) async {
      return Future<void>.value();
    });
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('test getAnnouncements function', () {
    test(
        'test getAnnouncements function with mock is enable and nextCursor is empty - the first page',
        () async {
      const String mockFileName = 'announcements.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.get(AnnouncementRepoImpl.announcementUrl,
          params: any(named: 'params'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final AnnouncementListEntity response = await announcementRepo.getAnnouncements(
        AnnouncementRequest(
          limit: 1,
          status: '',
          nextCursor: '',
        ),
        mockConfig: const MockConfig(enable: true, fileName: mockFileName),
      );

      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.announcements?.isNotEmpty, true);
      expect(response.nextCursor, '2');
    });

    test('test getAnnouncements function with mock is enable and nextCursor is 2 - the middle page',
        () async {
      const String mockFileName = 'announcements_page_2.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.get(AnnouncementRepoImpl.announcementUrl,
          params: any(named: 'params'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final AnnouncementListEntity response = await announcementRepo.getAnnouncements(
        AnnouncementRequest(
          limit: 1,
          status: '',
          nextCursor: '2',
        ),
        mockConfig: const MockConfig(enable: true, fileName: mockFileName),
      );

      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.announcements?.isNotEmpty, true);
      expect(response.nextCursor, '3');
    });

    test('test getAnnouncements function with mock is enable and nextCursor is 3 - the last page',
        () async {
      const String mockFileName = 'announcements_page_3.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.get(AnnouncementRepoImpl.announcementUrl,
          params: any(named: 'params'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final AnnouncementListEntity response = await announcementRepo.getAnnouncements(
        AnnouncementRequest(
          limit: 1,
          status: '',
          nextCursor: '3',
        ),
        mockConfig: const MockConfig(enable: true, fileName: mockFileName),
      );

      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.announcements?.isNotEmpty, false);
    });

    test('test getAnnouncements function with mock is disable and api return fail', () async {
      final AnnouncementRequest request = AnnouncementRequest(limit: 1, status: '', nextCursor: '');

      when(
        () => httpClient.get(AnnouncementRepoImpl.announcementUrl, params: any(named: 'params')),
      ).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null);
      });

      final AnnouncementListEntity response = await announcementRepo.getAnnouncements(request);

      expect(response.statusCode, CommonHttpClient.BAD_REQUEST);
      expect(response.announcements, null);
    });

    test(
      'test getAnnouncements function with mock is disable and api return success and invalid data',
      () async {
        final AnnouncementRequest request =
            AnnouncementRequest(limit: 1, status: '', nextCursor: '');

        when(
          () => httpClient.get(AnnouncementRepoImpl.announcementUrl, params: any(named: 'params')),
        ).thenAnswer((_) async {
          return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: null);
        });

        final AnnouncementListEntity response = await announcementRepo.getAnnouncements(request);

        expect(response.statusCode, CommonHttpClient.SUCCESS);
        expect(response.announcements, null);
      },
    );

    test(
      'test getAnnouncements function with mock is disable and api return success and valid data',
      () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('announcements.json');

        when(() =>
                httpClient.get(AnnouncementRepoImpl.announcementUrl, params: any(named: 'params')))
            .thenAnswer((_) async {
          return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
        });

        final AnnouncementRequest request =
            AnnouncementRequest(limit: 1, status: '', nextCursor: '');

        final AnnouncementListEntity response = await announcementRepo.getAnnouncements(request);

        expect(response.statusCode, CommonHttpClient.SUCCESS);
        expect(response.announcements?.isNotEmpty, true);
      },
    );

    test(
      'test getAnnouncements function with mock is disable and api return success and invalid data',
      () async {
        when(() =>
                httpClient.get(AnnouncementRepoImpl.announcementUrl, params: any(named: 'params')))
            .thenAnswer((_) async {
          return responseUnSerializableData;
        });

        final AnnouncementRequest request =
            AnnouncementRequest(limit: 1, status: '', nextCursor: '');

        final AnnouncementListEntity response = await announcementRepo.getAnnouncements(request);

        expect(response.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      },
    );
  });

  group('test updateAnnouncementStatus function', () {
    test('test updateAnnouncementStatus function with mock is enable', () async {
      const String mockFileName = 'announcement_update_status.json';
      const int id = 1;

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.patch(
          AnnouncementRepoImpl.updateStatusAnnouncementUrl.replaceAll('{id}', id.toString()),
          data: any(named: 'data'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final AnnouncementStatusEntity response = await announcementRepo.updateAnnouncementStatus(
          id, 'read',
          mockConfig: const MockConfig(enable: true, fileName: mockFileName));

      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.id, 1);
      expect(response.status, 'read');
    });

    test(
        'test updateAnnouncementStatus function with mock is disable api return success but cannot parse json',
        () async {
      const int id = 1;
      const String status = 'read';
      when(() => httpClient.patch(
          AnnouncementRepoImpl.updateStatusAnnouncementUrl.replaceAll('{id}', id.toString()),
          data: any(named: 'data'))).thenAnswer((_) async {
        return responseUnSerializableData;
      });

      final AnnouncementStatusEntity response =
          await announcementRepo.updateAnnouncementStatus(id, status);

      expect(response.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('test updateAnnouncementStatus function with mock is disable api return fail', () async {
      const int id = 1;
      const String status = 'read';
      when(() => httpClient.patch(
          AnnouncementRepoImpl.updateStatusAnnouncementUrl.replaceAll('{id}', id.toString()),
          data: any(named: 'data'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null);
      });

      final AnnouncementStatusEntity response =
          await announcementRepo.updateAnnouncementStatus(id, status);

      expect(response.statusCode, CommonHttpClient.BAD_REQUEST);
    });

    test(
        'test updateAnnouncementStatus function with mock is disable, api return success and invalid data',
        () async {
      const int id = 1;
      const String status = 'read';
      const String mockFileName = 'announcements_invalid_data.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.patch(
          AnnouncementRepoImpl.updateStatusAnnouncementUrl.replaceAll('{id}', id.toString()),
          data: any(named: 'data'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final AnnouncementStatusEntity response =
          await announcementRepo.updateAnnouncementStatus(id, status);

      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.id, null);
      expect(response.status, null);
    });

    test(
        'test updateAnnouncementStatus function with mock is disable, api return success and valid data',
        () async {
      const int id = 1;
      const String status = 'read';
      const String mockFileName = 'announcements_valid_data.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.patch(
          AnnouncementRepoImpl.updateStatusAnnouncementUrl.replaceAll('{id}', id.toString()),
          data: any(named: 'data'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final AnnouncementStatusEntity response =
          await announcementRepo.updateAnnouncementStatus(id, status);

      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.id, id);
      expect(response.status, status);
    });
  });

  group('test getTransactions function', () {
    test('test getTransactions function with mock is enable', () async {
      const String mockFileName = 'transaction.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.get(AnnouncementRepoImpl.announcementUrl,
          params: any(named: 'params'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final TransactionListEntity response = await announcementRepo.getTransactions(
        TransactionRequest(lastNotificationId: 1, limit: 1, status: 'read'),
        mockConfig: const MockConfig(enable: true, fileName: mockFileName),
      );

      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.transactions?.isNotEmpty, true);
    });

    test('test getTransactions function with mock is disable, api return fail', () async {
      final TransactionRequest request =
          TransactionRequest(lastNotificationId: 1, limit: 1, status: 'read');

      when(() => httpClient.get(AnnouncementRepoImpl.announcementUrl, params: any(named: 'params')))
          .thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null);
      });

      final TransactionListEntity response = await announcementRepo.getTransactions(request);

      expect(response.statusCode, CommonHttpClient.BAD_REQUEST);
      expect(response.transactions, null);
    });

    test('test getTransactions function with mock is disable, api return success and invalid data',
        () async {
      const String mockFileName = 'transaction_wrong_key.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      final TransactionRequest request =
          TransactionRequest(lastNotificationId: 1, limit: 1, status: 'read');

      when(
        () => httpClient.get(AnnouncementRepoImpl.announcementUrl, params: any(named: 'params')),
      ).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final TransactionListEntity response = await announcementRepo.getTransactions(request);

      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.transactions, null);
    });

    test('test getTransactions function with mock is false, api return success and valid data',
        () async {
      const String mockFileName = 'transaction.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      final TransactionRequest request =
          TransactionRequest(lastNotificationId: 1, limit: 1, status: 'read');
      when(
        () => httpClient.get(AnnouncementRepoImpl.announcementUrl, params: any(named: 'params')),
      ).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final TransactionListEntity response = await announcementRepo.getTransactions(request);

      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.transactions?.isNotEmpty, true);
    });

    test(
        'test getTransactions function with mock is false, api return success and cannot parse json',
        () async {
      final TransactionRequest request =
          TransactionRequest(lastNotificationId: 1, limit: 1, status: 'read');
      when(
        () => httpClient.get(AnnouncementRepoImpl.announcementUrl, params: any(named: 'params')),
      ).thenAnswer((_) async {
        return responseUnSerializableData;
      });

      final TransactionListEntity response = await announcementRepo.getTransactions(request);

      expect(response.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });
}
