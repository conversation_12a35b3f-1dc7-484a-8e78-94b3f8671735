import 'package:evoapp/data/response/plain_code_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';

void main() {
  group('PlainCodeEntity', () {
    const String fakePlainCode = 'test_plain_code';

    test('fromBaseResponse creates a valid object', () {
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'plain_code': fakePlainCode,
          }
        },
      );

      final PlainCodeEntity entity = PlainCodeEntity.fromBaseResponse(response);

      expect(entity.plainCode, fakePlainCode);
    });

    test('from<PERSON><PERSON> creates a valid object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'plain_code': fakePlainCode,
      };

      final PlainCodeEntity entity = PlainCodeEntity.fromJson(json);

      expect(entity.plainCode, fakePlainCode);
    });

    test('toJson returns a valid JSON representation', () {
      final PlainCodeEntity entity = PlainCodeEntity(
        plainCode: fakePlainCode,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json, isNotNull);
      expect(json['plain_code'], fakePlainCode);
    });

    test('unserializable constructor sets localExceptionCode', () {
      final PlainCodeEntity entity = PlainCodeEntity.unserializable();

      expect(entity.plainCode, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });
}
