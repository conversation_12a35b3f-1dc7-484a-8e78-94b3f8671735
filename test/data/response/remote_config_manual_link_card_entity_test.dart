import 'package:evoapp/data/response/remote_config_manual_link_card_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const bool fakeEnable = true;
  const String fakeDescription = 'fake_description';

  group('RemoteConfigManualLinkCardEntity', () {
    test('fromJson should return a valid RemoteConfigManualLinkCardEntity', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'enable': fakeEnable,
        'description': fakeDescription,
      };

      final RemoteConfigManualLinkCardEntity result =
          RemoteConfigManualLinkCardEntity.fromJson(json);

      expect(result.enable, fakeEnable);
      expect(result.description, fakeDescription);
    });

    test('toJson should return a valid JSON representation', () {
      final RemoteConfigManualLinkCardEntity manualLinkCard = RemoteConfigManualLinkCardEntity(
        enable: fakeEnable,
        description: fakeDescription,
      );

      final Map<String, dynamic> json = manualLinkCard.toJson();

      expect(json['enable'], fakeEnable);
      expect(json['description'], fakeDescription);
    });
  });
}
