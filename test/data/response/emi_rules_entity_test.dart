import 'package:evoapp/data/response/emi_rules_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('constructor', () {
    final Map<String, List<String>> blockedMerchants = <String, List<String>>{
      'product1': <String>['merchant1', 'merchant2'],
      'product2': <String>['merchant3'],
    };

    final EmiRulesEntity emiRules = EmiRulesEntity(
      rules: Rules(
        allowedProductCodes: <String>['product1', 'product2'],
        minUserChargeAmount: 1000,
        productCodeToBlockedMerchants: blockedMerchants,
      ),
    );

    expect(emiRules.rules?.allowedProductCodes, <String>['product1', 'product2']);
    expect(emiRules.rules?.minUserChargeAmount, 1000);
    expect(emiRules.rules?.productCodeToBlockedMerchants, blockedMerchants);
    expect(emiRules.rules?.productCodeToBlockedMerchants?['product1'],
        <String>['merchant1', 'merchant2']);
    expect(emiRules.rules?.productCodeToBlockedMerchants?['product2'], <String>['merchant3']);
  });

  test('unserializable constructor', () {
    final EmiRulesEntity emiRules = EmiRulesEntity.unserializable();

    expect(emiRules.rules?.allowedProductCodes, null);
    expect(emiRules.rules?.minUserChargeAmount, null);
    expect(emiRules.rules?.productCodeToBlockedMerchants, null);
    expect(emiRules.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
  });

  test('verify fromBaseResponse work as correctly', () {
    final BaseResponse baseResponse = BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: <String, dynamic>{
        'data': <String, dynamic>{
          'rules': <String, dynamic>{
            'allowed_product_codes': <String>['product1', 'product2'],
            'min_user_charge_amount': 1000,
            'product_code_to_blocked_merchants': <String, dynamic>{
              'product1': <String>['merchant1', 'merchant2'],
              'product2': <String>['merchant3'],
            },
          }
        }
      },
    );

    final EmiRulesEntity emiRules = EmiRulesEntity.fromBaseResponse(baseResponse);

    expect(emiRules.rules?.allowedProductCodes, <String>['product1', 'product2']);
    expect(emiRules.rules?.minUserChargeAmount, 1000);
    expect(emiRules.rules?.productCodeToBlockedMerchants?['product1'],
        <String>['merchant1', 'merchant2']);
    expect(emiRules.rules?.productCodeToBlockedMerchants?['product2'], <String>['merchant3']);
  });

  test('verify toJson work as correctly', () {
    final Map<String, List<String>> blockedMerchants = <String, List<String>>{
      'product1': <String>['merchant1', 'merchant2'],
      'product2': <String>['merchant3'],
    };

    final EmiRulesEntity emiRules = EmiRulesEntity(
      rules: Rules(
        allowedProductCodes: <String>['product1', 'product2'],
        minUserChargeAmount: 1000,
        productCodeToBlockedMerchants: blockedMerchants,
      ),
    );

    final Map<String, dynamic> json = emiRules.toJson();

    expect(json['rules']['allowed_product_code'], <String>['product1', 'product2']);
    expect(json['rules']['min_user_charge_amount'], 1000);
    expect(json['rules']['product_code_to_blocked_merchants'], blockedMerchants);
    expect(json['rules']['product_code_to_blocked_merchants']['product1'],
        <String>['merchant1', 'merchant2']);
    expect(json['rules']['product_code_to_blocked_merchants']['product2'], <String>['merchant3']);
  });

  test('Rules.fromJson handles null productCodeToBlockedMerchants', () {
    final Map<String, dynamic> json = <String, dynamic>{
      'allowed_product_codes': <String>['product1', 'product2'],
      'min_user_charge_amount': 1000,
    };

    final Rules rules = Rules.fromJson(json);

    expect(rules.allowedProductCodes, <String>['product1', 'product2']);
    expect(rules.minUserChargeAmount, 1000);
    expect(rules.productCodeToBlockedMerchants, null);
  });

  test('Rules.fromJson handles empty productCodeToBlockedMerchants', () {
    final Map<String, dynamic> json = <String, dynamic>{
      'allowed_product_codes': <String>['product1', 'product2'],
      'min_user_charge_amount': 1000,
      'product_code_to_blocked_merchants': <String, dynamic>{},
    };

    final Rules rules = Rules.fromJson(json);

    expect(rules.allowedProductCodes, <String>['product1', 'product2']);
    expect(rules.minUserChargeAmount, 1000);
    expect(rules.productCodeToBlockedMerchants, <String, List<String>>{});
  });
}
