import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/card_benefits_button_item_entity.dart';
import 'package:evoapp/data/response/card_benefits_entity.dart';
import 'package:evoapp/data/response/card_benefits_item_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeTitle = 'fake_title';
  const String fakeUrl = 'https://fake.com/image1.jpg';
  const String fakeType = 'fake_type';

  group('CardBenefitsEntity', () {
    test('VoucherListEntity.unserializable creates an object with null properties', () {
      final CardBenefitsEntity cardBenefitsEntity = CardBenefitsEntity.unserializable();

      expect(cardBenefitsEntity.cardImages, isNull);
      expect(cardBenefitsEntity.buttons, isNull);
    });

    test('fromBaseResponse', () async {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'card_images': <dynamic>[
              <String, dynamic>{
                'image_url': fakeUrl,
              },
            ],
            'buttons': <dynamic>[
              <String, dynamic>{
                'action': <String, dynamic>{
                  'type': fakeType,
                },
                'title': fakeTitle,
              },
            ],
          }
        },
      );

      final CardBenefitsEntity cardBenefitsEntity =
          CardBenefitsEntity.fromBaseResponse(baseResponse);

      expect(cardBenefitsEntity.cardImages?.length, baseResponse.data?['card_images'].length);
      expect(cardBenefitsEntity.buttons?.length, baseResponse.data?['buttons'].length);

      expect(cardBenefitsEntity.cardImages?.first.imageUrl, fakeUrl);
      expect(cardBenefitsEntity.buttons?.first.title, fakeTitle);
    });

    test('toJson returns a valid Map', () {
      final CardBenefitsEntity entity = CardBenefitsEntity(
        cardImages: <CardBenefitsItemEntity>[
          CardBenefitsItemEntity(
            imageUrl: fakeUrl,
            action: ActionEntity(
              type: fakeType,
            ),
          ),
        ],
        buttons: <CardBenefitsButtonItemEntity>[
          CardBenefitsButtonItemEntity(
            title: fakeTitle,
            action: ActionEntity(
              type: fakeType,
            ),
          ),
        ],
      );

      final Map<String, dynamic> json = entity.toJson();
      expect(json['card_images'], isNotNull);
      expect(json['buttons'], isNotNull);
    });
  });
}
