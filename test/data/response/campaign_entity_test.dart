import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/campaign_entity.dart';
import 'package:evoapp/data/response/offer_entity.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

void main() {
  // Action
  const String fakeLink = 'https://fake.com';
  const String fakeScreenName = 'screen_name';
  const String fakeActionLabel = 'fake_action_label';
  const String fakeType = 'fake_type';
  const Map<String, dynamic> fakeParameter = <String, dynamic>{'id': '123', 'code': 'abc'};

  // Next Action Earn Action
  const String fakeNextLink = 'https://fake_next.com';
  const String fakeNextScreenName = 'screen_name_next';
  const String fakeNextActionLabel = 'fake_action_label_next';
  const String fakeNextType = 'fake_type_next';
  const Map<String, dynamic> fakeNextParameter = <String, dynamic>{'id': '456', 'code': 'cdf'};

  const String fakeBanner = 'https://fake.com/banner.png';
  const String fakeCampaignType = 'fake_campaign_type';
  const String fakeCode = 'fake_code';
  const String fakeDescription = 'Example campaign description';
  const String fakeStartAt = '2022-01-01T00:00:00.000Z';
  const String fakeEndAt = '2022-01-31T23:59:59.000Z';
  const String fakeFormattedExpiry = 'Expires: Jan 31, 2022';
  const String fakeId = 'fake_campaign_id';
  const String fakeThumbnail = 'https://fake.com/thumbnail.png';
  const String fakeTitle = 'Example Campaign Title';
  const String fakeOfferId = 'fake_offer_id';

  late CommonUtilFunction commonUtilFunction;
  final DateTime expectedDateTime = DateTime(2023, 1, 2, 3, 4, 5);

  setUpAll(() {
    getIt.registerSingleton<CommonUtilFunction>(MockCommonUtilFunction());
    commonUtilFunction = getIt.get<CommonUtilFunction>();

    when(() => commonUtilFunction.toDateTime(any())).thenReturn(expectedDateTime);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('CampaignEntity', () {
    test('fromJson creates a valid CampaignEntity', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'action': <String, dynamic>{
          'args': <String, dynamic>{
            'link': fakeLink,
            'screen_name': fakeScreenName,
            'action_label': fakeActionLabel,
            'parameters': fakeParameter,
            'next_action': <String, dynamic>{
              'args': <String, dynamic>{
                'link': fakeNextLink,
                'screen_name': fakeNextScreenName,
                'action_label': fakeNextActionLabel,
                'parameters': fakeNextParameter,
              },
              'type': fakeNextType,
            },
          },
          'type': fakeType,
        },
        'earn_action': <String, dynamic>{},
        'banner': fakeBanner,
        'campaign_type': fakeCampaignType,
        'code': fakeCode,
        'description': fakeDescription,
        'start_at': fakeStartAt,
        'end_at': fakeEndAt,
        'formatted_expiry': fakeFormattedExpiry,
        'id': fakeId,
        'thumbnail': fakeThumbnail,
        'title': fakeTitle,
        'offers': <dynamic>[
          <String, dynamic>{
            'id': fakeOfferId,
          },
        ],
      };

      final CampaignEntity campaignEntity = CampaignEntity.fromJson(json);
      expect(campaignEntity.action, isNotNull);
      expect(campaignEntity.earnAction, isNotNull);
      expect(campaignEntity.banner, fakeBanner);
      expect(campaignEntity.campaignType, fakeCampaignType);
      expect(campaignEntity.code, fakeCode);
      expect(campaignEntity.description, fakeDescription);
      expect(campaignEntity.startAt, fakeStartAt);
      expect(campaignEntity.endAt, fakeEndAt);
      expect(campaignEntity.formattedExpiry, fakeFormattedExpiry);
      expect(campaignEntity.id, fakeId);
      expect(campaignEntity.thumbnail, fakeThumbnail);
      expect(campaignEntity.title, fakeTitle);
      expect(campaignEntity.offers?.first.id, fakeOfferId);
    });

    test('toJson returns a valid Map', () {
      final CampaignEntity campaignEntity = CampaignEntity(
        action: ActionEntity(
          type: fakeType,
          args: ArgsEntity(
            link: fakeLink,
            screenName: fakeScreenName,
            actionLabel: fakeActionLabel,
            parameters: ParametersEntity(id: '123', code: 'abc'),
            nextAction: ActionEntity(
              type: fakeNextType,
              args: ArgsEntity(
                link: fakeNextLink,
                screenName: fakeNextScreenName,
                actionLabel: fakeNextActionLabel,
                parameters: ParametersEntity(id: '456', code: 'cdf'),
              ),
            ),
          ),
        ),
        earnAction: ActionEntity(
          type: fakeNextType,
          args: ArgsEntity(
            link: fakeNextLink,
            screenName: fakeNextScreenName,
            actionLabel: fakeNextActionLabel,
            parameters: ParametersEntity(id: '456', code: 'cdf'),
          ),
        ),
        banner: fakeBanner,
        campaignType: fakeCampaignType,
        code: fakeCode,
        description: fakeDescription,
        startAt: fakeStartAt,
        endAt: fakeEndAt,
        formattedExpiry: fakeFormattedExpiry,
        id: fakeId,
        thumbnail: fakeThumbnail,
        title: fakeTitle,
        offers: <OfferEntity>[
          OfferEntity(
            id: fakeOfferId,
          ),
        ],
      );

      final Map<String, dynamic> json = campaignEntity.toJson();
      expect(json['action'], isNotNull);
      expect(json['earn_action'], isNotNull);
      expect(json['banner'], fakeBanner);
      expect(json['campaign_type'], fakeCampaignType);
      expect(json['code'], fakeCode);
      expect(json['description'], fakeDescription);
      expect(json['start_at'], fakeStartAt);
      expect(json['end_at'], fakeEndAt);
      expect(json['formatted_expiry'], fakeFormattedExpiry);
      expect(json['id'], fakeId);
      expect(json['thumbnail'], fakeThumbnail);
      expect(json['title'], fakeTitle);
      expect(json['offers'], isNotNull);
    });

    test('verify startExpireDateTime', () {
      final CampaignEntity campaignEntity = CampaignEntity(
        id: '1',
        startAt: '2022-01-01T00:00:00.000Z',
      );

      final DateTime? startExpireDateTime = campaignEntity.startExpireDateTime;
      expect(startExpireDateTime, isNotNull);
      expect(startExpireDateTime?.year, expectedDateTime.year);
      expect(startExpireDateTime?.month, expectedDateTime.month);
      expect(startExpireDateTime?.day, expectedDateTime.day);
      expect(startExpireDateTime?.hour, expectedDateTime.hour);
      expect(startExpireDateTime?.minute, expectedDateTime.minute);
      expect(startExpireDateTime?.second, expectedDateTime.second);

      verify(() => commonUtilFunction.toDateTime(campaignEntity.startAt)).called(1);
    });

    test('verify endExpireDateTime', () {
      final CampaignEntity campaignEntity = CampaignEntity(
        id: '1',
        endAt: '2022-01-01T00:00:00.000Z',
      );

      final DateTime? endExpireDateTime = campaignEntity.endExpireDateTime;
      expect(endExpireDateTime, isNotNull);
      expect(endExpireDateTime?.year, expectedDateTime.year);
      expect(endExpireDateTime?.month, expectedDateTime.month);
      expect(endExpireDateTime?.day, expectedDateTime.day);
      expect(endExpireDateTime?.hour, expectedDateTime.hour);
      expect(endExpireDateTime?.minute, expectedDateTime.minute);
      expect(endExpireDateTime?.second, expectedDateTime.second);

      verify(() => commonUtilFunction.toDateTime(campaignEntity.endAt)).called(1);
    });
  });
}
