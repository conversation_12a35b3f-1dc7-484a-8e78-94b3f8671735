import 'package:evoapp/data/response/emi_conversion_status_content_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EmiInfoConversionStatusContentEntity', () {
    const String fakeTitle = 'fake_title';
    const String fakeSubTitle = 'fake_sub_title';
    const String fakeShortTitle = 'fake_short_title';
    const String fakeMessage = 'fake_message';
    const String fakeDescription = 'fake_description';

    test('fromJson should create EmiInfoConversionStatusContentEntity() from JSON', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'title': fakeTitle,
        'sub_title': fakeSubTitle,
        'short_title': fakeShortTitle,
        'user_message': fakeMessage,
        'description': fakeDescription,
      };

      final EmiInfoConversionStatusContentEntity entity =
          EmiInfoConversionStatusContentEntity.fromJson(json);
      expect(entity.title, fakeTitle);
      expect(entity.subTitle, fakeSubTitle);
      expect(entity.shortTitle, fakeShortTitle);
      expect(entity.userMessage, fakeMessage);
      expect(entity.description, fakeDescription);
    });

    test('toJson should return a JSON map containing the proper values', () {
      final EmiInfoConversionStatusContentEntity entity = EmiInfoConversionStatusContentEntity(
        userMessage: fakeMessage,
        subTitle: fakeSubTitle,
        shortTitle: fakeShortTitle,
        title: fakeTitle,
        description: fakeDescription,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json['title'], fakeTitle);
      expect(json['sub_title'], fakeSubTitle);
      expect(json['short_title'], fakeShortTitle);
      expect(json['user_message'], fakeMessage);
      expect(json['description'], fakeDescription);
    });
  });
}
