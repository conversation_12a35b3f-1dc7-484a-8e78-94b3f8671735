import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/emi_offer_entity.dart';
import 'package:evoapp/data/response/merchant_info_entity.dart';
import 'package:evoapp/data/response/order_info_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/payment_info_entity.dart';
import 'package:evoapp/data/response/promotion_info_entity.dart';
import 'package:evoapp/data/response/store_info_entity.dart';
import 'package:evoapp/data/response/user_payment_information_entity.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

void main() {
  late CommonUtilFunction mockCommonUtilFunction;

  final DateTime fakeDateTime = DateTime(2022, 1, 2);
  const String fakeId = 'fake_id';
  const String fakeTransactionId = 'fake_transaction_id';
  const String fakeChannel = 'fake_channel';
  const String fakeCreatedAt = '2023-03-31T16:55:11+07:00';
  const String fakeProductCode = 'fake_product_code';
  const List<String> fakePromotionCodes = <String>[
    'fake_promotion_code_1',
    'fake_promotion_code_2'
  ];
  const String fakeStatus = 'fake_status';
  const String fakeUpdatedAt = '2022-01-02T00:00:00.000Z';
  final StoreInfoEntity fakeStoreInfo = StoreInfoEntity(
    id: 'fake_store_id',
    name: 'fake_store_name',
    address: 'fake_store_address',
  );
  final ActionEntity fakeNextAction = ActionEntity(
    type: 'fake_action_type',
  );
  final OrderInfoEntity fakeOrderInfo = OrderInfoEntity(
    id: 'fake_order_id',
  );
  final PaymentInfoEntity fakePaymentInfo = PaymentInfoEntity(
    paymentMethodId: 'fake_payment_method_id',
  );
  final PromotionInfoEntity fakePromotionInfo = PromotionInfoEntity(
    discountAmount: 10,
  );
  final UserPaymentInformationEntity fakeUserInfo = UserPaymentInformationEntity(
    id: 1,
  );
  const int fakeFee = 100;
  const int fakeOrderAmount = 200;
  const int fakePromotionAmount = 300;
  const int fakeUserChargeAmount = 400;
  final MerchantInfoEntity fakeMerchantInfo = MerchantInfoEntity(
    id: 'fake_merchant_id',
  );
  final EmiOfferEntity fakeEmiOfferEntity = EmiOfferEntity(
    id: 'fake_emi_offer_id',
  );

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<CommonUtilFunction>(() => MockCommonUtilFunction());
    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();

    when(() => mockCommonUtilFunction.toDateTime(any())).thenReturn(fakeDateTime);
  });

  group('OrderSessionEntity', () {
    test('verify fromJson()', () async {
      final Map<String, dynamic> json = <String, dynamic>{
        'id': fakeId,
        'transaction_id': fakeTransactionId,
        'channel': fakeChannel,
        'created_at': fakeCreatedAt,
        'product_code': fakeProductCode,
        'promotion_codes': fakePromotionCodes,
        'status': fakeStatus,
        'updated_at': fakeUpdatedAt,
        'store_info': <String, dynamic>{
          'id': fakeStoreInfo.id,
          'name': fakeStoreInfo.name,
          'address': fakeStoreInfo.address,
        },
        'promotion_info': <String, dynamic>{
          'discount_amount': fakePromotionInfo.discountAmount,
        },
        'next_action': <String, dynamic>{
          'type': fakeNextAction.type,
        },
        'order_info': <String, dynamic>{
          'id': fakeOrderInfo.id,
        },
        'payment_info': <String, dynamic>{
          'payment_method_id': fakePaymentInfo.paymentMethodId,
        },
        'user_info': <String, dynamic>{
          'id': fakeUserInfo.id,
        },
        'fee': fakeFee,
        'order_amount': fakeOrderAmount,
        'promotion_amount': fakePromotionAmount,
        'user_charge_amount': fakeUserChargeAmount,
        'merchant_info': <String, dynamic>{
          'id': fakeMerchantInfo.id,
        },
        'emi_offer': <String, dynamic>{
          'id': fakeEmiOfferEntity.id,
        },
      };

      final OrderSessionEntity orderSession = OrderSessionEntity.fromJson(json);

      expect(orderSession.id, fakeId);
      expect(orderSession.transactionId, fakeTransactionId);
      expect(orderSession.createdAt, fakeCreatedAt);
      expect(orderSession.createdAtDateTime, fakeDateTime);
      expect(orderSession.productCode, fakeProductCode);
      expect(orderSession.promotionCodes, fakePromotionCodes);
      expect(orderSession.status, fakeStatus);
      expect(orderSession.updatedAt, fakeUpdatedAt);
      expect(orderSession.storeInfo?.id, fakeStoreInfo.id);
      expect(orderSession.promotionInfo?.discountAmount, fakePromotionInfo.discountAmount);
      expect(orderSession.nextAction?.type, fakeNextAction.type);
      expect(orderSession.orderInfo?.id, fakeOrderInfo.id);
      expect(orderSession.paymentInfo?.paymentMethodId, fakePaymentInfo.paymentMethodId);
      expect(orderSession.userInfo?.id, fakeUserInfo.id);
      expect(orderSession.fee, fakeFee);
      expect(orderSession.orderAmount, fakeOrderAmount);
      expect(orderSession.promotionAmount, fakePromotionAmount);
      expect(orderSession.userChargeAmount, fakeUserChargeAmount);
      expect(orderSession.merchantInfo?.id, fakeMerchantInfo.id);
      expect(orderSession.emiOffer?.id, fakeEmiOfferEntity.id);
    });

    test('verify toJson()', () async {
      final OrderSessionEntity orderSession = OrderSessionEntity(
        id: fakeId,
        transactionId: fakeTransactionId,
        channel: fakeChannel,
        createdAt: fakeCreatedAt,
        productCode: fakeProductCode,
        promotionCodes: fakePromotionCodes,
        status: fakeStatus,
        updatedAt: fakeUpdatedAt,
        storeInfo: fakeStoreInfo,
        nextAction: fakeNextAction,
        orderInfo: fakeOrderInfo,
        paymentInfo: fakePaymentInfo,
        promotionInfo: fakePromotionInfo,
        userInfo: fakeUserInfo,
        fee: fakeFee,
        orderAmount: fakeOrderAmount,
        promotionAmount: fakePromotionAmount,
        userChargeAmount: fakeUserChargeAmount,
        merchantInfo: fakeMerchantInfo,
        emiOffer: fakeEmiOfferEntity,
      );

      final Map<String, dynamic> json = orderSession.toJson();

      expect(json['id'], fakeId);
      expect(json['transaction_id'], fakeTransactionId);
      expect(json['channel'], fakeChannel);
      expect(json['created_at'], fakeCreatedAt);
      expect(json['product_code'], fakeProductCode);
      expect(json['promotion_codes'], fakePromotionCodes);
      expect(json['status'], fakeStatus);
      expect(json['updated_at'], fakeUpdatedAt);
      expect(json['store_info']['id'], fakeStoreInfo.id);
      expect(json['promotion_info']['discount_amount'], fakePromotionInfo.discountAmount);
      expect(json['next_action']['type'], fakeNextAction.type);
      expect(json['order_info']['id'], fakeOrderInfo.id);
      expect(json['payment_info']['payment_method_id'], fakePaymentInfo.paymentMethodId);
      expect(json['user_info']['id'], fakeUserInfo.id);
      expect(json['fee'], fakeFee);
      expect(json['order_amount'], fakeOrderAmount);
      expect(json['promotion_amount'], fakePromotionAmount);
      expect(json['user_charge_amount'], fakeUserChargeAmount);
      expect(json['merchant_info']['id'], fakeMerchantInfo.id);
      expect(json['emi_offer']['id'], fakeEmiOfferEntity.id);
    });
  });
}
