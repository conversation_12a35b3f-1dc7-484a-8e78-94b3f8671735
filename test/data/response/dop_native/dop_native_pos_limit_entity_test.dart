import 'package:evoapp/data/response/dop_native/dop_native_pos_limit_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativePOSLimitActivationStatus', () {
    test('fromValue should return the correct enum value', () {
      expect(DOPNativePOSLimitActivationStatus.fromValue('success'),
          equals(DOPNativePOSLimitActivationStatus.success));

      expect(DOPNativePOSLimitActivationStatus.fromValue('failure'),
          equals(DOPNativePOSLimitActivationStatus.failure));

      expect(DOPNativePOSLimitActivationStatus.fromValue('pending'),
          equals(DOPNativePOSLimitActivationStatus.pending));

      expect(DOPNativePOSLimitActivationStatus.fromValue('timeout'),
          equals(DOPNativePOSLimitActivationStatus.timeout));

      expect(DOPNativePOSLimitActivationStatus.fromValue(''),
          equals(DOPNativePOSLimitActivationStatus.empty));

      expect(DOPNativePOSLimitActivationStatus.fromValue('invalid_value'),
          equals(DOPNativePOSLimitActivationStatus.unknown));
    });
  });

  group('DOPNativePosLimitEntity', () {
    test('fromJson should convert JSON to DOPNativePosLimitEntity object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'pos_limit_activation_status': 'success',
        'max_pos_limit_allow': 1000,
        'pos_limit_user_setting': 1000,
      };

      final DOPNativePosLimitEntity entity = DOPNativePosLimitEntity.fromJson(json);

      expect(entity.maxPosLimitAllow, equals(json['max_pos_limit_allow']));
      expect(entity.posLimitUserSetting, equals(json['pos_limit_user_setting']));
      expect(entity.posLimitActivationStatus, equals(DOPNativePOSLimitActivationStatus.success));
    });

    test('toJson should convert DOPNativePosLimitEntity object to JSON', () {
      const DOPNativePosLimitEntity entity = DOPNativePosLimitEntity(
        maxPosLimitAllow: 1000,
        posLimitUserSetting: 1000,
        posLimitActivationStatus: DOPNativePOSLimitActivationStatus.success,
      );

      final Map<String, dynamic>? json = entity.toJson();

      expect(json?['pos_limit_activation_status'], equals('success'));
      expect(json?['pos_limit_user_setting'], equals(1000));
      expect(json?['max_pos_limit_allow'], equals(1000));
    });
  });
}
