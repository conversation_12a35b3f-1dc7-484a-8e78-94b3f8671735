import 'package:evoapp/data/response/dop_native/dop_native_card_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeCardInfoEntity', () {
    test('from<PERSON><PERSON> creates a correct instance from JSON', () {
      final Map<String, dynamic> jsonMap = <String, dynamic>{'cardCategory': 'Debit'};

      final DOPNativeCardInfoEntity entity = DOPNativeCardInfoEntity.fromJson(jsonMap);

      expect(entity.cardCategory, 'Debit');
    });

    test('from<PERSON><PERSON> handles null cardCategory', () {
      final Map<String, dynamic> jsonMap = <String, dynamic>{'cardCategory': null};

      final DOPNativeCardInfoEntity entity = DOPNativeCardInfoEntity.fromJson(jsonMap);

      expect(entity.cardCategory, null);
    });

    test('toJson converts an entity instance to a JSON map', () {
      final DOPNativeCardInfoEntity entity =
          DOPNativeCardInfoEntity.fromJson(<String, dynamic>{'cardCategory': 'Credit'});

      final Map<String, dynamic> json = entity.toJson();

      expect(json, <String, dynamic>{'cardCategory': 'Credit'});
    });

    test('toJson handles null cardCategory', () {
      final DOPNativeCardInfoEntity entity =
          DOPNativeCardInfoEntity.fromJson(<String, dynamic>{'cardCategory': null});

      final Map<String, dynamic> json = entity.toJson();

      expect(json, <String, dynamic>{'cardCategory': null});
    });
  });
}
