import 'package:evoapp/data/response/dop_native/dop_native_working_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeWorkingInfoEntity', () {
    test('fromJson should initialize fields correctly', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'employmentId': 'E1',
        'employmentStatusId': 'ES_1',
        'income': 50000,
        'salaryPaymentMethodId': 'SLR',
      };

      final DOPNativeWorkingInfoEntity entity = DOPNativeWorkingInfoEntity.fromJson(json);

      expect(entity.employmentId, json['employmentId']);
      expect(entity.employmentStatusId, json['employmentStatusId']);
      expect(entity.income, json['income']);
      expect(entity.salaryPaymentMethodId, json['salaryPaymentMethodId']);
    });

    test('toJson should convert object to json correctly', () {
      const DOPNativeWorkingInfoEntity entity = DOPNativeWorkingInfoEntity(
        employmentId: 'E1',
        employmentStatusId: 'ES_1',
        income: 50000,
        salaryPaymentMethodId: 'SLR',
      );

      final Map<String, dynamic>? json = entity.toJson();

      expect(json!['employmentId'], 'E1');
      expect(json['employmentStatusId'], 'ES_1');
      expect(json['income'], 50000);
      expect(json['salaryPaymentMethodId'], 'SLR');
    });

    test('toJson ignore null field', () {
      const DOPNativeWorkingInfoEntity entity = DOPNativeWorkingInfoEntity(
        employmentId: 'E1',
      );

      final Map<String, dynamic>? json = entity.toJson();
      expect(json, isNot(contains('employmentStatusId')));
      expect(json, isNot(contains('income')));
      expect(json, isNot(contains('salaryPaymentMethodId')));
    });

    test('toJson return null if all field are null', () {
      const DOPNativeWorkingInfoEntity entity = DOPNativeWorkingInfoEntity();

      final Map<String, dynamic>? json = entity.toJson();
      expect(json, isNull);
    });
  });
}
