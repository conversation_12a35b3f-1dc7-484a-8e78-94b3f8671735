import 'package:evoapp/data/response/dop_native/dop_native_esign_prepare_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('DOPNativeESignPrepareEntity constants', () {
    expect(DOPNativeESignPrepareEntity.verdictInvalidState, 'invalid_state');
  });

  group('DOPNativeESignPrepareEntity', () {
    test('from<PERSON>son should properly construct entity from JSON', () {
      final Map<String, dynamic> jsonMap = <String, dynamic>{
        'data': <String, dynamic>{
          'retries': 3,
          'valid_seconds': 180,
        }
      };
      final BaseResponse baseResponse = BaseResponse(
        response: jsonMap,
        statusCode: CommonHttpClient.SUCCESS,
      );
      final DOPNativeESignPrepareEntity formDataEntity =
          DOPNativeESignPrepareEntity.fromBaseResponse(baseResponse);

      expect(formDataEntity.retries, 3);
      expect(formDataEntity.validSeconds, 180);
    });

    test('fromJson should handle null values for nested entities', () {
      final Map<String, dynamic> jsonMap = <String, dynamic>{
        'retries': null,
        'validSeconds': null,
      };
      final BaseResponse baseResponse = BaseResponse(
        response: jsonMap,
        statusCode: CommonHttpClient.SUCCESS,
      );
      final DOPNativeESignPrepareEntity formDataEntity =
          DOPNativeESignPrepareEntity.fromBaseResponse(baseResponse);

      expect(formDataEntity.retries, null);
      expect(formDataEntity.validSeconds, null);
    });

    test('unserializable constructor sets settings to null and correct exception code', () {
      final DOPNativeESignPrepareEntity entity = DOPNativeESignPrepareEntity.unserializable();

      expect(entity.retries, null);
      expect(entity.validSeconds, null);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });
}
