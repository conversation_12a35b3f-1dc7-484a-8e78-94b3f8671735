import 'package:evoapp/data/response/dop_native/dop_native_data_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeDataEntity', () {
    test('fromJson() should correctly parse JSON', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'phone_number': '123456789',
        'access_token': 'fake_access_token',
        'unique_token': 'fake_token',
        'additional_params_from_deep_link': <String, dynamic>{
          'utm_source': 'google',
          'utm_campaign': 'cpc'
        },
        'campaign_code': 'ABC123',
        'source': 'email',
      };

      final DOPNativeDataEntity entity = DOPNativeDataEntity.fromJson(json);

      expect(entity.phoneNumber, '123456789');
      expect(entity.accessToken, 'fake_access_token');
      expect(entity.uniqueToken, 'fake_token');
      expect(entity.additionalParamsFromDeepLink,
          <String, dynamic>{'utm_source': 'google', 'utm_campaign': 'cpc'});
      expect(entity.campaignCode, 'ABC123');
      expect(entity.source, 'email');
    });

    test('toJson() should correctly convert to JSON', () {
      final DOPNativeDataEntity entity = DOPNativeDataEntity(
        phoneNumber: '123456789',
        accessToken: 'fake_access_token',
        uniqueToken: 'fake_token',
        additionalParamsFromDeepLink: <String, dynamic>{
          'utm_source': 'google',
          'utm_campaign': 'cpc'
        },
        campaignCode: 'ABC123',
        source: 'email',
      );

      final Map<String, dynamic> json = entity.toJson()!;

      expect(json['phone_number'], '123456789');
      expect(json['access_token'], 'fake_access_token');
      expect(json['unique_token'], 'fake_token');
      expect(json['additional_params_from_deep_link'],
          <String, dynamic>{'utm_source': 'google', 'utm_campaign': 'cpc'});
      expect(json['campaign_code'], 'ABC123');
      expect(json['source'], 'email');
    });
  });
}
