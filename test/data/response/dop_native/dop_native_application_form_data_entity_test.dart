import 'package:evoapp/data/response/dop_native/dop_native_application_form_data_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_form_data_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeApplicationFormDataEntity', () {
    test('unserializable should create an entity with null fields and localExceptionCode set', () {
      final DOPNativeApplicationFormDataEntity entity =
          DOPNativeApplicationFormDataEntity.unserializable();

      expect(entity.formData, null);
      expect(entity.formStep, null);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('fromBaseResponse should properly construct entity from BaseResponse', () {
      final Map<String, dynamic> responseData = <String, dynamic>{
        'data': <String, dynamic>{
          'form_data': <String, dynamic>{
            'cardInfo': <String, dynamic>{'cardCategory': 'Debit'},
            'cifInfo': <String, dynamic>{'idCard': '1234567890', 'useNewCif': true},
            'personalInfo': null,
            'contactInfo': null,
            'salemanInfo': null
          },
          'form_step': 'step1',
        }
      };

      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseData,
      );

      final DOPNativeApplicationFormDataEntity entity =
          DOPNativeApplicationFormDataEntity.fromBaseResponse(baseResponse);

      expect(entity.formData?.cardInfo?.cardCategory, 'Debit');
      expect(entity.formStep, 'step1');

      // toJson
      final Map<String, dynamic> json = entity.toJson();

      expect(json, <String, dynamic>{
        'form_data': <String, dynamic>{
          'cardInfo': <String, dynamic>{'cardCategory': 'Debit'},
          'cifInfo': <String, dynamic>{'idCard': '1234567890', 'useNewCif': true},
          'personalInfo': null,
          'contactInfo': null,
          'referenceInfo': null,
          'salemanInfo': null
        },
        'form_step': 'step1',
      });
    });

    test('copyWith should return correctly', () {
      final DOPNativeApplicationFormDataEntity entity = DOPNativeApplicationFormDataEntity(
        formData: DOPNativeFormDataEntity(),
        formStep: 'form-step-1',
      );
      const String newFormStep = 'form-step-2';
      final DOPNativeApplicationFormDataEntity entity1 = entity.copyWith(formStep: newFormStep);

      expect(
        entity1.formStep,
        newFormStep,
      );
      expect(
        entity1.formData,
        entity.formData,
      );
    });
  });
}
