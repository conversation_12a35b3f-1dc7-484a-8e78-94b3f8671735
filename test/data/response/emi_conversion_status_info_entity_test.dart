import 'package:evoapp/data/response/emi_conversion_status_content_entity.dart';
import 'package:evoapp/data/response/emi_conversion_status_info_entity.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../util/flutter_test_config.dart';

void main() {
  group('EmiConversionStatusType', () {
    setUpAll(() {
      getItRegisterColor();
    });

    tearDownAll(() {
      getItUnregisterColor();
    });

    test('fromValue should return correct enum for valid values', () {
      expect(EmiConversionStatusType.fromValue('received'), EmiConversionStatusType.received);
      expect(
        EmiConversionStatusType.fromValue('auto_approved'),
        EmiConversionStatusType.autoApproved,
      );
      expect(
        EmiConversionStatusType.fromValue('manual_approved'),
        EmiConversionStatusType.manualApproved,
      );
      expect(EmiConversionStatusType.fromValue('rejected'), EmiConversionStatusType.rejected);
      expect(EmiConversionStatusType.fromValue('done'), EmiConversionStatusType.done);
    });

    test('fromValue should return unknown for invalid values', () {
      expect(EmiConversionStatusType.fromValue('invalid'), EmiConversionStatusType.unknown);
      expect(EmiConversionStatusType.fromValue(null), EmiConversionStatusType.unknown);
    });

    test('titleColor returns correct color', () {
      expect(
        EmiConversionStatusType.received.titleColor,
        evoColors.transactionHistoryEmiTitleReceived,
      );
      expect(
        EmiConversionStatusType.manualApproved.titleColor,
        evoColors.transactionHistoryEmiTitleApproved,
      );
      expect(
        EmiConversionStatusType.autoApproved.titleColor,
        evoColors.transactionHistoryEmiTitleApproved,
      );
      expect(
        EmiConversionStatusType.done.titleColor,
        evoColors.transactionHistoryEmiTitleDone,
      );
      expect(
        EmiConversionStatusType.rejected.titleColor,
        evoColors.transactionHistoryEmiTitleRejected,
      );
      expect(
        EmiConversionStatusType.unknown.titleColor,
        evoColors.transactionHistoryEmiTitleUnknown,
      );
    });

    test('statusColor returns correct color', () {
      expect(
        EmiConversionStatusType.received.statusColor,
        evoColors.transactionHistoryEmiStatusReceived,
      );
      expect(
        EmiConversionStatusType.manualApproved.statusColor,
        evoColors.transactionHistoryEmiStatusApproved,
      );
      expect(
        EmiConversionStatusType.autoApproved.statusColor,
        evoColors.transactionHistoryEmiStatusApproved,
      );
      expect(
        EmiConversionStatusType.done.statusColor,
        evoColors.transactionHistoryEmiStatusDone,
      );
      expect(
        EmiConversionStatusType.rejected.statusColor,
        evoColors.transactionHistoryEmiStatusRejected,
      );
      expect(
        EmiConversionStatusType.unknown.statusColor,
        evoColors.transactionHistoryEmiStatusUnknown,
      );
    });
  });

  group('EmiConversionStatusInfoEntity', () {
    const EmiConversionStatusType fakeStatusType = EmiConversionStatusType.autoApproved;
    final EmiInfoConversionStatusContentEntity fakeContent = EmiInfoConversionStatusContentEntity(
      title: 'fake_title',
      subTitle: 'fake_sub_title',
      userMessage: 'user_message',
    );
    test('fromJson should create EmiConversionStatusInfoEntity() from JSON', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'status': fakeStatusType.value,
        'content': fakeContent.toJson(),
      };

      final EmiConversionStatusInfoEntity entity = EmiConversionStatusInfoEntity.fromJson(json);
      expect(entity.status, isA<EmiConversionStatusType>());
      expect(entity.content, isA<EmiInfoConversionStatusContentEntity>());
    });

    test('toJson should return a JSON map containing the proper values', () {
      final EmiConversionStatusInfoEntity entity = EmiConversionStatusInfoEntity(
        content: fakeContent,
        status: fakeStatusType,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json['status'], fakeStatusType.value);
      expect(json['content'], fakeContent.toJson());
    });
  });
}
