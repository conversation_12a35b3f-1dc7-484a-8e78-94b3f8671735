import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/link_card_submission_status_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify status constant', () {
    expect(LinkCardSubmissionStatusEntity.statusProcessing, 'processing');
    expect(LinkCardSubmissionStatusEntity.statusFailed, 'failed');
    expect(LinkCardSubmissionStatusEntity.statusSucceeded, 'succeeded');
  });

  test('verify verdict constant', () {
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardFailure, 'link_card_failure');
    expect(LinkCardSubmissionStatusEntity.verdictDuplicatedLinkRequest, 'duplicated_link_request');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardInvalidParameters,
        'link_card_invalid_parameters');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardLinkRequestNotExists,
        'link_card_link_request_not_exists');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardLinkTimeout, 'link_card_link_timeout');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardInvalidBankCode,
        'link_card_invalid_bank_code');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardBankProductNotSupported,
        'link_card_bank_product_not_supported');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardAlreadyLinked, 'link_card_already_linked');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardInvalidIDNumber,
        'link_card_invalid_ic_number');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardInvalidPhoneNumber,
        'link_card_invalid_phone_number');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardNotFoundLinkInfo,
        'link_card_not_found_link_info');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardNotExists, 'link_card_not_exists');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardUnsupportedCard,
        'link_card_unsupported_card');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardTooManyValidCard,
        'link_card_too_many_valid_card');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardInvalidCardStatus,
        'link_card_invalid_card_status');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardOnboardingRequestFailed,
        'link_card_onboarding_request_failed');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardNotFoundOnboardingRequest,
        'link_card_not_found_onboarding_request');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardLinkRequestIsProcessing,
        'link_card_link_request_is_processing');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardPermissionDenied, 'permission_denied');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardRecordNotFound, 'record_not_found');
    expect(LinkCardSubmissionStatusEntity.verdictLinkCardFailureAll, 'failure');
  });

  group('LinkCardSubmissionStatusEntity Test', () {
    const String fakeType = 'fake_type';
    const String fakeRequestId = 'test_request_id';
    const String fakeTestStatus = 'test_status';
    const int fakeIntervalInquiryMs = 1000;
    const int fakeNextRetryInMinutes = 5;

    test('verify unserializable()', () {
      final LinkCardSubmissionStatusEntity entity = LinkCardSubmissionStatusEntity.unserializable();

      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(entity.action, null);
      expect(entity.intervalInquiryMs, null);
      expect(entity.linkCardRequestId, null);
      expect(entity.linkCardStatus, null);
      expect(entity.nextRetryIfExitDurationInMinute, null);
    });

    test('fromJson constructor should create a valid object', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'action': <String, dynamic>{
              'type': fakeType,
            },
            'interval_inquiry_ms': fakeIntervalInquiryMs,
            'link_card_request_id': fakeRequestId,
            'link_card_status': fakeTestStatus,
            'next_retry_if_exit_duration_in_minute': fakeNextRetryInMinutes,
          },
        },
      );

      final LinkCardSubmissionStatusEntity entity =
          LinkCardSubmissionStatusEntity.fromBaseResponse(baseResponse);

      expect(entity.action, isNotNull);
      expect(entity.intervalInquiryMs, fakeIntervalInquiryMs);
      expect(entity.linkCardRequestId, fakeRequestId);
      expect(entity.linkCardStatus, fakeTestStatus);
      expect(entity.nextRetryIfExitDurationInMinute, fakeNextRetryInMinutes);
    });

    test('toJson should return a JSON map containing the proper data', () {
      final LinkCardSubmissionStatusEntity entity = LinkCardSubmissionStatusEntity(
        action: ActionEntity(type: fakeType),
        intervalInquiryMs: fakeIntervalInquiryMs,
        linkCardRequestId: fakeRequestId,
        linkCardStatus: fakeTestStatus,
        nextRetryIfExitDurationInMinute: fakeNextRetryInMinutes,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json['action'], isNotNull);
      expect(json['interval_inquiry_ms'], fakeIntervalInquiryMs);
      expect(json['link_card_request_id'], fakeRequestId);
      expect(json['link_card_status'], fakeTestStatus);
      expect(json['next_retry_if_exit_duration_in_minute'], fakeNextRetryInMinutes);
    });
  });
}
