// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/data/response/order_extra_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('OrderSessionExtraInfoEntity', () {
    test('initialization with parameters', () {
      const bool isFirstTransaction = true;
      const bool autoApplyVoucher = true;

      final OrderExtraInfoEntity entity = OrderExtraInfoEntity(
        isFirstTransaction: isFirstTransaction,
        autoApplyVoucher: autoApplyVoucher,
      );

      expect(entity.isFirstTransaction, isFirstTransaction);
      expect(entity.autoApplyVoucher, autoApplyVoucher);
    });

    test('initialization with null parameters', () {
      final OrderExtraInfoEntity entity = OrderExtraInfoEntity();

      expect(entity.isFirstTransaction, isNull);
      expect(entity.autoApplyVoucher, isNull);
    });

    test('fromJson with all fields', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'is_first_transaction': true,
        'auto_apply_voucher': true,
      };

      final OrderExtraInfoEntity entity = OrderExtraInfoEntity.fromJson(json);

      expect(entity.isFirstTransaction, true);
      expect(entity.autoApplyVoucher, true);
    });

    test('fromJson with null fields', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'is_first_transaction': null,
        'auto_apply_voucher': null,
      };

      final OrderExtraInfoEntity entity = OrderExtraInfoEntity.fromJson(json);

      expect(entity.isFirstTransaction, isNull);
      expect(entity.autoApplyVoucher, isNull);
    });

    test('fromJson with missing fields', () {
      final Map<String, dynamic> json = <String, dynamic>{};

      final OrderExtraInfoEntity entity = OrderExtraInfoEntity.fromJson(json);

      expect(entity.isFirstTransaction, isNull);
      expect(entity.autoApplyVoucher, isNull);
    });

    test('toJson with all fields', () {
      final OrderExtraInfoEntity entity = OrderExtraInfoEntity(
        isFirstTransaction: true,
        autoApplyVoucher: true,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json['is_first_transaction'], true);
      expect(json['auto_apply_voucher'], true);
    });

    test('toJson with null fields', () {
      final OrderExtraInfoEntity entity = OrderExtraInfoEntity();

      final Map<String, dynamic> json = entity.toJson();

      expect(json['is_first_transaction'], isNull);
      expect(json['auto_apply_voucher'], isNull);
    });
  });
}
