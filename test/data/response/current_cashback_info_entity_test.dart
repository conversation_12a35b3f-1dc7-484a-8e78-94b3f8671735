import 'package:evoapp/data/response/current_cashback_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('should create an instance from JSON', () {
    final Map<String, dynamic> json = <String, dynamic>{
      'month': 2,
      'year': 2025,
      'amount': 100,
      'limit': 500,
    };

    final CurrentCashbackInfoEntity entity = CurrentCashbackInfoEntity.fromJson(json);

    expect(entity.month, 2);
    expect(entity.year, 2025);
    expect(entity.amount, 100);
    expect(entity.limit, 500);
  });

  test('should convert an instance to JSON', () {
    final CurrentCashbackInfoEntity entity = CurrentCashbackInfoEntity(
      month: 2,
      year: 2025,
      amount: 100,
      limit: 500,
    );

    final Map<String, dynamic> json = entity.toJson();

    expect(json['month'], 2);
    expect(json['year'], 2025);
    expect(json['amount'], 100);
    expect(json['limit'], 500);
  });
}
