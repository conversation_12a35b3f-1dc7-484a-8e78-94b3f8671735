import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/ekyc_info_entity.dart';
import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeAccessKey = 'test_access_key';
  const String fakeClientUrl = 'test_client_url';
  const String fakeExpiredAt = 'test_expired_at';
  const int fakeMaxAttempt = 3;
  const int fakeEkycCredentialRemainAttempt = 2;
  const String fakeSecretKey = 'test_secret_key';
  const String fakeSessionKey = 'test_session_token';
  const String fakeLenderRequestId = 'test_lender_request_id';
  const String fakeRequestId = 'test_request_id';

  final Map<String, dynamic> ekycCredential = <String, dynamic>{
    'access_key': fakeAccessKey,
    'ekyc_client_url': fakeClientUrl,
    'expired_at': fakeExpiredAt,
    'max_attempt': fakeMaxAttempt,
    'remain_attempt': fakeEkycCredentialRemainAttempt,
    'secret_key': fakeSecretKey,
    'x_lender_request_id': fakeLenderRequestId,
    'x_request_id': fakeRequestId,
  };

  test('verify constant', () {
    expect(SignInOtpEntity.verdictSuccess, 'success');
    expect(SignInOtpEntity.verdictIncorrectOtp, 'incorrect_otp');
    expect(SignInOtpEntity.verdictLimitExceeded, 'limit_exceeded');
    expect(SignInOtpEntity.verdictExpiredOTP, 'expired_data');
    expect(SignInOtpEntity.verdictUserNotExisted, 'record_not_found');
    expect(SignInOtpEntity.verdictInvalidDeviceToken, 'invalid_device_token');
    expect(SignInOtpEntity.verdictAlreadySignedIn, 'already_signed_in');
    expect(SignInOtpEntity.verdictCompleteWithOtherPhone, 'token_mismatch');
  });

  group('SignInOtpEntity', () {
    const String fakeAccessToken = 'sample_challenge_type';
    const String fakeRefreshToken = 'sample_refresh_token';
    const String fakeBiometricToken = 'sample_biometric_token';
    const String fakeDeviceToken = 'sample_device_token';
    const String fakeSessionToken = 'sample_session_token';
    const String fakeChallengeType = 'sample_challenge_type';
    const int fakeUserId = 1;
    const String fakeNotificationAuthKey = 'sample_notification_auth_key';
    const String fakeStatus = 'sample_status';
    const String fakeActionType = 'sample_action_type';
    const String fakeAuthNotifyToken = 'sample_auth_noti_token';
    const int fakeRemainAttempt = 10;

    test('unserializable', () {
      final SignInOtpEntity result = SignInOtpEntity.unserializable();

      expect(result.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('fromBaseResponse should return a valid SignInOtpEntity', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'access_token': fakeAccessToken,
            'refresh_token': fakeRefreshToken,
            'biometric_token': fakeBiometricToken,
            'device_token': fakeDeviceToken,
            'session_token': fakeSessionToken,
            'action': <String, dynamic>{
              'type': fakeActionType,
            },
            'challenge_type': fakeChallengeType,
            'user_id': fakeUserId,
            'notification_auth_key': fakeNotificationAuthKey,
            'status': fakeStatus,
            'ekyc_credential': ekycCredential,
            'auth_noti_token': fakeAuthNotifyToken,
            'remain_attempt': fakeRemainAttempt,
          },
        },
      );

      final SignInOtpEntity result = SignInOtpEntity.fromBaseResponse(baseResponse);

      expect(result.accessToken, fakeAccessToken);
      expect(result.refreshToken, fakeRefreshToken);
      expect(result.biometricToken, fakeBiometricToken);
      expect(result.deviceToken, fakeDeviceToken);
      expect(result.sessionToken, fakeSessionToken);
      expect(result.action?.type, fakeActionType);
      expect(result.challengeType, fakeChallengeType);
      expect(result.userId, fakeUserId);
      expect(result.notificationAuthKey, fakeNotificationAuthKey);
      expect(result.status, fakeStatus);
      expect(result.authNotiToken, fakeAuthNotifyToken);
      expect(result.remainAttempt, fakeRemainAttempt);
      expect(result.ekycCredential?.accessKey, fakeAccessKey);
      expect(result.ekycCredential?.eKYCClientUrl, fakeClientUrl);
      expect(result.ekycCredential?.expiredAt, fakeExpiredAt);
      expect(result.ekycCredential?.maxAttempt, fakeMaxAttempt);
      expect(result.ekycCredential?.remainAttempt, fakeEkycCredentialRemainAttempt);
      expect(result.ekycCredential?.secretKey, fakeSecretKey);
      expect(result.ekycCredential?.xLenderRequestId, fakeLenderRequestId);
      expect(result.ekycCredential?.xRequestId, fakeRequestId);
    });

    test('toJson should return a valid JSON representation', () {
      final SignInOtpEntity signInOtpEntity = SignInOtpEntity(
        accessToken: fakeAccessToken,
        refreshToken: fakeRefreshToken,
        biometricToken: fakeBiometricToken,
        deviceToken: fakeDeviceToken,
        sessionToken: fakeSessionToken,
        action: ActionEntity(type: fakeActionType),
        challengeType: fakeChallengeType,
        userId: fakeUserId,
        notificationAuthKey: fakeNotificationAuthKey,
        status: fakeStatus,
        authNotiToken: fakeAuthNotifyToken,
        remainAttempt: fakeRemainAttempt,
        ekycCredential: EKYCSessionEntity(
          accessKey: fakeAccessKey,
          eKYCClientUrl: fakeClientUrl,
          expiredAt: fakeExpiredAt,
          maxAttempt: fakeMaxAttempt,
          remainAttempt: fakeRemainAttempt,
          secretKey: fakeSecretKey,
          sessionToken: fakeSessionKey,
          xLenderRequestId: fakeLenderRequestId,
          xRequestId: fakeRequestId,
        ),
      );

      final Map<String, dynamic> json = signInOtpEntity.toJson();

      expect(json['access_token'], fakeAccessToken);
      expect(json['refresh_token'], fakeRefreshToken);
      expect(json['biometric_token'], fakeBiometricToken);
      expect(json['device_token'], fakeDeviceToken);
      expect(json['session_token'], fakeSessionToken);
      expect((json['action'] as ActionEntity).type, fakeActionType);
      expect(json['challenge_type'], fakeChallengeType);
      expect(json['user_id'], fakeUserId);
      expect(json['notification_auth_key'], fakeNotificationAuthKey);
      expect(json['status'], fakeStatus);
      expect(json['status'], fakeStatus);
      expect(json['ekyc_credential'], isNotNull);
      expect(json['auth_noti_token'], fakeAuthNotifyToken);
      expect(json['remain_attempt'], fakeRemainAttempt);
    });
  });

  test('SignInChallengeType should have correct string values', () {
    expect(SignInChallengeType.action.value, 'action');
    expect(SignInChallengeType.verifyPin.value, 'verify_pin');
    expect(SignInChallengeType.createPin.value, 'create_pin');
    expect(SignInChallengeType.faceOTP.value, 'face_otp');
    expect(SignInChallengeType.faceAuth.value, 'face_auth');
  });
}
