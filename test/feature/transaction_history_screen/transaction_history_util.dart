import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class TransactionHistoryUtil {
  static final TransactionHistoryUtil _instance = TransactionHistoryUtil._();

  static TransactionHistoryUtil get instance => _instance;

  TransactionHistoryUtil._();

  void initSetUpAll() {
    registerFallbackValue(BoxFit.contain);
    registerFallbackValue(BoxFit.fill);

    getItRegisterTextStyle();
    getItRegisterColor();

    getItRegisterMockCommonUtilFunctionAndImageProvider();
  }
}
