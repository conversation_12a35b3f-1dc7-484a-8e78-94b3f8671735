import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/card_status_entity.dart';
import 'package:evoapp/data/response/credit_limit_widget_config_entity.dart';
import 'package:evoapp/data/response/cta_widget_config_entity.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/cubit/card_status_cubit.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/cubit/card_status_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../constant.dart';
import '../../../../../util/test_util.dart';

class MockUserRepo extends Mock implements UserRepo {}

void main() {
  final UserRepo mockUserRepo = MockUserRepo();
  late CardStatusCubit cardStatusCubit;
  final AppState appState = AppState();

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(CardStatusEntity());
  });

  setUp(() {
    appState.cardStatus = null;
    cardStatusCubit = CardStatusCubit(appState, mockUserRepo);
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('Default state', () {
    expect(cardStatusCubit.state, isA<CardStatusInitState>());
  });

  group('verify loadCardStatus', () {
    blocTest<CardStatusCubit, CardStatusState>(
      'Load success with renovateStatus = false',
      build: () => cardStatusCubit,
      setUp: () {
        when(() => mockUserRepo.getCardStatus(
              renovateStatus: any(named: 'renovateStatus'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => CardStatusEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: await TestUtil.getResponseMock(
                  'get_card_status_credit_limit_widget_waiting_for_approval.json'),
            )));

        expect(appState.cardStatus, isNull);
      },
      act: (CardStatusCubit cubit) => cubit.loadCardStatus(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CardStatusLoadingState>(),
        isA<CardStatusLoadedState>()
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.display,
              'test display',
              true,
            )
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.creditLimitWidgetConfig?.display,
              'test creditLimitWidgetConfig.display',
              true,
            )
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.creditLimitWidgetConfig?.creditLimit,
              'test creditLimitWidgetConfig.creditLimit',
              75000000,
            )
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.ctaWidgetConfig?.display,
              'test ctaWidgetConfig.display',
              true,
            )
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.ctaWidgetConfig?.linkedCard,
              'test ctaWidgetConfig.linkedCard',
              false,
            )
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.ctaWidgetConfig?.activatedCard,
              'test ctaWidgetConfig.activatedCard',
              false,
            )
      ],
      verify: (_) {
        expect(
          appState.cardStatus,
          isA<CardStatusEntity>()
              .having(
                (CardStatusEntity p0) => p0.display,
                'test display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.display,
                'test creditLimitWidgetConfig.display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.creditLimit,
                'test creditLimitWidgetConfig.creditLimit',
                75000000,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.display,
                'test ctaWidgetConfig.display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.linkedCard,
                'test ctaWidgetConfig.linkedCard',
                false,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.activatedCard,
                'test ctaWidgetConfig.activatedCard',
                false,
              ),
        );
        verify(() => mockUserRepo.getCardStatus(
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<CardStatusCubit, CardStatusState>(
      'Load success with renovateStatus = true',
      build: () => cardStatusCubit,
      setUp: () {
        when(() => mockUserRepo.getCardStatus(
              renovateStatus: any(named: 'renovateStatus'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => CardStatusEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: await TestUtil.getResponseMock(
                  'get_card_status_credit_limit_widget_waiting_for_approval.json'),
            )));

        expect(appState.cardStatus, isNull);
      },
      act: (CardStatusCubit cubit) => cubit.loadCardStatus(renovateStatus: true),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CardStatusLoadingState>(),
        isA<CardStatusLoadedState>()
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.display,
              'test display',
              true,
            )
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.creditLimitWidgetConfig?.display,
              'test creditLimitWidgetConfig.display',
              true,
            )
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.creditLimitWidgetConfig?.creditLimit,
              'test creditLimitWidgetConfig.creditLimit',
              75000000,
            )
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.ctaWidgetConfig?.display,
              'test ctaWidgetConfig.display',
              true,
            )
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.ctaWidgetConfig?.linkedCard,
              'test ctaWidgetConfig.linkedCard',
              false,
            )
            .having(
              (CardStatusLoadedState p0) => p0.cardStatus?.ctaWidgetConfig?.activatedCard,
              'test ctaWidgetConfig.activatedCard',
              false,
            )
      ],
      verify: (_) {
        expect(
          appState.cardStatus,
          isA<CardStatusEntity>()
              .having(
                (CardStatusEntity p0) => p0.display,
                'test display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.display,
                'test creditLimitWidgetConfig.display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.creditLimit,
                'test creditLimitWidgetConfig.creditLimit',
                75000000,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.display,
                'test ctaWidgetConfig.display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.linkedCard,
                'test ctaWidgetConfig.linkedCard',
                false,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.activatedCard,
                'test ctaWidgetConfig.activatedCard',
                false,
              ),
        );
        verify(() => mockUserRepo.getCardStatus(
              renovateStatus: true,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<CardStatusCubit, CardStatusState>(
      'Call API success with verdict = limit_exceeded',
      build: () => cardStatusCubit,
      setUp: () {
        when(() => mockUserRepo.getCardStatus(
              renovateStatus: any(named: 'renovateStatus'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => CardStatusEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: await TestUtil.getResponseMock(
                  'get_card_status_verdict_renovate_status_limit_exceeded.json'),
            )));

        expect(appState.cardStatus, isNull);
      },
      act: (CardStatusCubit cubit) => cubit.loadCardStatus(renovateStatus: true),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CardStatusLoadingState>(),
        isA<CardStatusDOPRenovateErrorState>()
            .having(
              (CardStatusDOPRenovateErrorState p0) => p0.cardStatus?.display,
              'test display',
              true,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) =>
                  p0.cardStatus?.creditLimitWidgetConfig?.display,
              'test creditLimitWidgetConfig.display',
              true,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) =>
                  p0.cardStatus?.creditLimitWidgetConfig?.creditLimit,
              'test creditLimitWidgetConfig.creditLimit',
              75000000,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) => p0.cardStatus?.ctaWidgetConfig?.display,
              'test ctaWidgetConfig.display',
              true,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) => p0.cardStatus?.ctaWidgetConfig?.linkedCard,
              'test ctaWidgetConfig.linkedCard',
              true,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) => p0.cardStatus?.ctaWidgetConfig?.activatedCard,
              'test ctaWidgetConfig.activatedCard',
              false,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) => p0.errorUiModel?.userMessage,
              'test userMessage',
              'Chưa thể cập nhật thông tin của bạn - renovate_status_limit_exceeded',
            )
      ],
      verify: (_) {
        expect(
          appState.cardStatus,
          isA<CardStatusEntity>()
              .having(
                (CardStatusEntity p0) => p0.display,
                'test display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.display,
                'test creditLimitWidgetConfig.display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.creditLimit,
                'test creditLimitWidgetConfig.creditLimit',
                75000000,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.display,
                'test ctaWidgetConfig.display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.linkedCard,
                'test ctaWidgetConfig.linkedCard',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.activatedCard,
                'test ctaWidgetConfig.activatedCard',
                false,
              ),
        );
        verify(() => mockUserRepo.getCardStatus(
              renovateStatus: true,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<CardStatusCubit, CardStatusState>(
      'Call API success with verdict = renovate_status_failed',
      build: () => cardStatusCubit,
      setUp: () {
        when(() => mockUserRepo.getCardStatus(
              renovateStatus: any(named: 'renovateStatus'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => CardStatusEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: await TestUtil.getResponseMock(
                  'get_card_status_verdict_renovate_status_failed.json'),
            )));

        expect(appState.cardStatus, isNull);
      },
      act: (CardStatusCubit cubit) => cubit.loadCardStatus(renovateStatus: true),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CardStatusLoadingState>(),
        isA<CardStatusDOPRenovateErrorState>()
            .having(
              (CardStatusDOPRenovateErrorState p0) => p0.cardStatus?.display,
              'test display',
              true,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) =>
                  p0.cardStatus?.creditLimitWidgetConfig?.display,
              'test creditLimitWidgetConfig.display',
              true,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) =>
                  p0.cardStatus?.creditLimitWidgetConfig?.creditLimit,
              'test creditLimitWidgetConfig.creditLimit',
              75000000,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) => p0.cardStatus?.ctaWidgetConfig?.display,
              'test ctaWidgetConfig.display',
              true,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) => p0.cardStatus?.ctaWidgetConfig?.linkedCard,
              'test ctaWidgetConfig.linkedCard',
              true,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) => p0.cardStatus?.ctaWidgetConfig?.activatedCard,
              'test ctaWidgetConfig.activatedCard',
              false,
            )
            .having(
              (CardStatusDOPRenovateErrorState p0) => p0.errorUiModel?.userMessage,
              'test userMessage',
              'Chưa thể cập nhật thông tin của bạn - renovate_status_failed',
            )
      ],
      verify: (_) {
        expect(
          appState.cardStatus,
          isA<CardStatusEntity>()
              .having(
                (CardStatusEntity p0) => p0.display,
                'test display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.display,
                'test creditLimitWidgetConfig.display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.creditLimit,
                'test creditLimitWidgetConfig.creditLimit',
                75000000,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.display,
                'test ctaWidgetConfig.display',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.linkedCard,
                'test ctaWidgetConfig.linkedCard',
                true,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.activatedCard,
                'test ctaWidgetConfig.activatedCard',
                false,
              ),
        );
        verify(() => mockUserRepo.getCardStatus(
              renovateStatus: true,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<CardStatusCubit, CardStatusState>(
      'Load error without cached data',
      build: () => cardStatusCubit,
      setUp: () {
        when(() => mockUserRepo.getCardStatus(
              renovateStatus: any(named: 'renovateStatus'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => CardStatusEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: null,
            )));
        expect(appState.cardStatus, null);
      },
      act: (CardStatusCubit cubit) => cubit.loadCardStatus(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CardStatusLoadingState>(),
        isA<CardStatusErrorState>()
            .having(
              (CardStatusErrorState p0) => p0.errorUiModel?.statusCode,
              'test errorUiModel',
              CommonHttpClient.BAD_REQUEST,
            )
            .having(
              (CardStatusErrorState p0) => p0.cachedCardStatus,
              'test cardStatus',
              isNull,
            )
      ],
      verify: (_) {
        expect(appState.cardStatus, isNull);
        verify(() => mockUserRepo.getCardStatus(
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<CardStatusCubit, CardStatusState>(
      'Load error with cached data',
      build: () => cardStatusCubit,
      setUp: () {
        when(() => mockUserRepo.getCardStatus(
              renovateStatus: any(named: 'renovateStatus'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => CardStatusEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: null,
            )));

        appState.cardStatus = CardStatusEntity(
          creditLimitWidgetConfig: CreditLimitWidgetConfigEntity(
            display: false,
            creditLimit: 75000000,
            creditStatus: 'waiting_for_approval',
          ),
          ctaWidgetConfig: CtaWidgetConfigEntity(
            display: false,
            linkedCard: false,
            activatedCard: false,
          ),
          display: false,
        );
      },
      act: (CardStatusCubit cubit) => cubit.loadCardStatus(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CardStatusLoadingState>(),
        isA<CardStatusErrorState>()
            .having(
              (CardStatusErrorState p0) => p0.errorUiModel?.statusCode,
              'test errorUiModel',
              CommonHttpClient.BAD_REQUEST,
            )
            .having(
              (CardStatusErrorState p0) => p0.cachedCardStatus,
              'test cachedCardStatus',
              isA<CardStatusEntity>()
                  .having(
                    (CardStatusEntity p0) => p0.display,
                    'test display',
                    false,
                  )
                  .having(
                    (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.display,
                    'test creditLimitWidgetConfig.display',
                    false,
                  )
                  .having(
                    (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.creditLimit,
                    'test creditLimitWidgetConfig.creditLimit',
                    75000000,
                  )
                  .having(
                    (CardStatusEntity p0) => p0.ctaWidgetConfig?.display,
                    'test ctaWidgetConfig.display',
                    false,
                  )
                  .having(
                    (CardStatusEntity p0) => p0.ctaWidgetConfig?.linkedCard,
                    'test ctaWidgetConfig.linkedCard',
                    false,
                  )
                  .having(
                    (CardStatusEntity p0) => p0.ctaWidgetConfig?.activatedCard,
                    'test ctaWidgetConfig.activatedCard',
                    false,
                  ),
            ),
      ],
      verify: (_) {
        expect(
          appState.cardStatus,
          isA<CardStatusEntity>()
              .having(
                (CardStatusEntity p0) => p0.display,
                'test display',
                false,
              )
              .having(
                (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.display,
                'test creditLimitWidgetConfig.display',
                false,
              )
              .having(
                (CardStatusEntity p0) => p0.creditLimitWidgetConfig?.creditLimit,
                'test creditLimitWidgetConfig.creditLimit',
                75000000,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.display,
                'test ctaWidgetConfig.display',
                false,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.linkedCard,
                'test ctaWidgetConfig.linkedCard',
                false,
              )
              .having(
                (CardStatusEntity p0) => p0.ctaWidgetConfig?.activatedCard,
                'test ctaWidgetConfig.activatedCard',
                false,
              ),
        );
        verify(() => mockUserRepo.getCardStatus(
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    test(
        'Call loadCardStatus without passing renovateStatus, should call repo.getCardStatus with renovateStatus = false',
        () {
      when(() => mockUserRepo.getCardStatus(
            renovateStatus: any(named: 'renovateStatus'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => CardStatusEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(
                'get_card_status_credit_limit_widget_waiting_for_approval.json'),
          )));

      cardStatusCubit.loadCardStatus();
      expect(
        verify(() => mockUserRepo.getCardStatus(
              renovateStatus: captureAny(named: 'renovateStatus'),
              mockConfig: any(named: 'mockConfig'),
            )).captured.single,
        false,
      );
    });

    test(
        'Call loadCardStatus with renovateStatus = true, should call repo.getCardStatus with renovateStatus = true',
        () {
      when(() => mockUserRepo.getCardStatus(
            renovateStatus: any(named: 'renovateStatus'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => CardStatusEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(
                'get_card_status_credit_limit_widget_waiting_for_approval.json'),
          )));

      cardStatusCubit.loadCardStatus(renovateStatus: true);
      expect(
        verify(() => mockUserRepo.getCardStatus(
              renovateStatus: captureAny(named: 'renovateStatus'),
              mockConfig: any(named: 'mockConfig'),
            )).captured.single,
        true,
      );
    });
  });
}
