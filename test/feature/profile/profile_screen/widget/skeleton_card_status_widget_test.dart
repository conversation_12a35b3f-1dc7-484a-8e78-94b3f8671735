import 'package:evoapp/feature/profile/profile_screen/card_status/widget/skeleton_card_status_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/shimmer/skeleton_container.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SkeletonCardStatusWidget Tests', () {
    testWidgets('renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SingleChildScrollView(
              child: SkeletonCardStatusWidget(),
            ),
          ),
        ),
      );

      expect(
        find.byWidgetPredicate(
          (Widget widget) => widget is SkeletonContainer && widget.height == 20.0,
        ),
        findsNWidgets(2),
      );

      expect(
        find.byWidgetPredicate(
          (Widget widget) => widget is SkeletonContainer && widget.height == 32.0,
        ),
        findsOneWidget,
      );

      expect(
        find.byWidgetPredicate(
          (Widget widget) => widget is SkeletonContainer && widget.height == 48.0,
        ),
        findsNWidgets(2),
      );

      expect(
        find.byWidgetPredicate(
          (Widget widget) => widget is SizedBox && widget.height == 20.0,
        ),
        findsNWidgets(3),
      );

      expect(
        find.byWidgetPredicate(
          (Widget widget) => widget is SizedBox && widget.height == 8.0,
        ),
        findsOneWidget,
      );

      expect(
        find.byWidgetPredicate(
          (Widget widget) => widget is SizedBox && widget.height == 16.0,
        ),
        findsOneWidget,
      );
    });
  });
}
