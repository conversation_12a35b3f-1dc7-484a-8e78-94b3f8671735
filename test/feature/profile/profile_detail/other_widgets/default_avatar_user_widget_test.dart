import 'package:evoapp/feature/profile/model/gender.dart';
import 'package:evoapp/feature/profile/profile_detail_screen/other_widgets/default_avatar_user_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  group('DefaultAvatarUserWidget', () {
    testWidgets('displays male avatar when gender is male', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: DefaultAvatarUserWidget(gender: Gender.male),
        ),
      );

      verify(() => mockCommonImageProvider.asset(
            EvoImages.imgMaleAvatar,
            cornerRadius: 60,
            width: 96,
            height: 96,
          )).called(1);
    });

    testWidgets('displays female avatar when gender is female', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: DefaultAvatarUserWidget(gender: Gender.female),
        ),
      );

      verify(() => mockCommonImageProvider.asset(
            EvoImages.imgFemaleAvatar,
            cornerRadius: 60,
            width: 96,
            height: 96,
          )).called(1);
    });

    testWidgets('displays default avatar when gender is null', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: DefaultAvatarUserWidget(),
        ),
      );

      verify(() => mockCommonImageProvider.asset(
            EvoImages.icDefaultAvatar,
            cornerRadius: 60,
            width: 96,
            height: 96,
          )).called(1);
    });

    testWidgets('applies cornerRadius and sizeAvatar correctly', (WidgetTester tester) async {
      const double testCornerRadius = 50;
      const double testSizeAvatar = 80;

      await tester.pumpWidget(
        MaterialApp(
          home: DefaultAvatarUserWidget(
            gender: Gender.male,
            cornerRadius: testCornerRadius,
            sizeAvatar: testSizeAvatar,
          ),
        ),
      );

      verify(() => mockCommonImageProvider.asset(
            any(),
            cornerRadius: testCornerRadius,
            width: testSizeAvatar,
            height: testSizeAvatar,
          )).called(1);
    });
  });
}
