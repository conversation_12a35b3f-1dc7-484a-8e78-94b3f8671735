import 'package:evoapp/feature/profile/model/gender.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('male code should return M', () {
    expect(Gender.male.code, equals(maleInString));
  });

  test('female code should return F', () {
    expect(Gender.female.code, equals(femaleInString));
  });

  test('should return Gender.male when input is M', () {
    expect(Gender.formatGenderString(maleInString), equals(Gender.male));
  });

  test('should return Gender.female when input is F', () {
    expect(Gender.formatGenderString(femaleInString), equals(Gender.female));
  });

  test('should return null when input is null', () {
    expect(Gender.formatGenderString(null), isNull);
  });

  test('should return null when input is invalid string', () {
    expect(Gender.formatGenderString('invalid'), isNull);
  });
}
