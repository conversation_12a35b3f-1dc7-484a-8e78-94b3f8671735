import 'package:evoapp/feature/appsflyer/appsflyer_handler.dart';
import 'package:evoapp/feature/appsflyer/one_link_utils.dart';
import 'package:evoapp/feature/deep_link/deep_link_utils.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/webview/models/evo_webview_arg.dart';
import 'package:evoapp/feature/webview/utils/webview_util.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/model/evo_action_model.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/web_link_utils.dart';
import 'package:evoapp/widget/evo_appbar.dart';
import 'package:evoapp/widget/evo_appbar_leading_button.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo_impl.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/feature/webview/common_webview_controller.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/ui_model/action_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/permission/permission_handler_mixin.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';
import '../../delete_account/attention_notes/widget/attention_notes_content_test.dart';

class MockCommonWebViewController extends Mock implements CommonWebViewController {}

class MockCommonHttpClient extends Mock implements CommonHttpClient {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class TestWebViewArg extends PageBaseArg {}

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

class MockDataCollector extends Mock implements DataCollector {}

class MockFirebaseAnalytics extends Mock implements FirebaseAnalytics {}

class MockFirebaseAnalyticsObserver extends Mock implements FirebaseAnalyticsObserver {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

class MockAppsflyerHandler extends Mock implements AppsflyerHandler {}

class MockDeepLinkUtils extends Mock implements DeepLinkUtils {}

class MockOneLinkUtils extends Mock implements OneLinkUtils {}

class MockPermissionHandlerMixin extends Mock with PermissionHandlerMixin {}

class TestWebViewUtil extends WebViewUtil {
  bool isCameraPermissionGranted = true;

  @override
  Future<bool> checkCameraPermission() async {
    return isCameraPermissionGranted;
  }
}

void main() {
  late CommonImageProvider commonImageProvider;
  late CommonHttpClient mockCommonHttpClient;
  late CommonWebViewController mockController;
  late EvoSnackBar mockEvoSnackBar;
  late EvoFlutterWrapper mockEvoFlutterWrapper;
  late DOPUtilFunctions mockDOPUtilFunctions;
  late AppsflyerHandler mockAppsflyerHandler;
  late DataCollector mockDataCollector;
  const String fakeType = 'open_web_view';
  const String fakeLink = 'fake_link';
  const String fakeTitle = 'fake_title';
  const String fakeDopLink = 'https://staging-tpbank.avay.vn/abc?def';

  late TestWebViewUtil webViewUtil;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(SnackBarType.error);
    registerFallbackValue(SnackBarDuration.short.value);
    registerFallbackValue(EvoDialogId.confirmToCancelDOPJourneyDialog);

    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getIt.registerLazySingleton<WebLinkUtils>(() => WebLinkUtils());
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());

    getIt.registerLazySingleton<EvoFlutterWrapper>(() => MockEvoFlutterWrapper());
    mockEvoFlutterWrapper = getIt.get<EvoFlutterWrapper>();

    getIt.registerLazySingleton<CommonHttpClient>(() => MockCommonHttpClient());
    mockCommonHttpClient = getIt.get<CommonHttpClient>();

    getIt.registerLazySingleton<LoggingRepo>(
      () => LoggingRepoImpl(commonHttpClient: getIt.get<CommonHttpClient>()),
    );

    getIt.registerLazySingleton<EvoSnackBar>(() => MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();

    commonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerLazySingleton<DOPUtilFunctions>(() => MockDOPUtilFunctions());
    mockDOPUtilFunctions = getIt.get<DOPUtilFunctions>();

    setupAppsflyerForTest();

    mockAppsflyerHandler = getIt.get<AppsflyerHandler>();

    getIt.registerLazySingleton<DataCollector>(() => MockDataCollector());
    mockDataCollector = getIt.get<DataCollector>();

    setUpOneLinkDeepLinkRegExForTest();

    when(() => mockCommonHttpClient.post(any(), data: any(named: 'data')))
        .thenAnswer((_) async => Future<BaseResponse>.value(BaseResponse(
              response: <String, dynamic>{},
              statusCode: 200,
            )));

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => mockEvoSnackBar.show(any(),
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInSec: any(named: 'durationInSec')))
        .thenAnswer((_) async => Future<bool?>.value());
  });

  setUp(() {
    getIt.registerLazySingleton<GlobalKeyProvider>(() => GlobalKeyProvider());
    webViewUtil = TestWebViewUtil();
    setUtilsMockInstanceForTesting();
    when(() => EvoDialogHelper().showDialogConfirm(
          title: any(named: 'title'),
          content: any(named: 'content'),
          textPositive: any(named: 'textPositive'),
          textNegative: any(named: 'textNegative'),
          dialogId: any(named: 'dialogId'),
          positiveButtonStyle: any(named: 'positiveButtonStyle'),
          negativeButtonStyle: any(named: 'negativeButtonStyle'),
          onClickPositive: any(named: 'onClickPositive'),
          onClickNegative: any(named: 'onClickNegative'),
        )).thenAnswer((_) async => Future<void>.value());

    mockController = MockCommonWebViewController();

    when(() => mockController.canGoBack).thenAnswer((_) => () {
          return Future<bool>.value(true);
        });

    when(() => mockController.goBack).thenAnswer((_) => () {
          return Future<void>.value();
        });

    when(() => mockController.hideKeyboard).thenAnswer((_) => () async {
          return Future<void>.value();
        });

    when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);
    when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
  });

  tearDown(() {
    getIt.unregister<GlobalKeyProvider>();
    resetUtilMockToOriginalInstance();
    reset(mockController);
  });

  group('verify processPageBaseArg()', () {
    FlavorConfig(
      flavor: FlavorType.stag.name,
      values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
    );

    final EvoActionModel actionModel = EvoActionModel(
      type: fakeType,
      args: EvoArgs(
        link: fakeLink,
      ),
    );

    test('calls createEvoWebViewArg with empty title when arg is null', () async {
      final PageBaseArg? result = await webViewUtil.processPageBaseArg(action: actionModel);
      expect(result, isA<EvoWebViewArg>());
      final EvoWebViewArg evoWebViewArg = result as EvoWebViewArg;
      expect(evoWebViewArg.title, '');
      expect(evoWebViewArg.nextActionWidget, null);
    });

    test('returns null when arg is not of type EvoWebViewArg', () async {
      final PageBaseArg nonEvoArg = TestWebViewArg();

      final PageBaseArg? result =
          await webViewUtil.processPageBaseArg(action: actionModel, arg: nonEvoArg);

      expect(result, isA<TestWebViewArg>());
    });

    test('calls createEvoWebViewArg with properties from arg when arg is EvoWebViewArg', () async {
      final EvoWebViewArg evoArg = EvoWebViewArg(title: fakeTitle, url: fakeLink);

      final PageBaseArg? result =
          await webViewUtil.processPageBaseArg(action: actionModel, arg: evoArg);

      expect(result, isA<EvoWebViewArg>());
      final EvoWebViewArg evoWebViewArg = result as EvoWebViewArg;
      expect(evoWebViewArg.title, evoArg.title);
      expect(evoWebViewArg.nextActionWidget, null);
    });
  });

  group('verify createEvoWebViewArg()', () {
    setUpAll(() {
      final MockFirebaseAnalytics mockFirebaseAnalytics = MockFirebaseAnalytics();
      final MockFirebaseAnalyticsObserver mockFirebaseAnalyticsObserver =
          MockFirebaseAnalyticsObserver();
      getIt.registerLazySingleton<FirebaseAnalyticsWrapper>(
          () => FirebaseAnalyticsWrapper(mockFirebaseAnalytics, mockFirebaseAnalyticsObserver));

      when(() => mockDataCollector.getDeviceId())
          .thenAnswer((_) => Future<String>.value('mockDeviceId'));
      when(() => mockFirebaseAnalytics.logEvent(
          name: any(named: 'name'),
          parameters: any(named: 'parameters'))).thenAnswer((_) => Future<void>.value());

      when(() => mockDataCollector.getFirebaseAppInstanceId())
          .thenAnswer((_) => Future<String>.value('mockAppInstanceId'));
      when(() => mockDOPUtilFunctions.getDevicePlatform()).thenReturn('android');
      when(() => mockAppsflyerHandler.getAppsflyerId())
          .thenAnswer((_) => Future<String>.value('appsflyerAppID'));

      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );
    });

    tearDownAll(() {
      getIt.unregister<FirebaseAnalyticsWrapper>();
    });

    test('creates an EvoWebViewArg with an AppBar, of which url is not a DOP link', () async {
      const Widget nextActionWidget = SizedBox.shrink();
      final EvoActionModel actionModel = EvoActionModel(
        type: fakeType,
        args: EvoArgs(
          link: fakeLink,
        ),
      );

      final EvoWebViewArg? result = await webViewUtil.createEvoWebViewArg(
        action: actionModel,
        title: fakeTitle,
        nextActionWidget: nextActionWidget,
      );

      expect(result?.title, fakeTitle);
      expect(result?.url, fakeLink);
      expect(result?.nextActionWidget, nextActionWidget);
      expect(result?.onHandlePopScope, null);
      expect(result?.appBar, isA<EvoAppBar>());

      final EvoAppBar appBar = result?.appBar as EvoAppBar;
      expect(appBar.title, isA<Text>());

      final Text titleAppBar = appBar.title as Text;
      expect(titleAppBar.data, fakeTitle);
    });

    test('creates an EvoWebViewArg with an AppBar has DOP link and camera permission is granted',
        () async {
      final EvoActionModel actionModel = EvoActionModel(
        type: fakeType,
        args: EvoArgs(
          link: fakeDopLink,
        ),
      );

      final EvoWebViewArg? result = await webViewUtil.createEvoWebViewArg(
        action: actionModel,
        title: fakeTitle,
      );

      final String url = await webViewUtil.addingDopAdditionalQueryParams(fakeDopLink);
      result?.onHandlePopScope?.call();
      expect(result?.title, fakeTitle);
      expect(result?.url, url);
      expect(result?.onHandlePopScope, isNotNull);
      expect(result?.appBar, isA<EvoAppBar>());

      final EvoAppBar appBar = result?.appBar as EvoAppBar;
      expect(appBar.title, isA<Text>());

      final Text titleAppBar = appBar.title as Text;
      expect(titleAppBar.data, EvoStrings.webViewDOPJourneyTitle);
    });

    test(
        'should not creates an EvoWebViewArg with an AppBar has DOP link and camera permission is not granted on Android',
        () async {
      final EvoActionModel actionModel = EvoActionModel(
        type: fakeType,
        args: EvoArgs(
          link: fakeDopLink,
        ),
      );

      webViewUtil.isCameraPermissionGranted = false;
      final EvoWebViewArg? result = await webViewUtil.createEvoWebViewArg(
        action: actionModel,
        title: fakeTitle,
      );

      expect(result, null);
      verify(() => mockEvoSnackBar.show(
            EvoStrings.webViewDOPWithErrorCameraPermission,
            typeSnackBar: SnackBarType.error,
            durationInSec: SnackBarDuration.short.value,
          )).called(1);
    });

    test('creates an EvoWebViewArg with an AppBar has DOP link on iOS and camera is not granted',
        () async {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);

      final EvoActionModel actionModel = EvoActionModel(
        type: fakeType,
        args: EvoArgs(
          link: fakeDopLink,
        ),
      );

      webViewUtil.isCameraPermissionGranted = false;
      final EvoWebViewArg? result = await webViewUtil.createEvoWebViewArg(
        action: actionModel,
        title: fakeTitle,
      );

      expect(result?.title, fakeTitle);
      expect(result?.url, await webViewUtil.addingDopAdditionalQueryParams(fakeDopLink));
      verify(() => mockEvoSnackBar.show(
            EvoStrings.webViewDOPWithErrorCameraPermission,
            typeSnackBar: SnackBarType.error,
            durationInSec: SnackBarDuration.short.value,
          )).called(1);
    });
  });

  group('verify isHandleDopLinkInAppWebView()', () {
    test(
        'verify isHandleDopLinkInAppWebView() with action is not null & type is not openInAppWebView',
        () {
      final EvoActionModel actionModel = EvoActionModel(
        type: fakeType,
        args: EvoArgs(
          link: fakeLink,
        ),
      );

      final bool result = webViewUtil.isHandleDopLinkInAppWebView(actionModel);
      expect(result, false);
    });

    test(
        'verify isHandleDopLinkInAppWebView() with action is not null & type is openInAppWebView & link is not DOP link',
        () {
      final EvoActionModel actionModel = EvoActionModel(
        type: ActionModel.openInAppWebView,
        args: EvoArgs(
          link: fakeLink,
        ),
      );

      final bool result = webViewUtil.isHandleDopLinkInAppWebView(actionModel);
      expect(result, false);
    });

    test(
        'verify isHandleDopLinkInAppWebView() with action is not null & type is openInAppWebView & link is DOP link',
        () {
      final EvoActionModel actionModel = EvoActionModel(
        type: ActionModel.openInAppWebView,
        args: EvoArgs(
          link: fakeDopLink,
        ),
      );

      final bool result = webViewUtil.isHandleDopLinkInAppWebView(actionModel);
      expect(result, true);
    });
  });

  group('verify createAppBarForDOPWebView()', () {
    testWidgets('createAppBarForDOPWebView() with right properties',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: webViewUtil.createAppBarForDOPWebView(null),
            body: const SizedBox.shrink(),
          ),
        ),
      );

      final Finder appBarFinder = find.byType(EvoAppBar);
      expect(appBarFinder, findsOneWidget);

      // verify title
      final Finder titleFinder = find.text(EvoStrings.webViewDOPJourneyTitle);
      expect(titleFinder, findsOneWidget);

      // verify close button
      final Finder closeButtonFinder = find.byType(CloseButton);
      expect(closeButtonFinder, findsOneWidget);

      // verify leading button
      final Finder leadingButtonFinder = find.byType(EvoAppBarLeadingButton);
      expect(leadingButtonFinder, findsOneWidget);
    });

    testWidgets('createAppBarForDOPWebView(), click close button',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: webViewUtil.createAppBarForDOPWebView(null),
            body: const SizedBox.shrink(),
          ),
        ),
      );

      // verify close button
      final Finder closeButtonFinder = find.byType(CloseButton);
      expect(closeButtonFinder, findsOneWidget);

      // click close button
      await widgetTester.tap(closeButtonFinder);

      // verify hideKeyboard()
      verifyNever(() => mockController.hideKeyboard?.call());

      // verify showDialogConfirm() is called
      verify(() => EvoDialogHelper().showDialogConfirm(
            title: EvoStrings.cancelDOPJourneyTitle,
            content: EvoStrings.cancelDOPJourneyDescription,
            textPositive: EvoStrings.continueBtn,
            textNegative: EvoStrings.agree,
            dialogId: EvoDialogId.confirmToCancelDOPJourneyDialog,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });

    testWidgets(
        'createAppBarForDOPWebView(), click leading button with controller is not null & canGoBack is true',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: webViewUtil.createAppBarForDOPWebView(mockController),
            body: const SizedBox.shrink(),
          ),
        ),
      );

      // verify leading button
      final Finder leadingButtonFinder = find.byType(EvoAppBarLeadingButton);
      expect(leadingButtonFinder, findsOneWidget);

      // click leading button
      await widgetTester.tap(leadingButtonFinder);

      // verify hideKeyboard()
      verifyNever(() => mockController.hideKeyboard?.call());

      // verify handleBackInAppWebView()
      verify(() => mockController.canGoBack?.call()).called(1);
      verify(() => mockController.goBack?.call()).called(1);

      verifyNever(() => EvoDialogHelper().showDialogConfirm(
            title: any(named: 'title'),
            content: any(named: 'content'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            dialogId: any(named: 'dialogId'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          ));
    });

    testWidgets(
        'createAppBarForDOPWebView(), click leading button with controller is not null & canGoBack is false',
        (WidgetTester widgetTester) async {
      when(() => mockController.canGoBack).thenAnswer((_) => () {
            return Future<bool>.value(false);
          });

      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: webViewUtil.createAppBarForDOPWebView(mockController),
            body: const SizedBox.shrink(),
          ),
        ),
      );

      // verify leading button
      final Finder leadingButtonFinder = find.byType(EvoAppBarLeadingButton);
      expect(leadingButtonFinder, findsOneWidget);

      // click leading button
      await widgetTester.tap(leadingButtonFinder);

      // verify hideKeyboard()
      verify(() => mockController.hideKeyboard?.call()).called(1);

      // verify handleBackInAppWebView()
      verify(() => mockController.canGoBack?.call()).called(1);
      verifyNever(() => mockController.goBack?.call());

      verify(() => EvoDialogHelper().showDialogConfirm(
            title: EvoStrings.cancelDOPJourneyTitle,
            content: EvoStrings.cancelDOPJourneyDescription,
            textPositive: EvoStrings.continueBtn,
            textNegative: EvoStrings.agree,
            dialogId: EvoDialogId.confirmToCancelDOPJourneyDialog,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });

    testWidgets('createAppBarForDOPWebView(), click leading button with controller is null',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: webViewUtil.createAppBarForDOPWebView(null),
            body: const SizedBox.shrink(),
          ),
        ),
      );

      // verify leading button
      final Finder leadingButtonFinder = find.byType(EvoAppBarLeadingButton);
      expect(leadingButtonFinder, findsOneWidget);

      // click leading button
      await widgetTester.tap(leadingButtonFinder);

      // verify hideKeyboard()
      verifyNever(() => mockController.hideKeyboard?.call());

      // verify handleBackInAppWebView()
      verifyNever(() => mockController.canGoBack?.call());
      verifyNever(() => mockController.goBack?.call());

      verify(() => EvoDialogHelper().showDialogConfirm(
            title: EvoStrings.cancelDOPJourneyTitle,
            content: EvoStrings.cancelDOPJourneyDescription,
            textPositive: EvoStrings.continueBtn,
            textNegative: EvoStrings.agree,
            dialogId: EvoDialogId.confirmToCancelDOPJourneyDialog,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });
  });

  group('verify checkDopLink()', () {
    test('addingDopAdditionalQueryParams add additional params to query params', () async {
      const String expectedDeviceId = 'mockDeviceId';
      const String expectedAppInstanceId = 'mockAppInstanceId';
      const String expectedAppsflyerId = 'mockAppsflyerId';
      const String expectedPlatform = 'android';

      when(() => mockDataCollector.getDeviceId())
          .thenAnswer((_) => Future<String>.value(expectedDeviceId));
      when(() => mockDataCollector.getFirebaseAppInstanceId())
          .thenAnswer((_) => Future<String>.value(expectedAppInstanceId));
      when(() => mockAppsflyerHandler.getAppsflyerId())
          .thenAnswer((_) => Future<String>.value(expectedAppsflyerId));
      when(() => mockDOPUtilFunctions.getDevicePlatform()).thenReturn(expectedPlatform);

      // Define the initial URL
      const String initialUrl = 'https://www.example.com';

      // Call the function to add query parameters
      final String updatedUrl = await webViewUtil.addingDopAdditionalQueryParams(initialUrl);

      // Parse the updated URL to check the query parameters
      final Uri updatedUri = Uri.parse(updatedUrl);

      // Check if the device_id parameter has been added correctly
      expect(updatedUri.queryParameters['device_id'], expectedDeviceId);
      expect(updatedUri.queryParameters['firebase_instance_id'], expectedAppInstanceId);
      expect(updatedUri.queryParameters['appsflyer_id'], expectedAppsflyerId);
      expect(updatedUri.queryParameters['platform'], expectedPlatform);
      expect(updatedUri.toString(),
          'https://www.example.com?device_id=$expectedDeviceId&firebase_instance_id=$expectedAppInstanceId&appsflyer_id=$expectedAppsflyerId&platform=$expectedPlatform');

      getIt.unregister<DataCollector>();
    });
  });

  group('verify checkCameraPermission()', () {
    test('verify checkCameraPermission() with permission is granted', () async {
      webViewUtil.isCameraPermissionGranted = true;
      final bool result = await webViewUtil.checkCameraPermission();
      expect(result, true);
    });

    test('verify checkCameraPermission() with permission is not granted', () async {
      webViewUtil.isCameraPermissionGranted = false;
      final bool result = await webViewUtil.checkCameraPermission();
      expect(result, false);
    });
  });

  group('test cancelDOPJourneyDialog()', () {
    test('verify call showDialogConfirm()', () async {
      await webViewUtil.cancelDOPJourneyDialog();

      verify(() => EvoDialogHelper().showDialogConfirm(
            title: EvoStrings.cancelDOPJourneyTitle,
            content: EvoStrings.cancelDOPJourneyDescription,
            textPositive: EvoStrings.continueBtn,
            textNegative: EvoStrings.agree,
            dialogId: EvoDialogId.confirmToCancelDOPJourneyDialog,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });
  });

  group('test cancelDOPJourneyDialog callback', () {
    final BuildContext mockBuildContext = MockBuildContext();
    final CommonNavigator mockCommonNavigator = MockCommonNavigator();

    setUp(() {
      //setup a mock navigator context
      getIt.unregister<GlobalKeyProvider>();
      getIt.registerLazySingleton<CommonNavigator>(() => mockCommonNavigator);
      setUpMockGlobalKeyProvider(mockBuildContext);
      when(() => mockBuildContext.pop()).thenAnswer((_) {});
      when(() => mockBuildContext.maybePop()).thenAnswer((_) => true);
    });

    tearDown(() {
      getIt.unregister<CommonNavigator>();
    });

    test('verify showDialogConfirm callback', () async {
      await webViewUtil.cancelDOPJourneyDialog();
      final List<dynamic> captured = verify(() => EvoDialogHelper().showDialogConfirm(
            title: EvoStrings.cancelDOPJourneyTitle,
            content: EvoStrings.cancelDOPJourneyDescription,
            textPositive: EvoStrings.continueBtn,
            textNegative: EvoStrings.agree,
            dialogId: EvoDialogId.confirmToCancelDOPJourneyDialog,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: captureAny(named: 'onClickPositive'),
            onClickNegative: captureAny(named: 'onClickNegative'),
          )).captured;
      final VoidCallback onClickPositiveCallBack = captured[0];
      final VoidCallback onClickNegativeCallBack = captured[1];
      onClickPositiveCallBack();
      onClickNegativeCallBack();
      verify(() => mockBuildContext.pop()).called(2);
    });
  });

  group('verify handleBackInAppWebView()', () {
    test('verify with controller is null', () async {
      await webViewUtil.handleBackInAppWebView(null);

      verifyNever(() => mockController.canGoBack?.call());
      verifyNever(() => mockController.goBack?.call());

      verifyNever(() => mockController.hideKeyboard?.call());

      verify(() => EvoDialogHelper().showDialogConfirm(
            title: EvoStrings.cancelDOPJourneyTitle,
            content: EvoStrings.cancelDOPJourneyDescription,
            textPositive: EvoStrings.continueBtn,
            textNegative: EvoStrings.agree,
            dialogId: EvoDialogId.confirmToCancelDOPJourneyDialog,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });

    test('verify with controller is not null & canGoBack is false', () async {
      when(() => mockController.canGoBack).thenAnswer((_) => () {
            return Future<bool>.value(false);
          });

      await webViewUtil.handleBackInAppWebView(mockController);

      verify(() => mockController.canGoBack?.call()).called(1);
      verifyNever(() => mockController.goBack?.call());

      verify(() => mockController.hideKeyboard?.call()).called(1);

      verify(() => EvoDialogHelper().showDialogConfirm(
            title: EvoStrings.cancelDOPJourneyTitle,
            content: EvoStrings.cancelDOPJourneyDescription,
            textPositive: EvoStrings.continueBtn,
            textNegative: EvoStrings.agree,
            dialogId: EvoDialogId.confirmToCancelDOPJourneyDialog,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });

    test('verify with controller is not null', () async {
      await webViewUtil.handleBackInAppWebView(mockController);

      verify(() => mockController.canGoBack?.call()).called(1);
      verify(() => mockController.goBack?.call()).called(1);

      verifyNever(() => mockController.hideKeyboard?.call());

      verifyNever(() => EvoDialogHelper().showDialogConfirm(
            title: any(named: 'title'),
            content: any(named: 'content'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            dialogId: any(named: 'dialogId'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          ));
    });
  });

  group('verify hideKeyboard()', () {
    test('verify when controller is null', () {
      webViewUtil.hideKeyboard(null);

      verifyNever(() => mockController.hideKeyboard?.call());
    });

    test('verify when controller is not null', () {
      webViewUtil.hideKeyboard(mockController);

      verify(() => mockController.hideKeyboard?.call()).called(1);
    });
  });

  group('verify confirmCancelDOPJourney()', () {
    test('verify when controller is null', () {
      webViewUtil.confirmCancelDOPJourney(null);

      verifyNever(() => mockController.hideKeyboard?.call());

      verify(() => EvoDialogHelper().showDialogConfirm(
            title: EvoStrings.cancelDOPJourneyTitle,
            content: EvoStrings.cancelDOPJourneyDescription,
            textPositive: EvoStrings.continueBtn,
            textNegative: EvoStrings.agree,
            dialogId: EvoDialogId.confirmToCancelDOPJourneyDialog,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });

    test('verify when controller is not null', () {
      webViewUtil.confirmCancelDOPJourney(mockController);

      verify(() => mockController.hideKeyboard?.call()).called(1);

      verify(() => EvoDialogHelper().showDialogConfirm(
            title: EvoStrings.cancelDOPJourneyTitle,
            content: EvoStrings.cancelDOPJourneyDescription,
            textPositive: EvoStrings.continueBtn,
            textNegative: EvoStrings.agree,
            dialogId: EvoDialogId.confirmToCancelDOPJourneyDialog,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });
  });

  group('test getRegExpOfSpecialLinkHandledByEvoApp', () {
    final List<String> invalidAdditionalUrls = <String>[
      'http://evocard.tpb.vn/',
      'http://www.evocard.tpb.vn/',
      'https://evocard.tpb/',
      'https://evocard.vn/',
      'http://www.goevo.vn/',
      'https://vn/acb',
    ];

    final List<String> validAdditionalUrls = <String>[
      'https://evocard.tpb.vn/',
      'https://evocard.tpb.vn',
      'https://www.evocard.tpb.vn',
      'https://evocard.tpb.vn/acb',
      'https://evocard.tpb.vn/acb?query=abc',
      'https://evocard.tpb.vn/acb/def',
      'https://evocard.tpb.vn?query=abc',
      'https://evocard.tpb.vn/?query=abc',
      'https://www.goevo.vn/',
      'https://goevo.vn/',
      'https://www.goevo.vn',
      'https://www.goevo.vn/acb',
      'https://www.goevo.vn/acb/def',
      'https://www.goevo.vn/acb?query=abc',
      'https://goevo.vn?query=abc',
      'https://goevo.vn/?query=abc',
    ];

    final List<String> validOneLinkDeepLinkUrl = <String>[
      'https://evoappvn-stag.onelink.me',
      'https://evoappvn-stag.onelink.me/',
      'https://www.evoappvn-stag.onelink.me',
      'https://www.evoappvn-stag.onelink.me/abc',
      'https://www.evoappvn-stag.onelink.me/',
      'https://evoappvn-stag.onelink.me?query=abc',
      'https://evoappvn-stag.onelink.me/?query=abc',
      'https://evoappvn-stag.onelink.me/abc/?query=abc',
      'https://evoappvn-stag.onelink.me/abc/xyz?query=abc',
      'https://evoappvn-stag.onelink.me/0Fmo?af_xp=custom&pid=evo_dop_nfc_uat&c=evo_dop_nfc_uat&af_dp=evoappvn%3A%2F%2Fmobile%2Fdeeplinking&deep_link_value=evoappvn%3A%2F%2Fmobile%2Fdeeplinking%3Fscreen_name%3Dwebview%26web_link%3Dhttps%253A%252F%252Frelease-tpbank.tsengineering.io%252Fpartner%252Fevoapp%252F%253Fenable_webview%253Dtrue%2526show_popup%253Dtrue&af_force_deeplink=true'
          'evoappvn://mobile/deeplinking',
      'evoappvn://mobile/deeplinking/dop_completed',
      'evoappvn://mobile/deeplinking?screen_name=webview&web_link=https://your-web-link.com&title=title',
      'evoappvn://mobile/deeplinking?screen_name=dop_native_introduction_screen&utm_source=evo_hoanghamobile&utm_campaign=offline_hhm_270524',
    ];

    const List<String> invalidOneLinkDeepLinkUrls = <String>[
      'https://evoappvn.onelink.me',
      'https://evoappvn-uat.onelink.me',
      'http://sub.evoappvn-stag.onelink.vn',
      'http://evoappvn-stag.vn/path/to/resource',
      'https://evoappvn-stag.com',
      'https://onelink.me',
      'https://evoappvn-stag.onelink.com',
      'https://goevo.com?asd=123',
      'https://example.com',
      'https://evoappvn-stag.onelink.me.com',
      'http://www.evoappvn-stag.onelink.me',
      'http://www.evoappvn-stag.onelink.me/abc',
      'http://www.evoappvn-stag.onelink.me/',
      'http://evoappvn-stag.onelink.me/abc?query=abc',
      'http://evoappvn-stag.onelink.me/abc/xyz?query=abc',
      'evoappvn://mobile/deeplinking.com',
      'evoappvn://mobile/deep-linking.com',
      'evoappvn://mobile/deeplinking.com/dop_completed',
      'evoappvn://mobile/deeplinking/dop_completed.com',
      'evoappvn://mobile/deeplinking.com.vn',
      'evo-appvn://mobile/deeplinking',
      'evo.appvn://mobile/deeplinking',
      'appvn://mobile/deeplinking',
    ];

    setUp(() {});

    test('verify if not pass additionalFormat ', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );

      final RegExp regex = webViewUtil.getRegExpOfSpecialLinkHandledByEvoApp();

      for (final String url in validOneLinkDeepLinkUrl) {
        expect(regex.hasMatch(url), isTrue);
      }

      for (final String url in invalidOneLinkDeepLinkUrls) {
        expect(regex.hasMatch(url), isFalse);
      }
    });

    test('verify if have pass the additionalFormat ', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );

      final RegExp additionalRegEx =
          RegExp(r'^https://(www\.)?(goevo\.vn|evocard\.tpb\.vn)(?:/.*)?(?:\?.*)?$');

      final RegExp regex =
          webViewUtil.getRegExpOfSpecialLinkHandledByEvoApp(additionalFormat: additionalRegEx);

      for (final String url in validOneLinkDeepLinkUrl) {
        expect(regex.hasMatch(url), isTrue);
      }

      for (final String url in validAdditionalUrls) {
        expect(regex.hasMatch(url), isTrue);
      }

      for (final String url in invalidOneLinkDeepLinkUrls) {
        expect(regex.hasMatch(url), isFalse);
      }
      for (final String url in invalidAdditionalUrls) {
        expect(regex.hasMatch(url), isFalse);
      }
    });
  });

  group('verify checkCameraPermission()', () {
    final WebViewUtil webViewUtil = WebViewUtil();
    const MethodChannel channel = MethodChannel('flutter.baseflow.com/permissions/methods');
    tearDown(() {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, null);
    });

    test('verify checkCameraPermission() with permission is granted', () async {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          switch (methodCall.method) {
            case 'checkPermissionStatus':
              return 1;
            case 'requestPermissions':
              return <int, int>{3: 1};
            default:
              return null;
          }
        },
      );
      final bool result = await webViewUtil.checkCameraPermission();
      expect(result, true);
    });

    test('verify checkCameraPermission() with permission is not granted', () async {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == 'checkPermissionStatus') {
            return 0;
          }
          if (methodCall.method == 'requestPermissions') {
            return <int, int>{3: 0};
          }
          return null;
        },
      );
      final bool result = await webViewUtil.checkCameraPermission();
      expect(result, false);
    });
  });
}
