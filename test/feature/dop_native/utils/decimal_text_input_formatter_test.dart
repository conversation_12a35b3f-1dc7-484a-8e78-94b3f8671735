import 'package:evoapp/feature/dop_native/util/decimal_text_input_formatter.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DecimalTextInputFormatter Tests', () {
    late DecimalTextInputFormatter formatter;

    setUp(() {
      formatter = DecimalTextInputFormatter(decimalRange: 2, maxValue: 1000);
    });

    test('clear to empty', () {
      const TextEditingValue oldValue = TextEditingValue(text: '1');
      const TextEditingValue newValue = TextEditingValue();
      expect(formatter.formatEditUpdate(oldValue, newValue), newValue);
    });

    test('should not allow space', () {
      const TextEditingValue oldValue = TextEditingValue();
      const TextEditingValue newValue = TextEditingValue(text: ' ');
      expect(formatter.formatEditUpdate(oldValue, newValue), oldValue);
    });

    test('should not allow multiple commas', () {
      const TextEditingValue oldValue = TextEditingValue(text: '1,');
      const TextEditingValue newValue = TextEditingValue(text: '1,,');
      expect(formatter.formatEditUpdate(oldValue, newValue), oldValue);
    });

    test('should ignore non-digit characters', () {
      const TextEditingValue oldValue = TextEditingValue(text: '1,');
      const TextEditingValue newValue = TextEditingValue(text: '1,a');
      expect(formatter.formatEditUpdate(oldValue, newValue), oldValue);
    });

    test('should not allow negative numbers', () {
      const TextEditingValue oldValue = TextEditingValue();
      const TextEditingValue newValue = TextEditingValue(text: '-1');
      expect(formatter.formatEditUpdate(oldValue, newValue), oldValue);
    });

    test('should add leading 0 if decimalSeparator is the first character', () {
      const TextEditingValue oldValue = TextEditingValue();
      const TextEditingValue newValue = TextEditingValue(text: ',');
      expect(formatter.formatEditUpdate(oldValue, newValue).text, '0,');
    });

    test('should remove leading 0 if followed by a number', () {
      const TextEditingValue oldValue = TextEditingValue(text: '0');
      const TextEditingValue newValue = TextEditingValue(text: '08');
      expect(formatter.formatEditUpdate(oldValue, newValue).text, '8');
    });

    test('should replace . by , if the input is 0.', () {
      const TextEditingValue oldValue = TextEditingValue(text: '0');
      const TextEditingValue newValue = TextEditingValue(text: '0.');
      expect(formatter.formatEditUpdate(oldValue, newValue).text, '0,');
    });

    test('should limit decimal places to decimalRange', () {
      const TextEditingValue oldValue = TextEditingValue(text: '1,23');
      const TextEditingValue newValue = TextEditingValue(text: '1,234');
      expect(formatter.formatEditUpdate(oldValue, newValue).text, oldValue.text);
    });

    test('should not allow values greater than maxValue', () {
      const TextEditingValue oldValue = TextEditingValue(text: '999');
      const TextEditingValue newValue = TextEditingValue(text: '1001');
      expect(formatter.formatEditUpdate(oldValue, newValue), oldValue);
    });

    test('should replace a dot with decimalSeparator at the end', () {
      const TextEditingValue oldValue = TextEditingValue(text: '1');
      const TextEditingValue newValue = TextEditingValue(text: '1.');
      expect(formatter.formatEditUpdate(oldValue, newValue).text, '1,');
    });
  });
}
