import 'package:evoapp/feature/dop_native/models/dop_native_dialog_id.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('DOPNativeDialogId', () {
    expect(DOPNativeDialogId.inputPhoneNumberDialog.id, 'dop_native_input_phone_number_dialog');
    expect(DOPNativeDialogId.confirmCloseDOPNativeFlowDialog.id,
        'dop_native_confirm_close_dop_native_flow_dialog');
    expect(DOPNativeDialogId.dopNativeAddressDialog.id, 'dop_native_address_dialog');
    expect(DOPNativeDialogId.dopNativeEmploymentDialog.id, 'dop_native_employment_dialog');
    expect(DOPNativeDialogId.dopNativeLinkCardDialog.id, 'dop_native_link_card_dialog');
    expect(DOPNativeDialogId.setupPosLimitDialog.id, 'dop_native_set_pos_limit_dialog');
    expect(DOPNativeDialogId.dopRetryNFCVerificationDialog.id, 'dop_retry_nfc_verification_dialog');
    expect(DOPNativeDialogId.dopNFCVerificationInvalidTokenDialog.id,
        'dop_nfc_verification_invalid_token_dialog');
    expect(DOPNativeDialogId.dopNFCVerificationNetworkErrorDialog.id,
        'dop_nfc_verification_network_error_dialog');
    expect(DOPNativeDialogId.dopNFCVerificationEnableNfcDialog.id,
        'dop_nfc_verification_enable_nfc_dialog');
    expect(DOPNativeDialogId.dopNFCVerificationErrorWrongInfoDialog.id,
        'dop_nfc_verification_error_wrong_info_dialog');
    expect(DOPNativeDialogId.dopNFCVerificationErrorUnknownDialog.id,
        'dop_nfc_verification_error_unknown_dialog');
    expect(DOPNativeDialogId.dopNFCVerificationErrorTimeoutDialog.id,
        'dop_nfc_verification_error_timeout_dialog');
  });
}
