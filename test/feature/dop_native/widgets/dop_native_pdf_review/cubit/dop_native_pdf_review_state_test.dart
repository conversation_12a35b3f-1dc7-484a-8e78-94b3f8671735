import 'package:evoapp/feature/dop_native/widgets/dop_native_pdf_review/cubit/dop_native_pdf_review_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakePdfUrl = 'fakePdfUrl';
  group('DOPNativePdfReviewState', () {
    test('DOPNativePdfReviewInitial should be a subclass of DOPNativePdfReviewState', () {
      expect(DOPNativePdfReviewInitial(), isA<DOPNativePdfReviewState>());
    });

    test('DOPNativePdfReviewLoading should be a subclass of DOPNativePdfReviewState', () {
      final DOPNativePdfReviewLoading state = DOPNativePdfReviewLoading(fakePdfUrl);
      expect(state, isA<DOPNativePdfReviewState>());
      expect(state.url, fakePdfUrl);
    });

    test('DOPNativePdfReviewLoaded should be a subclass of DOPNativePdfReviewState', () {
      final DOPNativePdfReviewLoaded state = DOPNativePdfReviewLoaded(fakePdfUrl);
      expect(state, isA<DOPNativePdfReviewState>());
      expect(state.url, fakePdfUrl);
    });

    test('DOPNativePdfReviewError should be a subclass of DOPNativePdfReviewState', () {
      expect(DOPNativePdfReviewError(), isA<DOPNativePdfReviewState>());
    });
  });
}
