import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_resources.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/widgets/card_activate/dop_native_card_status_title_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockContext extends Mock implements BuildContext {}

void main() {
  late CommonImageProvider mockCommonImageProvider;

  const double iconCardHeight = 56;

  setUpAll(() {
    registerFallbackValue(MockContext());
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    getItRegisterTextStyle();
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());

    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    when(() => mockCommonImageProvider.asset(
          any(),
          height: any(named: 'height'),
        )).thenReturn(const SizedBox(height: iconCardHeight));
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('Test DOPNativeCardStatusTitleWidget', () {
    const String fakeTitle = 'fake title';
    const String fakeDescription = 'fake description';

    testWidgets('verify init widget', (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: DOPNativeCardStatusTitleWidget(
                title: fakeTitle,
                description: fakeDescription,
              ),
            ),
          ),
        );
      });

      verify(() => mockCommonImageProvider.asset(
            DOPNativeImages.icCardVerify,
            height: iconCardHeight,
          )).called(1);

      //check title present
      final Finder textFinder = find.text(fakeTitle);
      expect(textFinder, findsOneWidget);
      final Text titleWidget = tester.widget(textFinder);
      expect(
          titleWidget.style,
          dopNativeTextStyles.h500().copyWith(
                height: 1.3,
                letterSpacing: -0.03,
              ));

      //check description present
      final Finder descFinder = find.text(fakeDescription);
      expect(descFinder, findsOneWidget);
      final Text descWidget = tester.widget(descFinder);
      expect(
        descWidget.style,
        dopNativeTextStyles.bodyLarge(dopNativeColors.textPassive).copyWith(height: 1.5),
      );
    });
  });
}
