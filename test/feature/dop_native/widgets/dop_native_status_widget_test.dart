import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/widgets/dop_native_status_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockContext extends Mock implements BuildContext {}

void main() {
  late CommonImageProvider mockCommonImageProvider;
  late DOPNativeTextStyles dopNativeTextStyles;

  const double expectTopSizedBox = 10;

  const double expectTopPaddingPercentage = 48 / 812;

  const double expectDefaultIconHeight = 93;

  setUpAll(() {
    registerFallbackValue(MockContext());

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    getItRegisterTextStyle();
    setUtilsMockInstanceForTesting();
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
    dopNativeTextStyles = getIt.get<DOPNativeTextStyles>();
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());

    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(expectTopSizedBox);

    when(() => mockCommonImageProvider.asset(
          any(),
          height: any(named: 'height'),
        )).thenReturn(const SizedBox(height: 93));
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  void verifyDOPNativeStatusWidget({
    required WidgetTester tester,
    required String icon,
    required String title,
    required double imageHeight,
    String? description,
    Widget? descriptionWidget,
    Widget? noticeWidget,
  }) {
    // verify top padding space is correct
    verify(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: expectTopPaddingPercentage,
        )).called(1);

    verify(() => mockCommonImageProvider.asset(
          icon,
          height: imageHeight,
        )).called(1);

    if (descriptionWidget != null) {
      final Finder descriptionWidgetFinder = find.byWidget(descriptionWidget);
      expect(descriptionWidgetFinder, findsOneWidget);
    } else if (description?.isNotEmpty == true) {
      final Finder descriptionFinder = find.byType(MarkdownBody);
      expect(descriptionFinder, findsOneWidget);
      final MarkdownBody descriptionWidget = tester.widget(descriptionFinder);
      expect(descriptionWidget.fitContent, false);
      expect(
        descriptionWidget.styleSheet?.p,
        dopNativeTextStyles.bodyLarge(evoColors.textPassive),
      );
      expect(
        descriptionWidget.styleSheet?.strong,
        dopNativeTextStyles.h300(color: evoColors.textActive),
      );
      expect(descriptionWidget.styleSheet?.textAlign, WrapAlignment.center);
      expect(descriptionWidget.data, description);
    }

    if (noticeWidget != null) {
      final Finder noticeWidgetFinder = find.byWidget(noticeWidget);
      expect(noticeWidgetFinder, findsOneWidget);
    }
  }

  void verifyOrderOfWidgets({
    required WidgetTester tester,
    String? description,
    Widget? descriptionWidget,
    Widget? ctaWidget,
    Widget? secondCtaWidget,
    Axis ctaAxis = Axis.vertical,
    Widget? noticeWidget,
  }) {
    int index = 0;
    // Retrieve the list of widgets in the Column
    final List<Widget> columnWidgets =
        tester.widgetList(find.byType(Column)).expand((Widget widget) {
      if (widget is Column) {
        return widget.children;
      }
      return <Widget>[];
    }).toList();

    // verify list of widget is not empty
    expect(columnWidgets, isNotEmpty);

    // space between navigation bar and image
    expect(columnWidgets[index], isA<SizedBox>());
    index++;

    // image widget
    expect(columnWidgets[index], isA<SizedBox>());
    expect((columnWidgets[index] as SizedBox).height, 93);
    index++;

    // space between image and title
    expect(columnWidgets[index], isA<SizedBox>());
    expect((columnWidgets[index] as SizedBox).height, 24);
    index++;

    // title widget
    expect(columnWidgets[index], isA<Text>());
    index++;

    // description widget
    if (descriptionWidget != null) {
      // space between title and description
      expect(columnWidgets[index], isA<SizedBox>());
      expect((columnWidgets[index] as SizedBox).height, 8);
      index++;

      expect(columnWidgets[index], descriptionWidget);
      index++;
    } else if (description?.isNotEmpty == true) {
      // space between title and description
      expect(columnWidgets[index], isA<SizedBox>());
      expect((columnWidgets[index] as SizedBox).height, 8);
      index++;

      expect(columnWidgets[index], isA<MarkdownBody>());
      index++;
    }

    // notice widget
    if (noticeWidget != null) {
      // space between description and notice widget
      expect(columnWidgets[index], isA<SizedBox>());
      expect((columnWidgets[index] as SizedBox).height, 24);
      index++;

      expect(columnWidgets[index], noticeWidget);
      index++;
    }

    if (ctaWidget != null || secondCtaWidget != null) {
      switch (ctaAxis) {
        case Axis.horizontal:
          // space between description and CTA widget
          expect(columnWidgets[index], isA<Padding>());
          final Padding paddingWidget = columnWidgets[index] as Padding;
          expect(paddingWidget.padding, EdgeInsets.only(top: 24, bottom: 20));

          final Row rowWidget = paddingWidget.child as Row;
          int subIndex = 0;

          if (secondCtaWidget != null) {
            expect(rowWidget.children[subIndex], secondCtaWidget);
            subIndex++;
          }

          if (ctaWidget != null) {
            expect(rowWidget.children[subIndex], isA<SizedBox>());
            expect(
                (rowWidget.children[subIndex] as SizedBox).width, secondCtaWidget != null ? 8 : 0);
            subIndex++;

            expect(rowWidget.children[subIndex], ctaWidget);
            subIndex++;
          }

          break;
        case Axis.vertical:
          final Column subColumn = columnWidgets[index] as Column;
          int subIndex = 0;

          if (ctaWidget != null) {
            expect(subColumn.children[subIndex], isA<SizedBox>());
            expect((subColumn.children[subIndex] as SizedBox).height, 24);
            subIndex++;

            expect(subColumn.children[subIndex], ctaWidget);
            subIndex++;
          }

          if (secondCtaWidget != null) {
            expect(subColumn.children[subIndex], isA<SizedBox>());
            expect((subColumn.children[subIndex] as SizedBox).height, ctaWidget != null ? 8 : 24);
            subIndex++;

            expect(subColumn.children[subIndex], secondCtaWidget);
            subIndex++;
          }
          break;
      }
    }
  }

  test('DOPNativeStatusWidget constants', () {
    expect(DOPNativeStatusWidget.topPaddingPercentage, expectTopPaddingPercentage);
    expect(DOPNativeStatusWidget.defaultIconHeight, expectDefaultIconHeight);
  });

  group('DOPNativeStatusWidget Tests', () {
    const String testIcon = 'assets/icons/test_icon.png';
    const String testTitle = 'An error occurred';
    const String testDescription = 'Something went wrong. Please try again later.';
    const Key testCTAKey = Key('ctaButton');
    const Key testSecondCTAKey = Key('secondCtaButton');
    const String titleCTAButton = 'Retry';
    const String titleSecondCTAButton = 'Cancel';

    final Widget testCTAWidget = ElevatedButton(
      key: testCTAKey,
      onPressed: () {},
      child: const Text(titleCTAButton),
    );

    final Widget testSecondCTAWidget = ElevatedButton(
      key: testSecondCTAKey,
      onPressed: () {},
      child: const Text(titleSecondCTAButton),
    );

    void verifyCTAWidget({required WidgetTester tester}) {
      final Finder ctaButtonFinder = find.byKey(testCTAKey);
      expect(ctaButtonFinder, findsOneWidget);
      final ElevatedButton ctaButtonWidget = tester.widget(ctaButtonFinder);
      expect(ctaButtonWidget.child, isA<Text>());
      final Text ctaButtonTextWidget = ctaButtonWidget.child as Text;
      expect(ctaButtonTextWidget.data, titleCTAButton);
    }

    void verifySecondCTAWidget({required WidgetTester tester}) {
      final Finder ctaButtonFinder = find.byKey(testSecondCTAKey);
      expect(ctaButtonFinder, findsOneWidget);
      final ElevatedButton ctaButtonWidget = tester.widget(ctaButtonFinder);
      expect(ctaButtonWidget.child, isA<Text>());
      final Text ctaButtonTextWidget = ctaButtonWidget.child as Text;
      expect(ctaButtonTextWidget.data, titleSecondCTAButton);
    }

    group('displays all CTA correctly', () {
      testWidgets('does not display CTA widget when null', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                description: testDescription,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          description: testDescription,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(tester: tester, description: testDescription);

        expect(find.byType(ElevatedButton), findsNothing);
      });

      testWidgets('only one CTA button', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                ctaWidget: testCTAWidget,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(
          tester: tester,
        );

        // verify CTA button
        verifyCTAWidget(tester: tester);
      });

      testWidgets('vertical - only CTA', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                ctaWidget: testCTAWidget,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(
          tester: tester,
          ctaWidget: testCTAWidget,
        );

        // verify CTA button
        verifyCTAWidget(tester: tester);
      });

      testWidgets('vertical - only second CTA', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                secondaryCTAWidget: testSecondCTAWidget,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(
          tester: tester,
          secondCtaWidget: testSecondCTAWidget,
        );

        // verify CTA button
        verifySecondCTAWidget(tester: tester);
      });

      testWidgets('vertical - both CTA button', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                ctaWidget: testCTAWidget,
                secondaryCTAWidget: testSecondCTAWidget,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(
          tester: tester,
          secondCtaWidget: testSecondCTAWidget,
          ctaWidget: testCTAWidget,
        );

        // verify CTA button
        verifyCTAWidget(tester: tester);
        verifySecondCTAWidget(tester: tester);
      });

      testWidgets('horizontal - only CTA button', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                ctaWidget: testCTAWidget,
                buttonAxis: Axis.horizontal,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(
          tester: tester,
          ctaWidget: testCTAWidget,
          ctaAxis: Axis.horizontal,
        );

        // verify CTA button
        verifyCTAWidget(tester: tester);
      });

      testWidgets('horizontal - only second CTA button', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                secondaryCTAWidget: testSecondCTAWidget,
                buttonAxis: Axis.horizontal,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(
          tester: tester,
          secondCtaWidget: testSecondCTAWidget,
          ctaAxis: Axis.horizontal,
        );

        // verify CTA button
        verifySecondCTAWidget(tester: tester);
      });

      testWidgets('horizontal - both CTA button', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                ctaWidget: testCTAWidget,
                secondaryCTAWidget: testSecondCTAWidget,
                buttonAxis: Axis.horizontal,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(
          tester: tester,
          ctaWidget: testCTAWidget,
          secondCtaWidget: testSecondCTAWidget,
          ctaAxis: Axis.horizontal,
        );

        // verify CTA button
        verifyCTAWidget(tester: tester);
        verifySecondCTAWidget(tester: tester);
      });
    });

    group('displays all description correctly', () {
      testWidgets('only descriptionWidget', (WidgetTester tester) async {
        const String customDescription = 'Custom description';
        const Widget expectDescriptionWidget = Text(customDescription);

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                ctaWidget: testCTAWidget,
                descriptionWidget: expectDescriptionWidget,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          descriptionWidget: expectDescriptionWidget,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(
          tester: tester,
          descriptionWidget: expectDescriptionWidget,
        );

        // verify CTA button
        verifyCTAWidget(tester: tester);
      });

      testWidgets('only description text', (WidgetTester tester) async {
        const String customDescription = 'Custom description';

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                ctaWidget: testCTAWidget,
                description: customDescription,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          description: customDescription,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(
          tester: tester,
          description: customDescription,
        );

        // verify CTA button
        verifyCTAWidget(tester: tester);
      });

      testWidgets('no description', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: DOPNativeStatusWidget(
                icon: testIcon,
                title: testTitle,
                ctaWidget: testCTAWidget,
              ),
            ),
          ),
        );

        verifyDOPNativeStatusWidget(
          tester: tester,
          icon: testIcon,
          title: testTitle,
          imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        );

        verifyOrderOfWidgets(
          tester: tester,
        );

        // verify CTA button
        verifyCTAWidget(tester: tester);
      });
    });

    testWidgets('Give custom image height, should display image with the height',
        (WidgetTester tester) async {
      const double customHeight = 100;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeStatusWidget(
              icon: testIcon,
              title: testTitle,
              description: testDescription,
              ctaWidget: testCTAWidget,
              iconHeight: customHeight,
            ),
          ),
        ),
      );

      verifyDOPNativeStatusWidget(
        tester: tester,
        icon: testIcon,
        title: testTitle,
        description: testDescription,
        imageHeight: customHeight,
      );

      verifyOrderOfWidgets(
        tester: tester,
        description: testDescription,
      );

      // verify CTA button
      verifyCTAWidget(tester: tester);
    });

    testWidgets('display notice widget', (WidgetTester tester) async {
      final Container noticeWidget = Container(key: ValueKey<String>('notice_widget'));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeStatusWidget(
              icon: testIcon,
              title: testTitle,
              noticeWidget: noticeWidget,
            ),
          ),
        ),
      );

      verifyDOPNativeStatusWidget(
        tester: tester,
        icon: testIcon,
        title: testTitle,
        imageHeight: DOPNativeStatusWidget.defaultIconHeight,
        noticeWidget: noticeWidget,
      );

      verifyOrderOfWidgets(
        tester: tester,
        noticeWidget: noticeWidget,
      );
    });
  });
}
