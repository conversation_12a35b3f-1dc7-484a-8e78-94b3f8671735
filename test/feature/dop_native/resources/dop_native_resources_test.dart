import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_resources.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDOPNativeColors extends Mock implements DOPNativeColors {}

class MockDopNativeButtonStyles extends Mock implements DopNativeButtonStyles {}

class MockDOPNativeTextStyles extends Mock implements DOPNativeTextStyles {}

void main() {
  setUpAll(() {
    getIt.registerSingleton<DOPNativeColors>(MockDOPNativeColors());
    getIt.registerSingleton<DopNativeButtonStyles>(MockDopNativeButtonStyles());
    getIt.registerSingleton<DOPNativeTextStyles>(MockDOPNativeTextStyles());
  });

  test('verify value', () {
    expect(dopNativeColors, isA<MockDOPNativeColors>());
    expect(dopNativeButtonStyles, isA<MockDopNativeButtonStyles>());
    expect(dopNativeTextStyles, isA<MockDOPNativeTextStyles>());
  });
}
