import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockDOPNativeColors extends Mock implements DOPNativeColors {}

void main() {
  late DopNativeButtonStyles buttonStyles;
  late DOPNativeColors dopNativeColors;
  late CommonColors commonColors;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getIt.registerSingleton<DOPNativeColors>(MockDOPNativeColors());
    dopNativeColors = getIt.get<DOPNativeColors>();

    commonColors = getIt.get<CommonColors>();
  });

  setUp(() {
    when(() => dopNativeColors.primaryButtonForegroundDisable).thenReturn(Colors.grey);
    when(() => dopNativeColors.primaryButtonForeground).thenReturn(Colors.white);
    when(() => dopNativeColors.primaryButtonBgDisable).thenReturn(Colors.black12);
    when(() => dopNativeColors.primaryButtonBg).thenReturn(Colors.blue);
    when(() => dopNativeColors.tertiaryButtonForegroundDisable).thenReturn(Colors.red);
    when(() => dopNativeColors.tertiaryButtonForeground).thenReturn(Colors.green);
    when(() => dopNativeColors.tertiaryButtonBgDisable).thenReturn(Colors.grey);
    when(() => dopNativeColors.tertiaryButtonBg).thenReturn(Colors.black);

    buttonStyles = DopNativeButtonStyles();
  });

  group('verify primary button', () {
    test('primary button style provides correct colors for enabled state', () {
      final ButtonStyle style = buttonStyles.primary(ButtonSize.large);

      expect(
        style.foregroundColor!.resolve(<WidgetState>{}),
        dopNativeColors.primaryButtonForeground,
      );

      expect(
        style.backgroundColor!.resolve(<WidgetState>{}),
        dopNativeColors.primaryButtonBg,
      );
    });

    test('primary button style provides correct colors for disabled state', () {
      final ButtonStyle style = buttonStyles.primary(ButtonSize.large);

      expect(
        style.foregroundColor!.resolve(<WidgetState>{WidgetState.disabled}),
        dopNativeColors.primaryButtonForegroundDisable,
      );

      expect(
        style.backgroundColor!.resolve(<WidgetState>{WidgetState.disabled}),
        dopNativeColors.primaryButtonBgDisable,
      );
    });
  });

  group('verify tertiary button', () {
    test('tertiary button style provides correct colors for enabled state', () {
      final ButtonStyle style = buttonStyles.tertiary(ButtonSize.large);

      expect(
        style.foregroundColor!.resolve(<WidgetState>{}),
        commonColors.tertiaryButtonForeground,
      );

      expect(
        style.backgroundColor!.resolve(<WidgetState>{}),
        dopNativeColors.tertiaryButtonBg,
      );
    });

    test('tertiary button style provides correct colors for disabled state', () {
      final ButtonStyle style = buttonStyles.tertiary(ButtonSize.large);

      expect(
        style.foregroundColor!.resolve(<WidgetState>{WidgetState.disabled}),
        commonColors.tertiaryButtonForegroundDisable,
      );

      expect(
        style.backgroundColor!.resolve(<WidgetState>{WidgetState.disabled}),
        dopNativeColors.tertiaryButtonBgDisable,
      );
    });
  });
}
