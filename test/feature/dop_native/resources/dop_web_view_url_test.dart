import 'package:evoapp/feature/dop_native/resources/dop_web_view_url.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Check DOP Web view URLs', () {
    expect(DOPWebViewUrl.dopWebViewStagUrl, 'https://staging-tpbank.tsengineering.io/');
    expect(DOPWebViewUrl.dopWebViewUATUrl, 'https://release-tpbank.tsengineering.io/');
    expect(DOPWebViewUrl.dopWebViewProdUrl, 'https://evocard.tpb.vn/');
  });

  group('Test getDopWebViewUrlByFlavor', () {
    const String fakeToken = 'fakeToken';
    test('Test getDopWebViewUrlByFlavor return correct value if flavor is production', () {
      FlavorConfig(
        flavor: FlavorType.prod.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
      );

      expect(getDopWebViewUrlByFlavor(fakeToken),
          '${DOPWebViewUrl.dopWebViewProdUrl}$fakeToken?enable_webview=true');
    });

    test('Test getDopWebViewUrlByFlavor return correct value if flavor is UAT', () {
      FlavorConfig(
        flavor: FlavorType.uat.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
      );

      expect(getDopWebViewUrlByFlavor(fakeToken),
          '${DOPWebViewUrl.dopWebViewUATUrl}$fakeToken?enable_webview=true');
    });

    test('Test getDopWebViewUrlByFlavor return correct value if flavor is STAG', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );

      expect(getDopWebViewUrlByFlavor(fakeToken),
          '${DOPWebViewUrl.dopWebViewUATUrl}$fakeToken?enable_webview=true');
    });
  });
}
