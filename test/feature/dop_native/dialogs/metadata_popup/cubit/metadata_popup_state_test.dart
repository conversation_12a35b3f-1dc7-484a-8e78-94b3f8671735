import 'package:evoapp/feature/dop_native/dialogs/metadata_popup/cubit/metadata_popup_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MetadataPopUpState', () {
    test('should be a subclass of MetadataPopUpState', () {
      final MetadataPopUpStateLoaded<String> state =
          MetadataPopUpStateLoaded<String>(data: <String>[]);

      expect(state, isA<MetadataPopUpState>());
    });

    test('should hold the correct data', () {
      final List<String> testData = <String>['item1', 'item2', 'item3'];

      final MetadataPopUpState state = MetadataPopUpStateLoaded<String>(data: testData);

      expect((state as MetadataPopUpStateLoaded<String>).data, equals(testData));
    });
  });
}
