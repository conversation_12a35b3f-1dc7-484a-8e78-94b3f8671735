import 'package:evoapp/data/response/dop_native/dop_native_bootstrap_auth_settings_entity.dart';
import 'package:evoapp/feature/deep_link/model/deep_link_shared_data.dart';
import 'package:evoapp/feature/dop_native/base/dop_native_api_error_handler_mixin.dart';
import 'package:evoapp/feature/dop_native/features/logging/dop_native_event_tracking_screen_id.dart';
import 'package:evoapp/feature/dop_native/features/status_screen/dop_native_status_screen.dart';
import 'package:evoapp/feature/dop_native/features/introduction/dop_native_introduction_screen.dart';
import 'package:evoapp/feature/dop_native/models/dop_native_state.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_navigation_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

// Create mock classes for any dependencies that are used in the methods
class MockDOPNativeNavigationUtils extends Mock implements DOPNativeNavigationUtils {}

class MockApiErrorHandlerClass with DopNativeApiErrorHandlerMixin {}

class MockAppState extends Mock implements AppState {}

class MockDeepLinkSharedData extends Mock implements DeepLinkSharedData {}

class MockDopUtilFunction extends Mock implements DOPUtilFunctions {}

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

void main() {
  // Initialize the mocks
  final DOPNativeNavigationUtils mockDOPNativeNavigationUtils = MockDOPNativeNavigationUtils();
  final AppState mockAppState = MockAppState();
  final DOPUtilFunctions mockDopUtilFunction = MockDopUtilFunction();

  late MockApiErrorHandlerClass mockApiErrorHandlerClass;

  late CommonNavigator commonNavigator;
  late MockBuildContext mockNavigatorContext;

  late EvoFlutterWrapper mockEvoFlutterWrapper;

  setUpAll(() {
    registerFallbackValue(MockBuildContext());

    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

    getIt.registerSingleton<DOPNativeNavigationUtils>(mockDOPNativeNavigationUtils);
    getIt.registerSingleton<AppState>(mockAppState);
    getIt.registerSingleton<DOPUtilFunctions>(mockDopUtilFunction);

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());

    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    getIt.registerSingleton<EvoFlutterWrapper>(MockEvoFlutterWrapper());
    mockEvoFlutterWrapper = getIt.get<EvoFlutterWrapper>();

    commonNavigator = getIt.get<CommonNavigator>();

    when(() => commonNavigator.pushReplacementNamed(
          any(),
          any(),
          extra: any(named: 'extra'),
        )).thenAnswer((_) => Future<void>.value());

    when(() => commonNavigator.pushNamed(
          any(),
          any(),
          extra: any(named: 'extra'),
        )).thenAnswer((_) => Future<void>.value());

    mockApiErrorHandlerClass = MockApiErrorHandlerClass();

    when(() => mockNavigatorContext.popUntilNamed(
          any(),
        )).thenAnswer((_) => Future<void>.value());
  });

  setUp(() {
    when(() => mockEvoFlutterWrapper.showDialog<void>(
          useSafeArea: any(named: 'useSafeArea'),
          builder: any(named: 'builder'),
        )).thenAnswer((_) => Future<void>.value());
  });

  tearDownAll(() {
    getIt.reset();
  });

  // Clean up the mock dependencies after each test
  tearDown(() {
    reset(mockDOPNativeNavigationUtils);
    reset(mockAppState);
    reset(mockDopUtilFunction);
    reset(mockEvoFlutterWrapper);
  });

  group('verify handleDopEvoApiError()', () {
    test(
      'handle [CommonHttpClient.INVALID_TOKEN]',
      () {
        // Arrange
        final ErrorUIModel errorUIModel = ErrorUIModel(statusCode: CommonHttpClient.INVALID_TOKEN);

        // Act
        mockApiErrorHandlerClass.handleDopEvoApiError(errorUIModel);

        // Assert
        final DOPNativeStatusScreenArg result = verify(() => mockNavigatorContext.pushNamed(
            Screen.dopNativeStatusScreen.name,
            extra: captureAny(named: 'extra'))).captured.single;

        expect(result.icon, DOPNativeImages.icSandClock);
        expect(result.title, DOPNativeStrings.dopNativeInvalidTokenTitle);
        expect(result.description, DOPNativeStrings.dopNativeInvalidTokenDesc);
        expect(result.ctaWidget, isA<CommonButton>());
      },
    );

    test(
      'handle [CommonHttpClient.SOCKET_ERRORS]',
      () {
        // Arrange
        final ErrorUIModel errorUIModel = ErrorUIModel(statusCode: CommonHttpClient.SOCKET_ERRORS);

        // Act
        mockApiErrorHandlerClass.handleDopEvoApiError(errorUIModel);

        // Assert
        final DOPNativeStatusScreenArg result = verify(() => mockNavigatorContext.pushNamed(
            Screen.dopNativeStatusScreen.name,
            extra: captureAny(named: 'extra'))).captured.single;

        expect(result.icon, DOPNativeImages.icInternetError);
        expect(result.title, DOPNativeStrings.dopNativeInternetErrorTitle);
        expect(result.description, DOPNativeStrings.dopNativeInternetErrorDesc);
        expect(result.ctaWidget, isA<CommonButton>());
      },
    );

    test(
      'handle [CommonHttpClient.NO_INTERNET]',
      () {
        // Arrange
        final ErrorUIModel errorUIModel = ErrorUIModel(statusCode: CommonHttpClient.NO_INTERNET);

        // Act
        mockApiErrorHandlerClass.handleDopEvoApiError(errorUIModel);

        // Assert
        final DOPNativeStatusScreenArg result = verify(() => mockNavigatorContext.pushNamed(
            Screen.dopNativeStatusScreen.name,
            extra: captureAny(named: 'extra'))).captured.single;

        expect(result.icon, DOPNativeImages.icInternetError);
        expect(result.title, DOPNativeStrings.dopNativeInternetErrorTitle);
        expect(result.description, DOPNativeStrings.dopNativeInternetErrorDesc);
        expect(result.ctaWidget, isA<CommonButton>());
      },
    );

    test(
      'handle [CommonHttpClient.UNKNOWN_ERRORS]',
      () {
        // Arrange
        final ErrorUIModel errorUIModel = ErrorUIModel(
          statusCode: CommonHttpClient.UNKNOWN_ERRORS,
          verdict: 'fake_verdict',
        );

        // Act
        mockApiErrorHandlerClass.handleDopEvoApiError(errorUIModel);

        // Assert
        final DOPNativeStatusScreenArg result = verify(() => mockNavigatorContext.pushNamed(
            Screen.dopNativeStatusScreen.name,
            extra: captureAny(named: 'extra'))).captured.single;

        expect(result.icon, DOPNativeImages.icOtpCodeError);
        expect(result.title, DOPNativeStrings.dopNativeCommonErrorTitle);
        expect(result.description, DOPNativeStrings.dopNativeCommonErrorDescription);
        expect(result.ctaWidget, isA<CommonButton>());
        expect(
          result.errorReason,
          'statusCode=${errorUIModel.statusCode}, verdict=${errorUIModel.verdict}',
        );
        expect(result.enableLogging, true);
        expect(
          result.eventTrackingScreenId,
          DOPNativeEventTrackingScreenId.dopNativeCommonErrorScreen,
        );
      },
    );

    test(
      'handle local error statusCode == null',
      () {
        // Arrange
        final ErrorUIModel errorUIModel = ErrorUIModel(verdict: 'fake_verdict');

        // Act
        mockApiErrorHandlerClass.handleDopEvoApiError(errorUIModel);

        // Assert
        final DOPNativeStatusScreenArg result = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.dopNativeStatusScreen.name,
            extra: captureAny(
              named: 'extra',
            ),
          ),
        ).captured.single;

        expect(result.icon, DOPNativeImages.icOtpCodeError);
        expect(result.title, DOPNativeStrings.dopNativeCommonErrorTitle);
        expect(result.description, DOPNativeStrings.dopNativeCommonErrorDescription);
        expect(result.ctaWidget, isA<CommonButton>());
        expect(result.errorReason, 'fake_verdict');
        expect(result.enableLogging, true);
        expect(
          result.eventTrackingScreenId,
          DOPNativeEventTrackingScreenId.dopNativeCommonErrorScreen,
        );
      },
    );
  });

  group('verify handleDopEvoApiError() with pushReplacementNamed = true', () {
    test(
      'handle [CommonHttpClient.INVALID_TOKEN]',
      () {
        // Arrange
        final ErrorUIModel errorUIModel = ErrorUIModel(statusCode: CommonHttpClient.INVALID_TOKEN);

        // Act
        mockApiErrorHandlerClass.handleDopEvoApiError(errorUIModel, isReplaceCurrentScreen: true);

        // Assert
        final DOPNativeStatusScreenArg result = verify(() =>
            mockNavigatorContext.pushReplacementNamed(Screen.dopNativeStatusScreen.name,
                extra: captureAny(named: 'extra'))).captured.single;

        expect(result.icon, DOPNativeImages.icSandClock);
        expect(result.title, DOPNativeStrings.dopNativeInvalidTokenTitle);
        expect(result.description, DOPNativeStrings.dopNativeInvalidTokenDesc);
        expect(result.ctaWidget, isA<CommonButton>());
      },
    );

    test(
      'handle [CommonHttpClient.SOCKET_ERRORS]',
      () {
        // Arrange
        final ErrorUIModel errorUIModel = ErrorUIModel(statusCode: CommonHttpClient.SOCKET_ERRORS);

        // Act
        mockApiErrorHandlerClass.handleDopEvoApiError(errorUIModel, isReplaceCurrentScreen: true);

        // Assert
        final DOPNativeStatusScreenArg result = verify(() =>
            mockNavigatorContext.pushReplacementNamed(Screen.dopNativeStatusScreen.name,
                extra: captureAny(named: 'extra'))).captured.single;

        expect(result.icon, DOPNativeImages.icInternetError);
        expect(result.title, DOPNativeStrings.dopNativeInternetErrorTitle);
        expect(result.description, DOPNativeStrings.dopNativeInternetErrorDesc);
        expect(result.ctaWidget, isA<CommonButton>());
      },
    );

    test(
      'handle [CommonHttpClient.NO_INTERNET]',
      () {
        // Arrange
        final ErrorUIModel errorUIModel = ErrorUIModel(statusCode: CommonHttpClient.NO_INTERNET);

        // Act
        mockApiErrorHandlerClass.handleDopEvoApiError(errorUIModel, isReplaceCurrentScreen: true);

        // Assert
        final DOPNativeStatusScreenArg result = verify(() =>
            mockNavigatorContext.pushReplacementNamed(Screen.dopNativeStatusScreen.name,
                extra: captureAny(named: 'extra'))).captured.single;

        expect(result.icon, DOPNativeImages.icInternetError);
        expect(result.title, DOPNativeStrings.dopNativeInternetErrorTitle);
        expect(result.description, DOPNativeStrings.dopNativeInternetErrorDesc);
        expect(result.ctaWidget, isA<CommonButton>());
      },
    );

    test(
      'handle [CommonHttpClient.UNKNOWN_ERRORS]',
      () {
        // Arrange
        final ErrorUIModel errorUIModel = ErrorUIModel(
          statusCode: CommonHttpClient.UNKNOWN_ERRORS,
          verdict: 'fake_verdict',
        );

        // Act
        mockApiErrorHandlerClass.handleDopEvoApiError(errorUIModel, isReplaceCurrentScreen: true);

        // Assert
        final DOPNativeStatusScreenArg result = verify(() =>
            mockNavigatorContext.pushReplacementNamed(Screen.dopNativeStatusScreen.name,
                extra: captureAny(named: 'extra'))).captured.single;

        expect(result.icon, DOPNativeImages.icOtpCodeError);
        expect(result.title, DOPNativeStrings.dopNativeCommonErrorTitle);
        expect(result.description, DOPNativeStrings.dopNativeCommonErrorDescription);
        expect(result.ctaWidget, isA<CommonButton>());
        expect(
          result.errorReason,
          'statusCode=${errorUIModel.statusCode}, verdict=${errorUIModel.verdict}',
        );
        expect(result.enableLogging, true);
        expect(
          result.eventTrackingScreenId,
          DOPNativeEventTrackingScreenId.dopNativeCommonErrorScreen,
        );
      },
    );

    test(
      'handle local error statusCode == null',
      () {
        // Arrange
        final ErrorUIModel errorUIModel = ErrorUIModel(verdict: 'fake_verdict');

        // Act
        mockApiErrorHandlerClass.handleDopEvoApiError(
          errorUIModel,
          isReplaceCurrentScreen: true,
        );

        // Assert
        final DOPNativeStatusScreenArg result = verify(
          () => mockNavigatorContext.pushReplacementNamed(
            Screen.dopNativeStatusScreen.name,
            extra: captureAny(
              named: 'extra',
            ),
          ),
        ).captured.single;

        expect(result.icon, DOPNativeImages.icOtpCodeError);
        expect(result.title, DOPNativeStrings.dopNativeCommonErrorTitle);
        expect(result.description, DOPNativeStrings.dopNativeCommonErrorDescription);
        expect(result.ctaWidget, isA<CommonButton>());
        expect(result.errorReason, 'fake_verdict');
        expect(result.enableLogging, true);
        expect(
          result.eventTrackingScreenId,
          DOPNativeEventTrackingScreenId.dopNativeCommonErrorScreen,
        );
      },
    );
  });

  group('verify createCommonErrorArg()', () {
    test('returns correct DOPNativeStatusScreenArg', () {
      // Act
      final DOPNativeStatusScreenArg result = mockApiErrorHandlerClass.createCommonErrorArg(
        reason: 'fake_error_reason',
      );

      // Assert
      expect(result.icon, DOPNativeImages.icOtpCodeError);
      expect(result.title, DOPNativeStrings.dopNativeCommonErrorTitle);
      expect(result.description, DOPNativeStrings.dopNativeCommonErrorDescription);
      expect(result.ctaWidget, isA<CommonButton>());
      expect(result.errorReason, 'fake_error_reason');
      expect(result.enableLogging, true);
      expect(
        result.eventTrackingScreenId,
        DOPNativeEventTrackingScreenId.dopNativeCommonErrorScreen,
      );

      // Check the CommonButton properties
      final CommonButton ctaButton = result.ctaWidget as CommonButton;
      expect((ctaButton.child as Text).data, DOPNativeStrings.dopNativeCommonErrorButtonTitle);

      // Simulate button press and check if callback is called
      // Simulate button press and check if navigateToLandingPage is called
      ctaButton.onPressed!();
      verify(() => mockDOPNativeNavigationUtils.navigateToLandingPage()).called(1);
    });
  });

  group('verify createInternetConnectErrorArg()', () {
    test('returns correct DOPNativeStatusScreenArg', () {
      // Act
      final DOPNativeStatusScreenArg result =
          mockApiErrorHandlerClass.createInternetConnectErrorArg();

      // Assert
      expect(result.icon, DOPNativeImages.icInternetError);
      expect(result.title, DOPNativeStrings.dopNativeInternetErrorTitle);
      expect(result.description, DOPNativeStrings.dopNativeInternetErrorDesc);
      expect(result.ctaWidget, isA<CommonButton>());

      // Check the CommonButton properties
      final CommonButton ctaButton = result.ctaWidget as CommonButton;
      expect((ctaButton.child as Text).data, DOPNativeStrings.dopNativeRetry);

      // Simulate button press and check if navigateToLandingPage is called
      ctaButton.onPressed!();
      verify(() => mockDOPNativeNavigationUtils.navigateToLandingPage()).called(1);
    });
  });

  group('verify gotoLandingPageWithInputPhoneDialog()', () {
    test('should call popUntilToLandingPage and showDOPInputPhoneDialog', () {
      // Arrange
      final DOPNativeState dopNativeState = DOPNativeState(phoneNumber: 'test_phone');
      when(() => mockAppState.dopNativeState).thenReturn(dopNativeState);

      // Act
      mockApiErrorHandlerClass.gotoLandingPageWithInputPhoneDialog();

      // Assert
      verify(() => mockDOPNativeNavigationUtils.popUntilToLandingPage()).called(1);
      verify(
        () => mockDopUtilFunction.showDOPInputPhoneDialog(
          phoneNumber: 'test_phone',
        ),
      ).called(1);
    });
  });

  group('verify gotoLandingPageWithUniqueToken()', () {
    test('should call navigateToLandingPage with uniqueToken', () {
      // Arrange
      final MockDeepLinkSharedData mockDeepLinkSharedData = MockDeepLinkSharedData();
      when(() => mockAppState.deepLinkSharedData).thenReturn(mockDeepLinkSharedData);
      when(() => mockDeepLinkSharedData.dopUniqueToken).thenReturn('test_unique_token');

      // Act
      mockApiErrorHandlerClass.gotoLandingPageWithUniqueToken();

      // Assert
      verify(
        () => mockDOPNativeNavigationUtils.navigateToLandingPage(
          arg: any(
            named: 'arg',
            that: predicate(
              (DOPNativeIntroductionScreenArg arg) => arg.uniqueToken == 'test_unique_token',
            ),
          ),
        ),
      );
    });

    test('should call navigateToLandingPage with null uniqueToken when dopUniqueToken is null', () {
      // Arrange
      final MockDeepLinkSharedData mockDeepLinkSharedData = MockDeepLinkSharedData();
      when(() => mockAppState.deepLinkSharedData).thenReturn(mockDeepLinkSharedData);
      when(() => mockDeepLinkSharedData.dopUniqueToken).thenReturn(null);

      // Act
      mockApiErrorHandlerClass.gotoLandingPageWithUniqueToken();

      // Assert
      verify(
        () => mockDOPNativeNavigationUtils.navigateToLandingPage(
          arg: any(
            named: 'arg',
            that: predicate(
              (DOPNativeIntroductionScreenArg arg) => arg.uniqueToken == null,
            ),
          ),
        ),
      );
    });
  });

  group('verify createInvalidTokenArg()', () {
    test('returns correct DOPNativeStatusScreenArg with handle onCTAPressed correct for SMS OTP',
        () {
      // Arrange
      final DOPNativeBootstrapAuthSettingsEntity mockBootstrapAuthSettings =
          DOPNativeBootstrapAuthSettingsEntity(
        // Initialize with your required properties
        authType: 'otp',
      );
      // Initialize with your required properties
      final DOPNativeState dopNativeState = DOPNativeState(
        bootstrapAuthSettings: mockBootstrapAuthSettings,
      );
      when(() => mockAppState.dopNativeState).thenReturn(dopNativeState);

      // Act
      final DOPNativeStatusScreenArg result = mockApiErrorHandlerClass.createInvalidTokenArg();

      // Assert
      expect(result.icon, DOPNativeImages.icSandClock);
      expect(result.title, DOPNativeStrings.dopNativeInvalidTokenTitle);
      expect(result.description, DOPNativeStrings.dopNativeInvalidTokenDesc);
      expect(result.ctaWidget, isA<CommonButton>());

      // Check the CommonButton properties
      final CommonButton ctaButton = result.ctaWidget as CommonButton;
      expect((ctaButton.child as Text).data, DOPNativeStrings.dopNativeInvalidTokenButtonTitle);

      // Simulate button press and check if token is cleared and navigation is called
      ctaButton.onPressed!();
      verify(() => mockDopUtilFunction.clearDOPNativeAccessTokenHeader()).called(1);

      verify(() => mockDOPNativeNavigationUtils.popUntilToLandingPage()).called(1);

      verify(() => mockDopUtilFunction.showDOPInputPhoneDialog()).called(1);

      verify(
        () => mockNavigatorContext.pushNamed(
          Screen.dopNativeVerifyOtpScreen.name,
          extra: any(named: 'extra'),
        ),
      ).called(1);
    });

    test('returns correct DOPNativeStatusScreenArg with handle onCTAPressed correct for Face OTP',
        () {
      // Arrange
      final DOPNativeBootstrapAuthSettingsEntity mockBootstrapAuthSettings =
          DOPNativeBootstrapAuthSettingsEntity(
        authType: 'face_id',
      );
      final DOPNativeState dopNativeState = DOPNativeState(
        bootstrapAuthSettings: mockBootstrapAuthSettings,
      );
      when(() => mockAppState.dopNativeState).thenReturn(dopNativeState);

      // Act
      final DOPNativeStatusScreenArg result = mockApiErrorHandlerClass.createInvalidTokenArg();

      // Assert
      expect(result.icon, DOPNativeImages.icSandClock);
      expect(result.title, DOPNativeStrings.dopNativeInvalidTokenTitle);
      expect(result.description, DOPNativeStrings.dopNativeInvalidTokenDesc);
      expect(result.ctaWidget, isA<CommonButton>());

      // Check the CommonButton properties
      final CommonButton ctaButton = result.ctaWidget as CommonButton;
      expect((ctaButton.child as Text).data, DOPNativeStrings.dopNativeInvalidTokenButtonTitle);

      // Simulate button press and check if token is cleared and navigation is called
      ctaButton.onPressed!();
      verify(() => mockDopUtilFunction.clearDOPNativeAccessTokenHeader()).called(1);

      verify(() => mockDOPNativeNavigationUtils.popUntilToLandingPage()).called(1);

      verify(() => mockDopUtilFunction.showDOPInputPhoneDialog()).called(1);

      verify(
        () => mockNavigatorContext.pushNamed(
          Screen.dopNativeFaceOtpIntroductionScreen.name,
        ),
      ).called(1);
    });

    test(
        'returns correct DOPNativeStatusScreenArg with handle onCTAPressed correct for ID Card Auth',
        () {
      // Arrange
      final DOPNativeBootstrapAuthSettingsEntity mockBootstrapAuthSettings =
          DOPNativeBootstrapAuthSettingsEntity(
        authType: 'id_card_auth',
      );
      final DOPNativeState dopNativeState = DOPNativeState(
        bootstrapAuthSettings: mockBootstrapAuthSettings,
      );
      when(() => mockAppState.dopNativeState).thenReturn(dopNativeState);

      // Mock DeepLinkSharedData for uniqueToken
      final MockDeepLinkSharedData mockDeepLinkSharedData = MockDeepLinkSharedData();
      when(() => mockAppState.deepLinkSharedData).thenReturn(mockDeepLinkSharedData);
      when(() => mockDeepLinkSharedData.dopUniqueToken).thenReturn('test_unique_token');

      // Act
      final DOPNativeStatusScreenArg result = mockApiErrorHandlerClass.createInvalidTokenArg();

      // Assert
      expect(result.icon, DOPNativeImages.icSandClock);
      expect(result.title, DOPNativeStrings.dopNativeInvalidTokenTitle);
      expect(result.description, DOPNativeStrings.dopNativeInvalidTokenDesc);
      expect(result.ctaWidget, isA<CommonButton>());

      // Check the CommonButton properties
      final CommonButton ctaButton = result.ctaWidget as CommonButton;
      expect((ctaButton.child as Text).data, DOPNativeStrings.dopNativeInvalidTokenButtonTitle);

      // Simulate button press and check if token is cleared and navigation is called
      ctaButton.onPressed!();
      verify(() => mockDopUtilFunction.clearDOPNativeAccessTokenHeader()).called(1);

      // Verify that gotoLandingPageWithUniqueToken is called
      verify(
        () => mockDOPNativeNavigationUtils.navigateToLandingPage(
          arg: any(
            named: 'arg',
            that: predicate(
              (DOPNativeIntroductionScreenArg arg) => arg.uniqueToken == 'test_unique_token',
            ),
          ),
        ),
      );
    });
  });

  group('verify gotoLandingPageWithInputPhoneDialog()', () {
    test('verify gotoLandingPageWithInputPhoneDialog()', () {
      final DOPNativeState dopNativeState = DOPNativeState(
        phoneNumber: '1234567890',
      );
      when(() => mockAppState.dopNativeState).thenReturn(dopNativeState);

      mockApiErrorHandlerClass.gotoLandingPageWithInputPhoneDialog();

      verify(() => mockDOPNativeNavigationUtils.popUntilToLandingPage()).called(1);
      expect(
          verify(() => mockDopUtilFunction.showDOPInputPhoneDialog(
              phoneNumber: captureAny(named: 'phoneNumber'))).captured,
          <String>['1234567890']);
    });
  });
}
