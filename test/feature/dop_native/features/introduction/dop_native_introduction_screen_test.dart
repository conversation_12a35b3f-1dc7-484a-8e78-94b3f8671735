import 'dart:async';

import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_bootstrap_auth_settings_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_data_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_registration_campaign_entity.dart';
import 'package:evoapp/feature/deep_link/deep_link_utils.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state_cubit.dart';
import 'package:evoapp/feature/dop_native/base/dop_native_page_state_base.dart';
import 'package:evoapp/feature/dop_native/dialogs/input_phone_number/dop_native_input_phone_dialog_controller.dart';
import 'package:evoapp/feature/dop_native/features/introduction/cubit/dop_native_introduction_cubit.dart';
import 'package:evoapp/feature/dop_native/features/introduction/dop_native_introduction_screen.dart';
import 'package:evoapp/feature/dop_native/features/introduction/sub_introduction/dop_native_sub_introduction_screen.dart';
import 'package:evoapp/feature/dop_native/features/introduction/widgets/dop_native_main_introduction_webview.dart';
import 'package:evoapp/feature/dop_native/features/introduction/widgets/sub_introduction_widget.dart';
import 'package:evoapp/feature/dop_native/features/verify_otp/dop_native_verify_otp_screen.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_website_url.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/dop_native/widgets/appbar/dop_native_appbar_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';

// ignore: depend_on_referenced_packages
import 'package:flutter_inappwebview_android/flutter_inappwebview_android.dart';
import '../../../../util/flutter_test_config.dart';

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

class MockDeepLinkUtils extends Mock implements DeepLinkUtils {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockDOPNativeApplicationStateCubit extends Mock implements DOPNativeApplicationStateCubit {}

class MockDOPNativeIntroductionCubit extends Mock implements DOPNativeIntroductionCubit {}

class TestDOPNativeIntroductionScreen extends DOPNativeIntroductionScreen {
  final DOPNativeIntroductionCubit cubit;
  final DOPNativeApplicationStateCubit applicationStateCubit;

  const TestDOPNativeIntroductionScreen({
    required this.cubit,
    required this.applicationStateCubit,
    super.arg,
    super.key,
  });

  @override
  TestDOPNativeIntroductionScreenState createState() =>
      // ignore: no_logic_in_create_state
      TestDOPNativeIntroductionScreenState(
        dopNativeApplicationStateCubit: applicationStateCubit,
        cubit: cubit,
      );
}

class TestDOPNativeIntroductionScreenState extends DOPNativeIntroductionScreenState {
  bool isHandleEvoApiErrorCalled = false;

  @override
  // ignore: overridden_fields
  final DOPNativeIntroductionCubit cubit;
  @override
  // ignore: overridden_fields
  final DOPNativeApplicationStateCubit dopNativeApplicationStateCubit;

  TestDOPNativeIntroductionScreenState({
    required this.cubit,
    required this.dopNativeApplicationStateCubit,
  });

  @override
  double getStatusBarHeight() => 0;

  @override
  Future<void> handleEvoApiError(
    ErrorUIModel? errorUIModel, {
    bool isReplaceCurrentScreen = false,
  }) async {
    isHandleEvoApiErrorCalled = true;
  }
}

void main() {
  late CommonImageProvider mockCommonImageProvider;
  late LoggingRepo mockLoggingRepo;
  late EvoFlutterWrapper mockEvoFlutterWrapper;
  late CommonNavigatorObserver mockNavigatorObserver;
  late DOPNativeRepo mockDOPNativeRepo;
  late DeepLinkUtils mockDeepLinkUtils;
  late DevicePlatform devicePlatform;
  late DOPNativeIntroductionCubit mockCubit;
  late DOPNativeApplicationStateCubit mockApplicationStateCubit;
  late DOPUtilFunctions mockDOPUtilFunctions;

  final StreamController<DOPNativeIntroductionState> introductionStreamController =
      StreamController<DOPNativeIntroductionState>.broadcast();
  final StreamController<DOPNativeApplicationState> applicationStateStreamController =
      StreamController<DOPNativeApplicationState>.broadcast();

  const MethodChannel channel = MethodChannel('flutter/platform_views');

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    AndroidInAppWebViewPlatform.registerWith();
    initConfigEvoPageStateBase();
    setUtilsMockInstanceForTesting();
    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => MockEvoFlutterWrapper());
    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());
    getIt.registerLazySingleton<AuthenticationRepo>(() => MockAuthenticationRepo());
    mockEvoFlutterWrapper = getIt.get<EvoFlutterWrapper>();
    mockNavigatorObserver = getIt.get<CommonNavigatorObserver>();

    getIt.registerLazySingleton<DOPNativeRepo>(() => MockDOPNativeRepo());
    mockDOPNativeRepo = getIt.get<DOPNativeRepo>();

    mockLoggingRepo = getIt.get<LoggingRepo>();

    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerSingleton<DOPUtilFunctions>(MockDOPUtilFunctions());
    mockDOPUtilFunctions = getIt.get<DOPUtilFunctions>();

    getIt.registerLazySingleton<DeepLinkUtils>(() => MockDeepLinkUtils());
    mockDeepLinkUtils = getIt.get<DeepLinkUtils>();

    devicePlatform = getIt.registerSingleton(MockDevicePlatform());

    mockApplicationStateCubit = MockDOPNativeApplicationStateCubit();
    mockCubit = MockDOPNativeIntroductionCubit();

    when(
      () => mockCommonImageProvider.asset(
        captureAny(),
        width: captureAny<double?>(named: 'width'),
        height: captureAny<double?>(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        cacheWidth: any(named: 'cacheWidth'),
        cacheHeight: any(named: 'cacheHeight'),
        package: any(named: 'package'),
      ),
    ).thenAnswer(
      (Invocation innovation) => Container(
        key: Key(innovation.positionalArguments[0]),
        width: innovation.positionalArguments.elementAtOrNull(1) as double?,
        height: innovation.positionalArguments.elementAtOrNull(2) as double?,
      ),
    );

    when(() => mockLoggingRepo.logEvent(
          eventType: EventType.userAction,
          data: any(named: 'data'),
        )).thenAnswer((_) => Future<void>.value());

    when(() => mockEvoFlutterWrapper.showDialog<void>(
          builder: any(named: 'builder'),
          useSafeArea: any(named: 'useSafeArea'),
          barrierDismissible: any(named: 'barrierDismissible'),
        )).thenAnswer((_) {
      return Future<void>.value();
    });

    when(() => mockNavigatorObserver.topStackIsAPageRoute()).thenAnswer((_) {
      return true;
    });

    when(() => mockDOPNativeRepo.registrationCampaign(
          campaignCode: any(named: 'campaignCode'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) {
      return Future<DOPNativeRegistrationCampaignEntity>.value(
          DOPNativeRegistrationCampaignEntity());
    });

    when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        return <dynamic, dynamic>{};
      },
    );
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    when(() => EvoUiUtils().calculateHorizontalSpace(
          context: any(named: 'context'),
          widthPercentage: any(named: 'widthPercentage'),
        )).thenReturn(10);

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(10);

    when(() => mockApplicationStateCubit.stream).thenAnswer(
      (_) => applicationStateStreamController.stream,
    );
    when(() => mockApplicationStateCubit.state).thenAnswer(
      (_) => DOPNativeApplicationStateInitial(),
    );
    when(() => mockApplicationStateCubit.close()).thenAnswer((_) async {});

    when(() => mockCubit.stream).thenAnswer((_) => introductionStreamController.stream);
    when(() => mockCubit.state).thenAnswer((_) => DOPNativeIntroductionInitial());
    when(() => mockCubit.close()).thenAnswer((_) async {});
  });

  tearDown(() {
    reset(mockCubit);
    reset(mockApplicationStateCubit);
    reset(mockDOPUtilFunctions);
  });

  tearDownAll(() {
    introductionStreamController.close();
    applicationStateStreamController.close();

    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  group('DOPNativeIntroductionScreen navigation tests', () {
    final String screenName = Screen.dopNativeIntroductionScreen.name;

    test('pushNamed pushes the correct route', () {
      DOPNativeIntroductionScreen.pushNamed();

      verify(
        () => mockNavigatorContext.pushNamed(
          screenName,
          extra: captureAny(named: 'extra'),
        ),
      ).called(1);
    });
  });

  void verifyConstants(DOPNativeIntroductionScreenState state) {
    expect(state.titleTopPercentage, 100.62 / 812);
    expect(state.buttonTopPercentage, 27.04 / 812);
    expect(state.cashBackToButtonTopPercentage, 187.65 / 812);
    expect(state.titleFooterPercentage, 33 / 812);
    expect(state.buttonFooterPercentage, 15 / 812);
    expect(state.cardFooterPercentage, 22 / 812);

    expect(state.scrollController, isA<ScrollController>());
    expect(state.cubit, isA<DOPNativeIntroductionCubit>());
  }

  void verifyAppBar(WidgetTester tester) {
    final Finder dopNativeAppBarFinder = find.byType(DOPNativeAppBar);
    expect(dopNativeAppBarFinder, findsOneWidget);
  }

  void verifyBackgroundScreen(WidgetTester tester) {
    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.bgDOPNativeIntroduction,
          width: double.infinity,
          fit: BoxFit.fitWidth,
        )).called(1);
  }

  void verifyHeaderScreen(WidgetTester tester) {
    // header TPBank
    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgHeaderTPBank,
          width: double.infinity,
          fit: BoxFit.fitWidth,
        )).called(1);
  }

  Future<void> verifyContentScreen(WidgetTester tester) async {
    final Finder scrollable = find.byType(Scrollable);
    Future<void> scrollUntil(dynamic finder) async {
      await tester.scrollUntilVisible(finder, 500, scrollable: scrollable);
    }

    Future<void> verifyOpenEvoCardButton() async {
      final Finder createEVOCardTextButtonFinder = find.text(DOPNativeStrings.dopNativeOpenEVOCard);
      await scrollUntil(createEVOCardTextButtonFinder);
      expect(createEVOCardTextButtonFinder, findsNWidgets(1));
    }

    await verifyOpenEvoCardButton();

    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgTitleTopOfIntroduction,
          fit: BoxFit.fill,
        )).called(1);

    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgCashBack10PercentBanner,
          width: double.infinity,
          fit: BoxFit.fitWidth,
        )).called(1);
    expect(
      find.byWidgetPredicate((Widget widget) {
        return widget is SubIntroductionWidget &&
            widget.url == DOPNativeWebsiteUrl.tpbEvoSpecialOfferUrl &&
            widget.showFooter;
      }),
      findsOneWidget,
    );

    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgCashBackNoLimitBanner,
          width: double.infinity,
          fit: BoxFit.fitWidth,
        )).called(1);
    expect(
      find.byWidgetPredicate((Widget widget) {
        return widget is SubIntroductionWidget &&
            widget.url == DOPNativeWebsiteUrl.cashbackProgramUrl &&
            widget.showFooter;
      }),
      findsOneWidget,
    );

    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgNoUseInformationBanner,
          width: double.infinity,
          fit: BoxFit.fitWidth,
        )).called(1);

    Finder finder = find.byWidgetPredicate((Widget widget) {
      return widget is SubIntroductionWidget &&
          widget.url == DOPNativeWebsiteUrl.registerAndUseUrl &&
          widget.showFooter;
    });
    await scrollUntil(finder);
    expect(finder, findsOneWidget);
    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgUseEVOCardBanner,
          width: double.infinity,
          fit: BoxFit.fitWidth,
        )).called(1);

    finder = find.byWidgetPredicate((Widget widget) {
      return widget is SubIntroductionWidget &&
          widget.url == DOPNativeWebsiteUrl.emiProgramUrl &&
          widget.showFooter;
    });
    await scrollUntil(finder);
    expect(finder, findsOneWidget);
    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgEMI0PercentBanner,
          width: double.infinity,
          fit: BoxFit.fitWidth,
        )).called(1);

    finder = find.byWidgetPredicate((Widget widget) {
      return widget is SubIntroductionWidget &&
          widget.url == DOPNativeWebsiteUrl.freeAnnualFeeUrl &&
          widget.showFooter;
    });
    await scrollUntil(finder);
    expect(finder, findsOneWidget);
    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgFreeAnnualFeeBanner,
          width: double.infinity,
          fit: BoxFit.fitWidth,
        )).called(1);

    finder = find.byWidgetPredicate((Widget widget) {
      return widget is SubIntroductionWidget &&
          widget.url == DOPNativeWebsiteUrl.freeInterestUrl &&
          widget.showFooter;
    });
    await scrollUntil(finder);
    expect(finder, findsOneWidget);
    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgFreeUpToBanner,
          width: double.infinity,
          fit: BoxFit.fitWidth,
        )).called(1);

    // voucher
    final Finder dopNativeWebView = find.byType(DOPNativeCampaignOnMainIntroductionWebView);
    await scrollUntil(dopNativeWebView);
    expect(dopNativeWebView, findsOneWidget);

    await verifyOpenEvoCardButton();

    // footer
    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgTitleIntroductionFooter,
          fit: BoxFit.fill,
        )).called(1);

    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgCardIntroductionFooter,
          fit: BoxFit.fill,
        )).called(1);

    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.icScrollToTopIntroduction,
          width: 43,
        )).called(1);

    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgSupportIconFooter,
        )).called(1);

    finder = find.byWidgetPredicate((Widget widget) {
      return widget is SubIntroductionWidget &&
          widget.url == DOPNativeWebsiteUrl.introductionUrl &&
          widget.showFooter == false;
    });
    await scrollUntil(finder);
    expect(finder, findsOneWidget);
    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgIntroIconFooter,
        )).called(1);

    finder = find.byWidgetPredicate((Widget widget) {
      return widget is SubIntroductionWidget &&
          widget.url == DOPNativeWebsiteUrl.faqUrl &&
          widget.showFooter == false;
    });
    await scrollUntil(finder);
    expect(finder, findsOneWidget);
    verify(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgQuestionIconFooter,
        )).called(1);
  }

  Future<void> pumpScreen(
    WidgetTester tester, {
    required DOPNativeIntroductionCubit cubit,
    required DOPNativeApplicationStateCubit applicationStateCubit,
    DOPNativeIntroductionScreenArg? arg,
  }) async {
    final Widget dopNativeIntroductionScreen = MaterialApp(
      navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
      scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
      home: Scaffold(
        body: Builder(builder: (BuildContext context) {
          return TestDOPNativeIntroductionScreen(
            arg: arg,
            applicationStateCubit: applicationStateCubit,
            cubit: cubit,
          );
        }),
      ),
    );
    final MediaQuery mediaQuery = MediaQuery(
      data: const MediaQueryData(size: Size(375, 812)),
      child: dopNativeIntroductionScreen,
    );

    when(() => mockNavigatorContext.widget).thenReturn(mediaQuery);
    when(() => mockNavigatorContext.dependOnInheritedWidgetOfExactType<MediaQuery>())
        .thenReturn(mediaQuery);
    when(() => mockDeepLinkUtils.removeEvoDeepLinkKeysFromMap(any()))
        .thenReturn(<String, String>{});
    when(() => devicePlatform.isIOS()).thenReturn(true);

    await tester.runAsync(() async {
      await tester.pumpWidget(mediaQuery);
    });
  }

  group('verify DOPNativeIntroductionScreen', () {
    testWidgets('should render DOPNativeIntroductionScreen', (WidgetTester tester) async {
      when(() => mockCubit.initToRegistrationCampaign(campaignCode: any(named: 'campaignCode')))
          .thenAnswer((_) async {});

      await pumpScreen(tester, applicationStateCubit: mockApplicationStateCubit, cubit: mockCubit);

      final State state = tester.state(
        find.byWidgetPredicate((Widget widget) => widget is DOPNativeIntroductionScreen),
      );
      expect(state is DOPNativeIntroductionScreenState, true);

      final DOPNativeIntroductionScreenState dopNativeIntroductionScreenState =
          state as DOPNativeIntroductionScreenState;
      verifyConstants(dopNativeIntroductionScreenState);

      state.hideDOPLoading();

      verifyAppBar(tester);
      verifyBackgroundScreen(tester);
      verifyHeaderScreen(tester);

      await verifyContentScreen(tester);
    });

    testWidgets('should handle event DOPNativeIntroductionScreen', (WidgetTester tester) async {
      final Finder scrollable = find.byType(Scrollable);
      Future<void> scrollUntil(dynamic finder) async {
        await tester.scrollUntilVisible(finder, 500, scrollable: scrollable);
      }

      Finder finder;

      when(() => mockCubit.initToRegistrationCampaign(campaignCode: any(named: 'campaignCode')))
          .thenAnswer((_) async {});
      when(() => mockCubit.requestToDialPhoneNumber(ContactInfo.dopSupportPhone))
          .thenAnswer((_) async {});

      await pumpScreen(tester, applicationStateCubit: mockApplicationStateCubit, cubit: mockCubit);

      final DOPNativeIntroductionScreenState state = tester.state(
        find.byWidgetPredicate((Widget widget) => widget is DOPNativeIntroductionScreen),
      );

      state.hideDOPLoading();
      expect(state.scrollController.offset, 0);

      // CTA button
      finder = find.byType(CommonButton);
      await scrollUntil(finder);
      expect(finder, findsOneWidget);
      await tester.tap(finder);
      verify(() => mockDOPUtilFunctions.showDOPInputPhoneDialog()).called(1);

      // voucher widget
      finder = find.byType(DOPNativeCampaignOnMainIntroductionWebView);
      await scrollUntil(finder);
      expect(finder, findsOneWidget);

      final DOPNativeCampaignOnMainIntroductionWebView webView =
          finder.evaluate().first.widget as DOPNativeCampaignOnMainIntroductionWebView;
      webView.onClickUrl.call('fake_url');
      expect(
        verify(
          () => mockNavigatorContext.pushNamed(
            Screen.dopNativeSubIntroductionScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured,
        <dynamic>[
          isA<DOPNativeSubIntroductionScreenArg>().having(
            (DOPNativeSubIntroductionScreenArg p0) => p0.url,
            'test url',
            'fake_url',
          ),
        ],
      );

      webView.onLoaded.call();
      // just log, nothing to test
      webView.onError.call('fake_error_message');
      // just log, nothing to test

      // call phone button
      finder = find.ancestor(
        of: find.byKey(Key(DOPNativeImages.imgSupportIconFooter)),
        matching: find.byType(InkWell),
      );
      await scrollUntil(finder);
      expect(finder, findsOneWidget);

      final InkWell inkWellButton = finder.evaluate().first.widget as InkWell;
      inkWellButton.onTap?.call();
      verify(() => mockCubit.requestToDialPhoneNumber(ContactInfo.dopSupportPhone)).called(1);

      // scroll to top button
      finder = find.ancestor(
        of: find.byKey(Key(DOPNativeImages.icScrollToTopIntroduction)),
        matching: find.byType(InkWell),
      );
      await scrollUntil(finder);
      expect(finder, findsOneWidget);
      await tester.tap(finder);
      await tester.pumpAndSettle();
      expect(state.scrollController.offset, 0);
    });
  });

  group('verify initState', () {
    testWidgets('dopNativeDataEntity != null', (WidgetTester tester) async {
      when(() => mockApplicationStateCubit.getApplicationState()).thenAnswer((_) async {});

      final DOPNativeDataEntity entity = DOPNativeDataEntity();
      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(dopNativeDataEntity: entity),
      );

      verify(() => mockApplicationStateCubit.getApplicationState()).called(1);
      verify(() => mockCubit.continueDOEJourney(entity)).called(1);
    });

    testWidgets('unique token != null', (WidgetTester tester) async {
      when(() => mockCubit.initWithUniqueToken('unique_token')).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(uniqueToken: 'unique_token'),
      );

      verify(() => mockCubit.initWithUniqueToken('unique_token')).called(1);
    });

    testWidgets('normal', (WidgetTester tester) async {
      when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(),
      );

      verify(() => mockCubit.initToRegistrationCampaign()).called(1);
    });

    testWidgets('initialize DOPNativeInputPhoneDialogController', (WidgetTester tester) async {
      when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});
      when(
        () => mockCubit.getBootstrap(
          'fake_unique_token',
          logEvent: any(named: 'logEvent'),
          hideInputPhoneDialogLoading: any(named: 'hideInputPhoneDialogLoading'),
        ),
      ).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(),
      );
      DOPNativeInputPhoneDialogController().onRegisterSuccess?.call(
            uniqueToken: 'fake_unique_token',
            hideInputPhoneDialogLoading: () {},
            onLogEvent: () {},
          );

      verify(
        () => mockCubit.getBootstrap(
          'fake_unique_token',
          logEvent: any(named: 'logEvent'),
          hideInputPhoneDialogLoading: any(named: 'hideInputPhoneDialogLoading'),
        ),
      ).called(1);
    });
  });

  group('handle listener', () {
    testWidgets('DOPNativeIntroductionLoading', (WidgetTester tester) async {
      when(() => mockApplicationStateCubit.getApplicationState()).thenAnswer((_) async {});
      when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(),
      );

      final DOPNativeIntroductionScreenState state = tester.state(
        find.byWidgetPredicate((Widget widget) => widget is DOPNativeIntroductionScreen),
      );

      introductionStreamController.add(DOPNativeIntroductionLoading());
      await tester.pump();
      expect(state.loadingNotifier.value, true);
    });

    testWidgets('RegistrationCampaignSuccess', (WidgetTester tester) async {
      when(() => mockApplicationStateCubit.getApplicationState()).thenAnswer((_) async {});
      when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(),
      );

      final DOPNativeIntroductionScreenState state = tester.state(
        find.byWidgetPredicate((Widget widget) => widget is DOPNativeIntroductionScreen),
      );

      // RegistrationCampaignSuccess
      introductionStreamController.add(
        RegistrationCampaignSuccess(DOPNativeRegistrationCampaignEntity()),
      );
      await tester.pump();
      expect(state.loadingNotifier.value, false);
      verify(
        () => mockDOPUtilFunctions.showDOPInputPhoneDialog(
          autoRequestOTP: any(named: 'autoRequestOTP'),
          phoneNumber: any(named: 'phoneNumber'),
        ),
      ).called(1);
    });

    testWidgets('RegistrationCampaignFail', (WidgetTester tester) async {
      when(() => mockApplicationStateCubit.getApplicationState()).thenAnswer((_) async {});
      when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(),
      );

      final DOPNativeIntroductionScreenState state = tester.state(
        find.byWidgetPredicate((Widget widget) => widget is DOPNativeIntroductionScreen),
      );

      introductionStreamController.add(RegistrationCampaignFail(error: ErrorUIModel()));
      await tester.pump();
      expect(state.loadingNotifier.value, false);
      verify(
        () => mockDOPUtilFunctions.showDOPInputPhoneDialog(
          autoRequestOTP: any(named: 'autoRequestOTP'),
          phoneNumber: any(named: 'phoneNumber'),
        ),
      ).called(1);
    });

    group('DOPNativeBootstrapAuthSettingsLoaded', () {
      testWidgets('smsOTP', (WidgetTester tester) async {
        bool isCallbackCalled = false;
        void callback() {
          isCallbackCalled = true;
        }

        when(() => mockApplicationStateCubit.getApplicationState()).thenAnswer((_) async {});
        when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

        await pumpScreen(
          tester,
          applicationStateCubit: mockApplicationStateCubit,
          cubit: mockCubit,
          arg: DOPNativeIntroductionScreenArg(),
        );

        final DOPNativeIntroductionScreenState state = tester.state(
          find.byWidgetPredicate((Widget widget) => widget is DOPNativeIntroductionScreen),
        );

        introductionStreamController.add(
          DOPNativeBootstrapAuthSettingsLoaded(
            DOPNativeBootstrapAuthSettingsEntity(authType: 'otp'),
            callback,
          ),
        );
        await tester.pump();
        expect(state.loadingNotifier.value, false);
        expect(isCallbackCalled, true);
        expect(
          verify(
            () => mockNavigatorContext.pushNamed(
              Screen.dopNativeVerifyOtpScreen.name,
              extra: captureAny(named: 'extra'),
            ),
          ).captured,
          <dynamic>[
            isA<DOPNativeVerifyOtpScreenArg>().having(
              (DOPNativeVerifyOtpScreenArg p0) => p0.verifyOtpType,
              'test verifyOtpType',
              DOPNativeVerifyOtpType.signIn,
            ),
          ],
        );
      });

      testWidgets('faceOTP', (WidgetTester tester) async {
        bool isCallbackCalled = false;
        void callback() {
          isCallbackCalled = true;
        }

        when(() => mockApplicationStateCubit.getApplicationState()).thenAnswer((_) async {});
        when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

        await pumpScreen(
          tester,
          applicationStateCubit: mockApplicationStateCubit,
          cubit: mockCubit,
          arg: DOPNativeIntroductionScreenArg(),
        );

        final DOPNativeIntroductionScreenState state = tester.state(
          find.byWidgetPredicate((Widget widget) => widget is DOPNativeIntroductionScreen),
        );

        introductionStreamController.add(
          DOPNativeBootstrapAuthSettingsLoaded(
            DOPNativeBootstrapAuthSettingsEntity(authType: 'face_id'),
            callback,
          ),
        );
        await tester.pump();
        expect(state.loadingNotifier.value, false);
        expect(isCallbackCalled, true);
        verify(
          () => mockNavigatorContext.pushNamed(
            Screen.dopNativeFaceOtpIntroductionScreen.name,
            extra: any(named: 'extra'),
          ),
        ).called(1);
      });

      testWidgets('e-sign OTP', (WidgetTester tester) async {
        bool isCallbackCalled = false;
        void callback() {
          isCallbackCalled = true;
        }

        when(() => mockApplicationStateCubit.getApplicationState()).thenAnswer((_) async {});
        when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

        await pumpScreen(
          tester,
          applicationStateCubit: mockApplicationStateCubit,
          cubit: mockCubit,
          arg: DOPNativeIntroductionScreenArg(),
        );

        final DOPNativeIntroductionScreenState state = tester.state(
          find.byWidgetPredicate((Widget widget) => widget is DOPNativeIntroductionScreen),
        );

        introductionStreamController.add(
          DOPNativeBootstrapAuthSettingsLoaded(
            DOPNativeBootstrapAuthSettingsEntity(authType: 'esign.otp'),
            callback,
          ),
        );
        await tester.pump();
        expect(state.loadingNotifier.value, false);
        expect(isCallbackCalled, true);
        expect(
          verify(
            () => mockNavigatorContext.pushNamed(
              Screen.dopNativeVerifyOtpScreen.name,
              extra: captureAny(named: 'extra'),
            ),
          ).captured,
          <dynamic>[
            isA<DOPNativeVerifyOtpScreenArg>().having(
              (DOPNativeVerifyOtpScreenArg p0) => p0.verifyOtpType,
              'test verifyOtpType',
              DOPNativeVerifyOtpType.eSign,
            ),
          ],
        );
      });

      testWidgets('id_card_auth', (WidgetTester tester) async {
        bool isCallbackCalled = false;
        void callback() {
          isCallbackCalled = true;
        }

        when(() => mockApplicationStateCubit.getApplicationState()).thenAnswer((_) async {});
        when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

        await pumpScreen(
          tester,
          applicationStateCubit: mockApplicationStateCubit,
          cubit: mockCubit,
          arg: DOPNativeIntroductionScreenArg(),
        );

        final DOPNativeIntroductionScreenState state = tester.state(
          find.byWidgetPredicate((Widget widget) => widget is DOPNativeIntroductionScreen),
        );

        introductionStreamController.add(
          DOPNativeBootstrapAuthSettingsLoaded(
            DOPNativeBootstrapAuthSettingsEntity(authType: 'id_card_auth'),
            callback,
          ),
        );
        await tester.pump();
        expect(state.loadingNotifier.value, false);
        expect(isCallbackCalled, true);
        verify(
          () => mockApplicationStateCubit.getApplicationState(),
        ).called(1);
      });
    });

    testWidgets('DOPNativeGetBootstrapFailed', (WidgetTester tester) async {
      bool isCallbackCalled = false;
      void callback() {
        isCallbackCalled = true;
      }

      when(() => mockApplicationStateCubit.getApplicationState()).thenAnswer((_) async {});
      when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(),
      );

      final TestDOPNativeIntroductionScreenState state = tester.state(
        find.byWidgetPredicate((Widget widget) => widget is DOPNativeIntroductionScreen),
      );

      introductionStreamController.add(
        DOPNativeGetBootstrapFailed(
          error: ErrorUIModel(),
          hideInputPhoneDialogLoading: callback,
        ),
      );
      await tester.pump();
      expect(state.loadingNotifier.value, false);
      expect(isCallbackCalled, true);
      expect(state.isHandleEvoApiErrorCalled, true);
    });

    testWidgets('DialPhoneNumberLaunchSucceed', (WidgetTester tester) async {
      when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(),
      );

      introductionStreamController.add(DialPhoneNumberLaunchSucceed());
      // just log, nothing to test
    });

    testWidgets('DialPhoneNumberLaunchFailed', (WidgetTester tester) async {
      when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(),
      );

      introductionStreamController.add(DialPhoneNumberLaunchFailed());
      // just log, nothing to test
    });

    testWidgets('DialPhoneNumberCanNotLaunch', (WidgetTester tester) async {
      when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(),
      );

      introductionStreamController.add(DialPhoneNumberCanNotLaunch());
      // just log, nothing to test
    });

    testWidgets('DialPhoneNumberLaunchError', (WidgetTester tester) async {
      when(() => mockCubit.initToRegistrationCampaign()).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        applicationStateCubit: mockApplicationStateCubit,
        cubit: mockCubit,
        arg: DOPNativeIntroductionScreenArg(),
      );

      introductionStreamController.add(DialPhoneNumberLaunchError());
      // just log, nothing to test
    });
  });

  test('verify Widget createState', () {
    final DOPNativeIntroductionScreen widget = DOPNativeIntroductionScreen();
    final DOPNativePageStateBase<DOPNativeIntroductionScreen> state = widget.createState();

    expect(state, isA<DOPNativePageStateBase<DOPNativeIntroductionScreen>>());
  });
}
