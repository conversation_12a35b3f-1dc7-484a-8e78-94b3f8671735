import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_bootstrap_auth_settings_entity.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state_cubit.dart';
import 'package:evoapp/feature/dop_native/features/collect_location/handler/dop_native_collect_location_handler.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_card_success/dop_native_id_success_screen.dart';
import 'package:evoapp/feature/dop_native/models/dop_native_state.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_resources.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/widgets/appbar/dop_native_appbar_widget.dart';
import 'package:evoapp/feature/dop_native/widgets/dop_native_status_widget.dart';
import 'package:evoapp/feature/logging/event_tracking_shared_data.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../base/evo_page_state_base_test_config.dart';
import '../../../../../util/flutter_test_config.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockDOPNativeCollectLocationHandler extends Mock implements DOPNativeCollectLocationHandler {}

class MockDOPNativeApplicationStateCubit extends Mock implements DOPNativeApplicationStateCubit {}

class MockAppState extends Mock implements AppState {}

class MockDOPNativeBootstrapAuthSettingsEntity extends Mock
    implements DOPNativeBootstrapAuthSettingsEntity {}

void main() {
  late MockDOPNativeRepo mockDOPNativeRepo;
  late MockAuthenticationRepo mockAuthenticationRepo;
  late MockDOPNativeCollectLocationHandler mockDOPNativeCollectLocationHandler;
  late MockAppState mockAppState;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    initConfigEvoPageStateBase();
    setUtilsMockInstanceForTesting();

    mockDOPNativeRepo = MockDOPNativeRepo();
    mockAuthenticationRepo = MockAuthenticationRepo();
    mockDOPNativeCollectLocationHandler = MockDOPNativeCollectLocationHandler();
    mockAppState = MockAppState();

    getIt.registerSingleton<DOPNativeRepo>(mockDOPNativeRepo);
    getIt.registerSingleton<AuthenticationRepo>(mockAuthenticationRepo);

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

    setUpMockConfigEvoPageStateBase();
    setupMockImageProvider();

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(100);
  });

  tearDown(() {
    reset(mockDOPNativeRepo);
    reset(mockAuthenticationRepo);
    reset(mockAppState);
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  test('pushReplacementNamed replaces with the correct route', () {
    DOPNativeIdCardSuccessScreen.pushReplacementNamed();
    verify(() =>
            mockNavigatorContext.pushReplacementNamed(Screen.dopNativeIdCardSuccessScreen.name))
        .called(1);
  });

  group('DOPNativeIdCardSuccessScreen UI tests', () {
    testWidgets('renders correctly with all UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const DOPNativeIdCardSuccessScreen(),
        ),
      );

      // Verify the screen renders
      expect(find.byType(DOPNativeIdCardSuccessScreen), findsOneWidget);

      // Verify the scaffold and background color
      final Scaffold scaffold = tester.widget(find.byType(Scaffold));
      expect(scaffold.backgroundColor, dopNativeColors.screenBackground);

      // Verify the appbar
      expect(find.byType(DOPNativeAppBar), findsOneWidget);

      // Verify the DOPNativeStatusWidget
      expect(find.byType(DOPNativeStatusWidget), findsOneWidget);

      final DOPNativeStatusWidget statusWidget = tester.widget<DOPNativeStatusWidget>(
        find.byType(DOPNativeStatusWidget),
      );

      // Verify status widget properties
      expect(statusWidget.icon, DOPNativeImages.imgIDCard);
      expect(statusWidget.title, DOPNativeStrings.dopNativeIdCardVerificationSuccessTitle);
      expect(statusWidget.description, DOPNativeStrings.dopNativeIdCardVerificationSuccessDesc);

      // Verify CTA button
      expect(find.byType(CommonButton), findsOneWidget);
      final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      final Text buttonText = button.child as Text;
      expect(buttonText.data, DOPNativeStrings.dopNativeNext);
    });
  });

  group('DOPNativeIdCardSuccessScreen functionality tests', () {
    setUpAll(() {
      if (getIt.isRegistered<AppState>()) {
        getIt.unregister<AppState>();

        getIt.registerSingleton<AppState>(mockAppState);
      }
    });

    setUp(() {
      when(() => mockAppState.eventTrackingSharedData).thenReturn(
        EventTrackingSharedData(),
      );

      when(() => mockDOPNativeRepo.getApplicationState(
            token: any(named: 'token'),
            flowSelectedAt: any(named: 'flowSelectedAt'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer(
        (_) async => DOPNativeApplicationStateEntity(),
      );
    });

    tearDown(() {
      reset(mockAppState);
    });

    testWidgets('onPressCTA calls getApplicationState when auth type is not NID',
        (WidgetTester tester) async {
      when(() => mockAppState.dopNativeState).thenReturn(
        DOPNativeState(
          bootstrapAuthSettings: DOPNativeBootstrapAuthSettingsEntity(authType: 'otp'),
        ),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(builder: (BuildContext context) {
              when(() => globalKeyProvider.navigatorContext).thenReturn(context);
              return const DOPNativeIdCardSuccessScreen();
            }),
          ),
        ),
      );

      await tester.tap(find.byType(CommonButton));
      await tester.pump(Duration(seconds: 1));

      verify(
        () => mockDOPNativeRepo.getApplicationState(
            token: captureAny(named: 'token'),
            flowSelectedAt: captureAny(named: 'flowSelectedAt'),
            mockConfig: any(named: 'mockConfig')),
      ).called(1);

      verifyNever(
          () => mockDOPNativeCollectLocationHandler.checkConditionsAndProcessCollectLocation(
                onFinish: any(named: 'onFinish'),
              ));
    });

    testWidgets('onPressCTA calls checkConditionsAndProcessCollectLocation when auth type is NID',
        (WidgetTester tester) async {
      when(() => mockAppState.dopNativeState).thenReturn(
        DOPNativeState(
          bootstrapAuthSettings: DOPNativeBootstrapAuthSettingsEntity(authType: 'id_card_auth'),
        ),
      );

      VoidCallback? capturedOnFinish;

      when(() => mockDOPNativeCollectLocationHandler.checkConditionsAndProcessCollectLocation(
            onFinish: any(named: 'onFinish'),
          )).thenAnswer((Invocation invocation) {
        capturedOnFinish = invocation.namedArguments[const Symbol('onFinish')] as VoidCallback;

        capturedOnFinish?.call();
        return Future<void>.value();
      });

      DOPNativeCollectLocationHandler.instanceForTesting = mockDOPNativeCollectLocationHandler;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(builder: (BuildContext context) {
              when(() => globalKeyProvider.navigatorContext).thenReturn(context);
              return const DOPNativeIdCardSuccessScreen();
            }),
          ),
        ),
      );

      await tester.tap(find.byType(CommonButton));
      await tester.pump(Durations.extralong4);

      verify(() => mockDOPNativeCollectLocationHandler.checkConditionsAndProcessCollectLocation(
            onFinish: any(named: 'onFinish'),
          )).called(1);

      verify(
        () => mockDOPNativeRepo.getApplicationState(
            token: captureAny(named: 'token'),
            flowSelectedAt: captureAny(named: 'flowSelectedAt'),
            mockConfig: any(named: 'mockConfig')),
      ).called(1);

      // Reset the location handler
      DOPNativeCollectLocationHandler.resetToOriginalInstance();
    });
  });
}
