import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_verification_confirm/widgets/dop_native_edit_old_id_card_widget.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../util/flutter_test_config.dart';

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

void main() {
  late MockDOPUtilFunctions mockDOPUtilFunctions;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(BoxFit.contain);

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    mockDOPUtilFunctions = MockDOPUtilFunctions();

    getIt.registerLazySingleton<DOPUtilFunctions>(() => mockDOPUtilFunctions);
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

    when(() => mockDOPUtilFunctions.isVietnameseCitizenIdCardBefore2016(any())).thenReturn(false);

    when(() => mockDOPUtilFunctions.isVietnameseCitizenIdCardBefore2016('123456789'))
        .thenReturn(true);
  });

  setUp(() {
    setupMockImageProvider();
  });

  tearDown(() {
    reset(mockDOPUtilFunctions);
    reset(evoImageProvider);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('DOPNativeEditOldIdCardWidget', () {
    // Add this test to your test file
    testWidgets('should verify initial state of variables', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeEditOldIdCardWidget(
              oldIDCard: '',
              onEditOldIdCard: (String value) {},
            ),
          ),
        ),
      );

      // Act
      final DOPNativeInformationItemWidgetState state = tester
          .state(find.byType(DOPNativeEditOldIdCardWidget)) as DOPNativeInformationItemWidgetState;

      // Assert
      expect(state.focusNode, isNotNull);
      expect(state.oldIdCardTextController, isNotNull);
      expect(state.status, equals(InputOldIdCardStatus.none));
    });

    testWidgets('should render initial state correctly', (WidgetTester tester) async {
      // Arrange
      bool callbackCalled = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeEditOldIdCardWidget(
              oldIDCard: '',
              onEditOldIdCard: (String value) {
                callbackCalled = true;
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(DOPNativeEditOldIdCardWidget), findsOneWidget);
      expect(find.text(DOPNativeStrings.dopNativeIdVerificationOldIdCardInput), findsOneWidget);

      // Verify edit icon is shown
      verify(() => evoImageProvider.asset(
            DOPNativeImages.icDOPNativeEdit,
            height: any(named: 'height'),
            width: any(named: 'width'),
            cornerRadius: any(named: 'cornerRadius'),
            fit: any(named: 'fit'),
          )).called(1);

      // Callback should not be called yet
      expect(callbackCalled, isFalse);

      // Initial status should be none
      final DOPNativeInformationItemWidgetState state = tester
          .state(find.byType(DOPNativeEditOldIdCardWidget)) as DOPNativeInformationItemWidgetState;
      expect(state.status, InputOldIdCardStatus.none);
    });

    testWidgets('should switch to input mode when edit icon is tapped',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 300,
              child: DOPNativeEditOldIdCardWidget(
                oldIDCard: '',
                onEditOldIdCard: (String value) {},
              ),
            ),
          ),
        ),
      );

      expect(find.text(DOPNativeStrings.dopNativeIdVerificationOldIdCardInput), findsOneWidget);

      final DOPNativeInformationItemWidgetState state = tester
          .state(find.byType(DOPNativeEditOldIdCardWidget)) as DOPNativeInformationItemWidgetState;

      expect(state.status, InputOldIdCardStatus.none);

      state.status = InputOldIdCardStatus.inputting;
      await tester.pump();

      expect(state.status, InputOldIdCardStatus.inputting);
    });

    testWidgets('should validate ID card correctly - valid case', (WidgetTester tester) async {
      // Arrange
      when(() => mockDOPUtilFunctions.isVietnameseCitizenIdCardBefore2016('123456789'))
          .thenReturn(true);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeEditOldIdCardWidget(
              oldIDCard: '',
              onEditOldIdCard: (String value) {},
            ),
          ),
        ),
      );

      final DOPNativeInformationItemWidgetState state = tester
          .state(find.byType(DOPNativeEditOldIdCardWidget)) as DOPNativeInformationItemWidgetState;

      state.status = InputOldIdCardStatus.inputting;
      await tester.pump();

      state.onChangeText('123456789');
      await tester.pump();

      verify(() => mockDOPUtilFunctions.isVietnameseCitizenIdCardBefore2016('123456789')).called(1);

      expect(state.status, InputOldIdCardStatus.valid);
    });

    testWidgets('should validate ID card correctly - invalid case', (WidgetTester tester) async {
      // Arrange
      when(() => mockDOPUtilFunctions.isVietnameseCitizenIdCardBefore2016('12345'))
          .thenReturn(false);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeEditOldIdCardWidget(
              oldIDCard: '',
              onEditOldIdCard: (String value) {},
            ),
          ),
        ),
      );

      final DOPNativeInformationItemWidgetState state = tester
          .state(find.byType(DOPNativeEditOldIdCardWidget)) as DOPNativeInformationItemWidgetState;

      state.status = InputOldIdCardStatus.inputting;
      await tester.pump();

      state.onChangeText('12345');
      await tester.pump();

      verify(() => mockDOPUtilFunctions.isVietnameseCitizenIdCardBefore2016('12345')).called(1);

      expect(state.status, InputOldIdCardStatus.invalid);
    });

    testWidgets('should test each branch of suffixIcon method', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeEditOldIdCardWidget(
              oldIDCard: '',
              onEditOldIdCard: (String value) {},
            ),
          ),
        ),
      );

      final DOPNativeInformationItemWidgetState state = tester
          .state(find.byType(DOPNativeEditOldIdCardWidget)) as DOPNativeInformationItemWidgetState;

      expect(state.status, InputOldIdCardStatus.none);
      expect(state.suffixIcon(), isNotNull);

      state.status = InputOldIdCardStatus.valid;
      await tester.pump();
      expect(state.suffixIcon(), isNotNull);

      state.status = InputOldIdCardStatus.invalid;
      await tester.pump();
      expect(state.suffixIcon(), isNotNull);
    });

    testWidgets(
        'should test buildContentDescriptionOldIdCard method returns ListenableBuilder with TextField',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeEditOldIdCardWidget(
              oldIDCard: '',
              onEditOldIdCard: (String value) {},
            ),
          ),
        ),
      );

      final DOPNativeInformationItemWidgetState state = tester
          .state(find.byType(DOPNativeEditOldIdCardWidget)) as DOPNativeInformationItemWidgetState;

      // Test for all status values
      for (final InputOldIdCardStatus status in InputOldIdCardStatus.values) {
        final Widget widget = state.buildContentDescriptionOldIdCard(status);
        expect(widget, isA<ListenableBuilder>());
      }

      // Verify TextField is rendered
      expect(find.byType(TextField), findsOneWidget);
    });

    testWidgets('should update status correctly when text changes', (WidgetTester tester) async {
      // Arrange
      when(() => mockDOPUtilFunctions.isVietnameseCitizenIdCardBefore2016('123456789'))
          .thenReturn(true);
      when(() => mockDOPUtilFunctions.isVietnameseCitizenIdCardBefore2016('12345'))
          .thenReturn(false);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeEditOldIdCardWidget(
              oldIDCard: '',
              onEditOldIdCard: (String value) {},
            ),
          ),
        ),
      );

      final DOPNativeInformationItemWidgetState state = tester
          .state(find.byType(DOPNativeEditOldIdCardWidget)) as DOPNativeInformationItemWidgetState;

      // Act & Assert - Valid ID
      state.onChangeText('123456789');
      await tester.pump();
      expect(state.status, InputOldIdCardStatus.valid);

      // Act & Assert - Invalid ID
      state.onChangeText('12345');
      await tester.pump();
      expect(state.status, InputOldIdCardStatus.invalid);
    });

    testWidgets('should enter edit mode when edit button is pressed', (WidgetTester tester) async {
      when(() => evoImageProvider.asset(
            DOPNativeImages.icDOPNativeEdit,
            width: any(named: 'width'),
            height: any(named: 'height'),
            color: any(named: 'color'),
            fit: any(named: 'fit'),
            cornerRadius: any(named: 'cornerRadius'),
            cacheWidth: any(named: 'cacheWidth'),
            cacheHeight: any(named: 'cacheHeight'),
            package: any(named: 'package'),
          )).thenAnswer((_) => SizedBox(
            width: 20,
            height: 20,
            key: Key(DOPNativeImages.icDOPNativeEdit),
          ));

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeEditOldIdCardWidget(
              oldIDCard: '',
              onEditOldIdCard: (String value) {},
            ),
          ),
        ),
      );

      expect(find.text(DOPNativeStrings.dopNativeIdVerificationOldIdCardInput), findsOneWidget);

      final InkWell inkWell = find.byType(InkWell).evaluate().first.widget as InkWell;

      inkWell.onTap?.call();
      await tester.pump();

      final DOPNativeInformationItemWidgetState state = tester
          .state<DOPNativeInformationItemWidgetState>(find.byType(DOPNativeEditOldIdCardWidget));

      // We don't need to check status since it's not changed by the tap
      // The status is changed by onChangeText method
      expect(find.byType(TextField), findsOneWidget);
      expect(state.focusNode.hasFocus, isTrue);
    });

    testWidgets('should call callback when text is entered after edit button press',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockDOPUtilFunctions.isVietnameseCitizenIdCardBefore2016(any())).thenReturn(false);
      String editedValue = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeEditOldIdCardWidget(
              oldIDCard: '',
              onEditOldIdCard: (String value) {
                editedValue = value;
              },
            ),
          ),
        ),
      );

      final InkWell inkWell = find.byType(InkWell).evaluate().first.widget as InkWell;

      inkWell.onTap?.call();
      await tester.pump();

      // Act - enter text
      await tester.enterText(find.byType(TextField), '123');
      await tester.pump();

      // Assert
      expect(editedValue, '123');
    });
  });
}
