import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/fpt/mock/mock_dop_native_submit_nfc_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Test MockDOPNativeSubmitNFC', () {
    expect(MockDOPNativeSubmitNFC.submitNFCSuccess.value, 'dop_native_submit_nfc_success.json');
    expect(MockDOPNativeSubmitNFC.submitNFCFail.value, 'dop_native_submit_nfc_fail.json');
  });

  test('Test getMockDOPNativeSubmitNFC', () {
    expect(
      getMockDOPNativeSubmitNFC(MockDOPNativeSubmitNFC.submitNFCSuccess),
      'dop_native_submit_nfc_success.json',
    );
    expect(
      getMockDOPNativeSubmitNFC(MockDOPNativeSubmitNFC.submitNFCFail),
      'dop_native_submit_nfc_fail.json',
    );
  });
}
