import 'package:evoapp/data/response/dop_native/dop_native_ekyc_status_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';

DOPNativeEkycStatusEntity createSubmitStatusEntity(
  String status, {
  String? verdict,
  String? userMessage,
  String? accessToken,
}) {
  return DOPNativeEkycStatusEntity.fromBaseResponse(
    BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: <String, dynamic>{
        'verdict': verdict,
        'data': <String, dynamic>{
          'status': status,
          'error_code': '',
          'label': 'label1',
          'qr_readable': true,
          'user_message': userMessage,
          'access_token': accessToken,
        }
      },
    ),
  );
}

const String mockUserMessage = 'mock-user-message';

final DOPNativeEkycStatusEntity pollingSuccessEntity = createSubmitStatusEntity(
  DOPNativeEkycStatusEntity.statusSuccess,
  verdict: 'success',
);
final DOPNativeEkycStatusEntity pollingSuccessIdCardAuthEntity = createSubmitStatusEntity(
  DOPNativeEkycStatusEntity.statusSuccess,
  verdict: 'success',
  accessToken: 'fake_access_token',
);
final DOPNativeEkycStatusEntity pollingFailureEntity = createSubmitStatusEntity(
  DOPNativeEkycStatusEntity.statusFailure,
  verdict: 'verdict-not-success',
  userMessage: mockUserMessage,
);
final DOPNativeEkycStatusEntity pollingPendingEntity = createSubmitStatusEntity(
  DOPNativeEkycStatusEntity.statusPending,
  verdict: 'success',
);
final DOPNativeEkycStatusEntity pollingInProgressEntity = createSubmitStatusEntity(
  DOPNativeEkycStatusEntity.statusInProgress,
  verdict: 'success',
);
