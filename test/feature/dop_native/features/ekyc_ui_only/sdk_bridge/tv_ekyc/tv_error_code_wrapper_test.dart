import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/tv_error_code_wrapper.dart';
import 'package:evoapp/feature/ekyc/model/ekyc_result_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:trust_vision_plugin/enums.dart';

void main() {
  test('TVErrorCodeWrapper constants', () {
    expect(TVErrorCodeWrapper.iOSQRTimeout, 'qrTimeout');
    expect(TVErrorCodeWrapper.androidSQRTimeout, 'qr_timeout');
    expect(TVErrorCodeWrapper.iOSQRSkip, 'qrSkip');
    expect(TVErrorCodeWrapper.androidQRSkip, 'qr_skip');
  });

  test('Test toTVSDKFailReason', () {
    expect(
      TVErrorCodeWrapper(exceptionCode: TVErrorCode.sdk_canceled.name).toTVSDKFailReason(),
      TVSDKFailReason.userCancelled,
    );
    expect(
      TVErrorCodeWrapper(exceptionCode: TVErrorCode.access_denied_exception.name)
          .toTVSDKFailReason(),
      TVSDKFailReason.sessionExpired,
    );
    expect(
      TVErrorCodeWrapper(exceptionCode: TVErrorCodeWrapper.iOSQRTimeout).toTVSDKFailReason(),
      TVSDKFailReason.qrTimeout,
    );
    expect(
      TVErrorCodeWrapper(exceptionCode: TVErrorCodeWrapper.androidSQRTimeout).toTVSDKFailReason(),
      TVSDKFailReason.qrTimeout,
    );
    expect(
      TVErrorCodeWrapper(exceptionCode: TVErrorCodeWrapper.iOSQRSkip).toTVSDKFailReason(),
      TVSDKFailReason.qrSkip,
    );
    expect(
      TVErrorCodeWrapper(exceptionCode: TVErrorCodeWrapper.androidQRSkip).toTVSDKFailReason(),
      TVSDKFailReason.qrSkip,
    );
    expect(
      TVErrorCodeWrapper(exceptionCode: 'fake_exception_code').toTVSDKFailReason(),
      TVSDKFailReason.unknown,
    );
  });
}
