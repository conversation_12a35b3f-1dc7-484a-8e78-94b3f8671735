import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/feature/dop_native/features/salesman/dop_native_salesman_id_lead_source_prefix.dart';
import 'package:evoapp/feature/dop_native/models/dop_native_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAppState extends Mock implements AppState {}

void main() {
  setUpAll(() {
    getIt.registerSingleton<AppState>(MockAppState());
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('enum value DOPNativeSalesmanIDLeadSourcePrefix', () {
    expect(DOPNativeSalesmanIDLeadSourcePrefix.viettelStore.value, 'VST');
  });

  test('getPrefixByLeadSource returns correct values', () {
    expect(getPrefixByLeadSource(LeadSource.viettelStore), 'VST');
  });

  group('test getSalesmanIdWithPrefix', () {
    test('getSalesmanIdWithPrefix returns correct ID with prefix', () {
      final MockAppState mockAppState = getIt<AppState>() as MockAppState;
      when(() => mockAppState.dopNativeState).thenReturn(
        DOPNativeState(
          dopApplicationState: DOPNativeApplicationStateEntity(
            flowConfig: FlowConfig.fromJson(
              <String, dynamic>{
                'lead_source': 'viettel_store',
              },
            ),
          ),
        ),
      );

      final String result = getSalesmanIdWithPrefix('12345');
      expect(result, 'VST12345');
    });

    test('getSalesmanIdWithPrefix returns correct ID with prefix', () {
      final MockAppState mockAppState = getIt<AppState>() as MockAppState;
      when(() => mockAppState.dopNativeState).thenReturn(
        DOPNativeState(
          dopApplicationState: DOPNativeApplicationStateEntity(
            flowConfig: FlowConfig.fromJson(
              <String, dynamic>{
                'lead_source': 'mwg',
              },
            ),
          ),
        ),
      );

      final String result = getSalesmanIdWithPrefix('12345');
      expect(result, '12345');
    });
  });
}
