import 'package:evoapp/data/response/dop_native/dop_native_metadata_item_entity.dart';
import 'package:evoapp/feature/dop_native/features/acquisition_reward/cubit/dop_native_acquisition_reward_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeAcquisitionRewardState Tests', () {
    test(
        'DOPNativeAcquisitionRewardInitial should be an instance of DOPNativeAcquisitionRewardState',
        () {
      final DOPNativeAcquisitionRewardInitial state = DOPNativeAcquisitionRewardInitial();
      expect(state, isA<DOPNativeAcquisitionRewardState>());
    });

    test(
        'DOPNativeAcquisitionRewardLoading should be an instance of DOPNativeAcquisitionRewardState',
        () {
      final DOPNativeAcquisitionRewardLoading state = DOPNativeAcquisitionRewardLoading();
      expect(state, isA<DOPNativeAcquisitionRewardState>());
    });

    test('DOPNativeAcquisitionRewardLoaded should store entities', () {
      final List<DOPNativeMetadataItemEntity> entities = <DOPNativeMetadataItemEntity>[];
      final DOPNativeAcquisitionRewardLoaded state = DOPNativeAcquisitionRewardLoaded(entities);
      expect(state.entities, entities);
    });

    test('DOPNativeAcquisitionRewardError should store error', () {
      final ErrorUIModel error = ErrorUIModel();
      final DOPNativeAcquisitionRewardError state = DOPNativeAcquisitionRewardError(error);
      expect(state.error, error);
    });

    test('DOPNativeAcquisitionRewardSelected should store selectedIndex', () {
      const int selectedIndex = 1;
      final DOPNativeAcquisitionRewardSelected state = DOPNativeAcquisitionRewardSelected(
        selectedIndex,
      );
      expect(state.selectedIndex, selectedIndex);
    });

    test(
        'DOPNativeAcquisitionRewardTermAndConditionPdfUrlLoaded should store termAndConditionPdfUrl',
        () {
      const String url = 'http://example.com/terms.pdf';
      final DOPNativeAcquisitionRewardTermAndConditionPdfUrlReady state =
          DOPNativeAcquisitionRewardTermAndConditionPdfUrlReady(url);
      expect(state.termAndConditionPdfUrl, url);
    });

    test(
        'DOPNativeAcquisitionRewardSubmitSuccess should be an instance of DOPNativeAcquisitionRewardState',
        () {
      final DOPNativeAcquisitionRewardSubmitSuccess state =
          DOPNativeAcquisitionRewardSubmitSuccess();
      expect(state, isA<DOPNativeAcquisitionRewardState>());
    });
  });
}
