import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_request_otp_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_verify_otp_entity.dart';
import 'package:evoapp/feature/dop_native/features/verify_otp/cubit/dop_native_verify_otp_cubit.dart';
import 'package:evoapp/feature/dop_native/features/verify_otp/dop_native_verify_otp_screen.dart';
import 'package:evoapp/feature/dop_native/models/dop_native_state.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockAppState extends Mock implements AppState {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

void main() {
  const String fakeUniqueToken = 'fakeUniqueToken';
  const String fakeDOPNativeAccessToken = 'fakeDopNativeAccessToken';
  final DOPNativeState fakeDOPNativeSharedData = DOPNativeState()
    ..uniqueToken = fakeUniqueToken
    ..dopNativeAccessToken = fakeDOPNativeAccessToken;

  final DOPNativeRepo mockDOPNativeRepo = MockDOPNativeRepo();
  final AppState mockAppState = MockAppState();
  final DOPUtilFunctions mockDOPUtilFunctions = MockDOPUtilFunctions();

  late DOPNativeVerifyOtpCubit cubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<DOPUtilFunctions>(() => mockDOPUtilFunctions);

    when(() => mockAppState.dopNativeState).thenReturn(fakeDOPNativeSharedData);
  });

  setUp(() {
    cubit = DOPNativeVerifyOtpCubit(
      dopNativeRepo: mockDOPNativeRepo,
      appState: mockAppState,
    );
  });

  test('Default state', () {
    expect(cubit.state, isA<VerifyOtpInitial>());
  });

  group('Test requestOTP', () {
    const int expectedValidSeconds = 180;
    const int expectedRetries = 4;
    const int expectedOtpLength = 6;

    blocTest<DOPNativeVerifyOtpCubit, DOPNativeVerifyOtpState>(
      'Call requestOTP success',
      setUp: () {
        final DOPNativeRequestOTPEntity entity =
            DOPNativeRequestOTPEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'valid_seconds': 180,
              'retries': 4,
              'otp_length': 6,
            }
          },
        ));

        when(() => mockDOPNativeRepo.requestOTP(
              token: any(named: 'token'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return entity;
        });
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeVerifyOtpCubit cubit) => cubit.requestOTP(),
      verify: (DOPNativeVerifyOtpCubit cubit) {
        verify(() => mockAppState.dopNativeState).called(1);
        expect(
          verify(() => mockDOPNativeRepo.requestOTP(
                token: captureAny(named: 'token'),
                mockConfig: any(named: 'mockConfig'),
              )).captured.single,
          fakeUniqueToken,
        );
      },
      expect: () => <dynamic>[
        isA<ScreenLoading>(),
        isA<RequestOTPLoaded>()
            .having(
              (RequestOTPLoaded p0) => p0.requestOTPEntity.validSeconds,
              'verify validSeconds',
              expectedValidSeconds,
            )
            .having(
              (RequestOTPLoaded p0) => p0.requestOTPEntity.retries,
              'verify retries',
              expectedRetries,
            )
            .having(
              (RequestOTPLoaded p0) => p0.requestOTPEntity.otpLength,
              'verify otpLength',
              expectedOtpLength,
            ),
      ],
    );

    blocTest<DOPNativeVerifyOtpCubit, DOPNativeVerifyOtpState>(
      'Call requestOTP fail',
      setUp: () {
        when(() => mockDOPNativeRepo.requestOTP(
              token: any(named: 'token'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return DOPNativeRequestOTPEntity.unserializable();
        });
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeVerifyOtpCubit cubit) => cubit.requestOTP(),
      verify: (DOPNativeVerifyOtpCubit cubit) {
        verify(() => mockAppState.dopNativeState).called(1);
        expect(
          verify(() => mockDOPNativeRepo.requestOTP(
                token: captureAny(named: 'token'),
                mockConfig: any(named: 'mockConfig'),
              )).captured.single,
          fakeUniqueToken,
        );
      },
      expect: () => <dynamic>[
        isA<ScreenLoading>(),
        isA<RequestOtpError>(),
      ],
    );
  });

  group('Test verifyOTP', () {
    const String fakeAccessToken = 'fakeAccessToken';
    const String fakeOtp = '123456';

    const String fakeErrorMessage = 'fakeErrorMessage';
    const String fakeErrorVerdict = 'fakeErrorVerdict';

    blocTest<DOPNativeVerifyOtpCubit, DOPNativeVerifyOtpState>(
      'Call verifyOTP success',
      setUp: () {
        final DOPVerifyOTPEntity entity = DOPVerifyOTPEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'access_token': fakeAccessToken,
            }
          },
        ));

        when(() => mockDOPNativeRepo.verifyOTP(
              token: any(named: 'token'),
              otp: any(named: 'otp'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return entity;
        });
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeVerifyOtpCubit cubit) => cubit.verifyOTP(fakeOtp),
      verify: (DOPNativeVerifyOtpCubit cubit) {
        expect(
          verify(() => mockDOPNativeRepo.verifyOTP(
                token: captureAny(named: 'token'),
                otp: captureAny(named: 'otp'),
                mockConfig: any(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            fakeUniqueToken,
            fakeOtp,
          ],
        );

        verify(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(fakeAccessToken)).called(1);
        verify(() => mockAppState.dopNativeState).called(2);
      },
      expect: () => <dynamic>[
        isA<ScreenLoading>(),
        isA<VerifyOtpCompleted>().having(
          (VerifyOtpCompleted p0) => p0.entity.accessToken,
          'verify response access token',
          fakeAccessToken,
        ),
      ],
    );

    blocTest<DOPNativeVerifyOtpCubit, DOPNativeVerifyOtpState>(
      'Call verifyOTP success but incorrect otp',
      setUp: () {
        final DOPVerifyOTPEntity entity = DOPVerifyOTPEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'verdict': DOPVerifyOTPEntity.verdictIncorrectOTP,
            'data': <String, dynamic>{
              'user_message': fakeErrorMessage,
            }
          },
        ));

        when(() => mockDOPNativeRepo.verifyOTP(
              token: any(named: 'token'),
              otp: any(named: 'otp'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return entity;
        });
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeVerifyOtpCubit cubit) => cubit.verifyOTP(fakeOtp),
      verify: (DOPNativeVerifyOtpCubit cubit) {
        expect(
          verify(() => mockDOPNativeRepo.verifyOTP(
                token: captureAny(named: 'token'),
                otp: captureAny(named: 'otp'),
                mockConfig: any(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            fakeUniqueToken,
            fakeOtp,
          ],
        );

        verify(() => mockAppState.dopNativeState).called(1);
      },
      expect: () => <dynamic>[
        isA<ScreenLoading>(),
        isA<VerifyOtpIncorrect>()
            .having(
              (VerifyOtpIncorrect p0) => p0.error.userMessage,
              'verify error message',
              fakeErrorMessage,
            )
            .having(
              (VerifyOtpIncorrect p0) => p0.error.verdict,
              'verify verdict',
              DOPVerifyOTPEntity.verdictIncorrectOTP,
            ),
      ],
    );

    blocTest<DOPNativeVerifyOtpCubit, DOPNativeVerifyOtpState>(
      'Call verifyOTP fail',
      setUp: () {
        final DOPVerifyOTPEntity entity = DOPVerifyOTPEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: <String, dynamic>{
            'verdict': fakeErrorVerdict,
            'data': <String, dynamic>{
              'user_message': fakeErrorMessage,
            }
          },
        ));

        when(() => mockDOPNativeRepo.verifyOTP(
              token: any(named: 'token'),
              otp: any(named: 'otp'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return entity;
        });
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeVerifyOtpCubit cubit) => cubit.verifyOTP(fakeOtp),
      verify: (DOPNativeVerifyOtpCubit cubit) {
        expect(
          verify(() => mockDOPNativeRepo.verifyOTP(
                token: captureAny(named: 'token'),
                otp: captureAny(named: 'otp'),
                mockConfig: any(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            fakeUniqueToken,
            fakeOtp,
          ],
        );
      },
      expect: () => <dynamic>[
        isA<ScreenLoading>(),
        isA<VerifyOtpError>()
            .having(
              (VerifyOtpError p0) => p0.error.userMessage,
              'verify error message',
              fakeErrorMessage,
            )
            .having(
              (VerifyOtpError p0) => p0.error.verdict,
              'verify verdict',
              fakeErrorVerdict,
            ),
      ],
    );
  });

  group('test handleESignOTP', () {
    test('emit RequestOTPLoaded with correct data ', () {
      cubit.handleESignOTP(180, 3);

      expect(cubit.state, isA<RequestOTPLoaded>());
      expect((cubit.state as RequestOTPLoaded).requestOTPEntity.retries, 3);
      expect((cubit.state as RequestOTPLoaded).requestOTPEntity.validSeconds, 180);
    });

    test('emit RequestOTPLoaded wrong data ', () {
      cubit.handleESignOTP(null, null);

      expect(cubit.state, isA<RequestOTPLoaded>());
      expect((cubit.state as RequestOTPLoaded).requestOTPEntity.retries, isNull);
      expect((cubit.state as RequestOTPLoaded).requestOTPEntity.validSeconds, isNull);
    });
  });

  group('test onInit function', () {
    test('onInit should call handleESignOTP if verifyOtpType is DOPNativeVerifyOtpType.eSign', () {
      cubit.requestOTPIfNeeded(
        verifyOtpType: DOPNativeVerifyOtpType.eSign,
        retries: 3,
        validSeconds: 180,
      );
      expect(cubit.state, isA<RequestOTPLoaded>());
      expect((cubit.state as RequestOTPLoaded).requestOTPEntity.retries, 3);
      expect((cubit.state as RequestOTPLoaded).requestOTPEntity.validSeconds, 180);
    });

    blocTest<DOPNativeVerifyOtpCubit, DOPNativeVerifyOtpState>(
      'onInit should call requestOTP if verifyOtpType is not DOPNativeVerifyOtpType.eSign',
      setUp: () async {
        final DOPNativeRequestOTPEntity entity =
            DOPNativeRequestOTPEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'valid_seconds': 180,
              'retries': 4,
              'otp_length': 6,
            }
          },
        ));

        when(() => mockDOPNativeRepo.requestOTP(
              token: any(named: 'token'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return entity;
        });

        when(() => mockAppState.dopNativeState).thenReturn(
          DOPNativeState(),
        );
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeVerifyOtpCubit cubit) => cubit.requestOTPIfNeeded(
        verifyOtpType: DOPNativeVerifyOtpType.signIn,
      ),
      verify: (DOPNativeVerifyOtpCubit cubit) {
        verify(() => mockDOPNativeRepo.requestOTP(
              token: any(named: 'token'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
      expect: () => <dynamic>[
        isA<ScreenLoading>(),
        isA<RequestOTPLoaded>(),
      ],
    );
  });
}
