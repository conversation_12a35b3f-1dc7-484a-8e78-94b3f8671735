import 'package:evoapp/feature/dop_native/features/card_activation_status/utils/dop_native_card_status_information_utils.dart';
import 'package:evoapp/feature/dop_native/util/card_status/cubit/dop_native_card_status_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

void main() {
  late BuildContext mockNavigatorContext;
  late CommonNavigator commonNavigator;

  setUpAll(() {
    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);
    registerFallbackValue(mockNavigatorContext);

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    commonNavigator = getIt.get<CommonNavigator>();
  });

  group('Test handleActivationStatus', () {
    test('Test handleActivationStatus when state is GetCardStatusBlocked', () {
      DOPNativeCardStatusInformationUtils.handleActivationStatus(GetCardStatusBlocked());

      expect(
        verify(() => commonNavigator.pushReplacementNamed(
              any(),
              captureAny(),
            )).captured,
        <dynamic>[
          Screen.dopNativeCardStatusCICBlockScreenName,
        ],
      );
    });

    test('Test handleActivationStatus when state is CardStatusInformationActivated', () {
      DOPNativeCardStatusInformationUtils.handleActivationStatus(CardStatusInformationActivated());

      expect(
        verify(() => commonNavigator.pushReplacementNamed(
              any(),
              captureAny(),
            )).captured,
        <dynamic>[
          Screen.dopNativeCardActivatedScreenName,
        ],
      );
    });

    test('Test handleActivationStatus when state is CardStatusInformationActivatedRetryPosLimit',
        () {
      DOPNativeCardStatusInformationUtils.handleActivationStatus(
        CardStatusInformationActivatedRetryPosLimit(),
      );
      expect(
        verify(
          () => commonNavigator.pushReplacementNamed(
            any(),
            captureAny(),
          ),
        ).captured,
        <dynamic>[
          Screen.dopNativeCardActivatedRetryPosLimitScreenName,
        ],
      );
    });

    test('Test handleActivationStatus when state is CardStatusInformationActivatedPosFailed', () {
      DOPNativeCardStatusInformationUtils.handleActivationStatus(
        CardStatusInformationActivatedPosFailed(),
      );
      expect(
        verify(
          () => commonNavigator.pushReplacementNamed(
            any(),
            captureAny(),
          ),
        ).captured,
        <dynamic>[
          Screen.dopNativeCardActivatedPosFailedScreenName,
        ],
      );
    });

    test('Test handleActivationStatus when state is CardStatusInformationRetry', () {
      DOPNativeCardStatusInformationUtils.handleActivationStatus(CardStatusInformationRetry());

      expect(
        verify(() => commonNavigator.pushReplacementNamed(
              any(),
              captureAny(),
            )).captured,
        <dynamic>[
          Screen.dopNativeCardActiveRetryScreenName,
        ],
      );
    });

    test('Test handleActivationStatus when state is CardStatusInformationFail', () {
      DOPNativeCardStatusInformationUtils.handleActivationStatus(CardStatusInformationFail());

      expect(
        verify(() => commonNavigator.pushReplacementNamed(
              any(),
              captureAny(),
            )).captured,
        <dynamic>[
          Screen.dopNativeCardActiveFailScreenName,
        ],
      );
    });
  });
}
