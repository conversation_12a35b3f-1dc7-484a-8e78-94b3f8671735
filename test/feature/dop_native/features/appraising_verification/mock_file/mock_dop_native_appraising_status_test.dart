import 'package:evoapp/feature/dop_native/features/appraising_verification/mock_file/mock_dop_native_appraising_status.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('enum value MockDOPNativeAppraisingStatusCase', () {
    expect(
      MockDOPNativeAppraisingStatusCase.success.value,
      'dop_native_appraising_status_success.json',
    );
    expect(
      MockDOPNativeAppraisingStatusCase.pending.value,
      'dop_native_appraising_status_pending.json',
    );
    expect(
      MockDOPNativeAppraisingStatusCase.inProgress.value,
      'dop_native_appraising_status_in_progress.json',
    );
  });

  test('getMockDOPNativeAppraisingStatus returns correct values', () {
    expect(
      getMockDOPNativeAppraisingStatus(MockDOPNativeAppraisingStatusCase.success),
      'dop_native_appraising_status_success.json',
    );
    expect(
      getMockDOPNativeAppraisingStatus(MockDOPNativeAppraisingStatusCase.pending),
      'dop_native_appraising_status_pending.json',
    );
    expect(
      getMockDOPNativeAppraisingStatus(MockDOPNativeAppraisingStatusCase.inProgress),
      'dop_native_appraising_status_in_progress.json',
    );
  });
}
