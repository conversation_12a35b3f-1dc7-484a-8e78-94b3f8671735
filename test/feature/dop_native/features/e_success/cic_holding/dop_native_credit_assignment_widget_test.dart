import 'package:evoapp/feature/dop_native/features/e_success/cic_holding/dop_native_cic_holding_credit_assignment_widget.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_resources.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterTextStyle();
    getItRegisterColor();

    registerFallbackValue(vietNamCurrencySymbol);

    when(() =>
            evoUtilFunction.evoFormatCurrency(any(), currencySymbol: any(named: 'currencySymbol')))
        .thenReturn('1000đ');
  });

  group('DOPNativeCreditAssignmentWidget', () {
    testWidgets('should display nothing when credit is null', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeCICHoldingCreditAssignmentWidget(),
          ),
        ),
      );

      expect(find.byType(SizedBox), findsOneWidget);
      expect(find.byType(Text), findsNothing);
    });

    testWidgets(
        'should display credit information with correct padding, text style, and data when credit is provided',
        (WidgetTester tester) async {
      const int testCredit = 1000;
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeCICHoldingCreditAssignmentWidget(credit: testCredit),
          ),
        ),
      );

      final Finder paddingFinder = find.byType(Padding);
      expect(paddingFinder, findsOneWidget);

      final Padding paddingWidget = tester.widget(paddingFinder);
      expect(paddingWidget.padding, const EdgeInsets.only(top: 24));

      final Finder textFinder = find.byType(Text);
      expect(textFinder, findsOneWidget);

      final Text textWidget = tester.widget(textFinder);
      final TextSpan textSpan = textWidget.textSpan as TextSpan;
      expect(textSpan.style, dopNativeTextStyles.bodyLarge(dopNativeColors.textPassive));

      final String expectedTextPart1 =
          DOPNativeStrings.dopNativeESuccessAutoPCBMWGTemporarilyCredit;
      final String expectedTextPart2 =
          ' ${evoUtilFunction.evoFormatCurrency(testCredit, currencySymbol: vietNamCurrencySymbol)}.';

      expect(textSpan.children?[0].toPlainText(), expectedTextPart1);
      expect(textSpan.children?[1].toPlainText(), expectedTextPart2);

      final TextSpan secondTextSpan = textSpan.children![1] as TextSpan;
      expect(secondTextSpan.style, dopNativeTextStyles.h300());
    });
  });
}
