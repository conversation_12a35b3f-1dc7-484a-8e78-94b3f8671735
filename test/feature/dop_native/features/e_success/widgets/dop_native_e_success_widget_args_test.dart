import 'package:evoapp/feature/dop_native/features/e_success/widgets/dop_native_e_success_widget_args.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  setUpAll(() {
    getIt.registerSingleton<CommonUtilFunction>(CommonUtilFunction());
    getIt.registerSingleton<EvoUtilFunction>(EvoUtilFunction());
  });

  group('DOPNativeESuccessDescArgs', () {
    test(
        'DOPNativeESuccessDescArgsPCB should initialize with correct descPrefix, credit, and descSuffix',
        () {
      const int credit = 1000;
      final DOPNativeESuccessDescArgs args = DOPNativeESuccessDescArgsPCB(
        credit: credit,
      );

      expect(args.descPrefix, equals('${DOPNativeStrings.dopNativePcbDescPrefix} '));
      expect(args.credit, equals(1000));
      expect(args.descSuffix, equals(DOPNativeStrings.dopNativePcbDescSuffix));
    });

    test(
        'DOPNativeESuccessDescArgs.CIC should initialize with correct descPrefix, credit, and descSuffix',
        () {
      const int credit = 2000;
      final DOPNativeESuccessDescArgs args = DOPNativeESuccessDescArgsCIC(credit: credit);

      expect(args.descPrefix, equals('${DOPNativeStrings.dopNativeCicDescPrefix} '));
      expect(args.credit, equals(2000));
      expect(args.descSuffix, equals(DOPNativeStrings.dopNativeCicDescSuffix));
    });

    test('creditText should return formatted currency when credit is not null', () {
      const int credit = 1500;
      final DOPNativeESuccessDescArgs args = DOPNativeESuccessDescArgsPCB(credit: credit);

      final String formattedCurrency = args.creditText;

      expect(formattedCurrency, equals('1,500đ')); // Assuming the formatted currency is as expected
    });
  });
}
