import 'package:evoapp/feature/emi_management/widgets/emi_custom_progressbar_widget.dart';
import 'package:evoapp/widget/calculation_widget_size_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
  });

  void verifyWidget(WidgetTester tester, {required Color? progressBarColorValue}) {
    final Finder containerFinder = find.byType(Container).last;
    final Container container = tester.widget<Container>(containerFinder);
    final Decoration? decoration = container.decoration;
    expect(decoration, isA<BoxDecoration>());
    final Color? progressBarColor = (decoration as BoxDecoration).color;
    expect(progressBarColor, progressBarColorValue);
  }

  group('EmiCustomProgressbarWidget', () {
    testWidgets('should render correctly with initial progress', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EmiCustomProgressbarWidget(
              width: 20,
              height: 20,
              background: Colors.green,
              progressBarColor: Colors.blue,
              progress: 0.5,
            ),
          ),
        ),
      );

      expect(find.byType(CalculationWidgetSizeWidget), findsOneWidget);
      verifyWidget(tester, progressBarColorValue: Colors.blue);
    });

    testWidgets('should update widthOfWidget on size change', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EmiCustomProgressbarWidget(
              width: 30,
              height: 20,
              background: Colors.green,
              progressBarColor: Colors.blue,
              progress: 0.5,
            ),
          ),
        ),
      );

      final Finder containerFinder = find.byType(Container).last;
      final Container container = tester.widget<Container>(containerFinder);
      final double? initialWidth = container.constraints?.maxWidth;

      // Simulate size change
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EmiCustomProgressbarWidget(
              width: 30,
              height: 20,
              background: Colors.green,
              progressBarColor: Colors.red,
              progress: 0.8,
            ),
          ),
        ),
      );

      final Container updatedContainer = tester.widget<Container>(containerFinder);
      final double? updatedWidth = updatedContainer.constraints?.maxWidth;

      expect(updatedWidth, isNot(equals(initialWidth)));

      verifyWidget(tester, progressBarColorValue: Colors.red);
    });

    testWidgets('should update widthOfWidget on size change with progress > 1',
        (WidgetTester tester) async {
      const double fakeWidth = 30;
      // Simulate size change
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EmiCustomProgressbarWidget(
              width: fakeWidth,
              height: 20,
              background: Colors.green,
              progressBarColor: Colors.red,
              progress: 1.5,
            ),
          ),
        ),
      );

      final Finder containerFinder = find.byType(Container).last;
      final Container container = tester.widget<Container>(containerFinder);
      final double? initialWidth = container.constraints?.maxWidth;

      expect(initialWidth, fakeWidth);
    });

    testWidgets('should render correctly with zero progress', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EmiCustomProgressbarWidget(
              width: 30,
              height: 20,
              background: Colors.green,
              progressBarColor: Colors.grey,
              progress: 0,
            ),
          ),
        ),
      );

      final Finder progressBarFinder = find.byType(Container).last;
      final Container progressBar = tester.widget<Container>(progressBarFinder);
      final double? progressBarWidth = progressBar.constraints?.maxWidth;

      expect(progressBarWidth, 20);
      verifyWidget(tester, progressBarColorValue: Colors.grey);
    });
  });
}
