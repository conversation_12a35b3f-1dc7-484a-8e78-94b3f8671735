import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/emi_repo.dart';
import 'package:evoapp/data/response/emi_record_change_entity.dart';
import 'package:evoapp/data/response/emi_record_detail_entity.dart';
import 'package:evoapp/feature/emi_management/detail_screen/cubit/emi_management_detail_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';
import '../../../util/test_util.dart';

class MockEmiRepo extends Mock implements EmiRepo {}

void main() {
  late EmiManagementDetailCubit cubit;
  late EmiRepo mockEmiRepo;

  const String fakeId = 'fake_id';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerSingleton<EmiRepo>(MockEmiRepo());
    mockEmiRepo = getIt<EmiRepo>();
  });

  setUp(() {
    cubit = EmiManagementDetailCubit(emiRepo: mockEmiRepo);
  });

  tearDown(() {
    reset(mockEmiRepo);
  });

  test('initial state is EmiManagementDetailInitialState', () {
    expect(cubit.state, isA<EmiManagementDetailInitialState>());
  });

  group('getEmiRecordDetail', () {
    blocTest<EmiManagementDetailCubit, EmiManagementDetailState>(
      'emits [EmiManagementDetailLoadingState, EmiManagementDetailSucceedState] when getEmiRecordDetail is successful',
      build: () => cubit,
      setUp: () {
        when(() => mockEmiRepo.getEmiRecordDetail(
              id: any(named: 'id'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return EmiRecordDetailEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('emi_management_detail_case_success.json'),
          ));
        });

        expect(cubit.emiRecordDetailEntity, null);
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (_) {
        return cubit.getEmiRecordDetail(id: fakeId);
      },
      expect: () => <dynamic>[
        isA<EmiManagementDetailLoadingState>(),
        isA<EmiManagementDetailSucceedState>()
            .having(
              (EmiManagementDetailSucceedState state) => state.item,
              'verify item',
              isNotNull,
            )
            .having(
              (EmiManagementDetailSucceedState state) => state.item.record,
              'verify record',
              isNotNull,
            ),
      ],
      verify: (_) {
        expect(cubit.emiRecordDetailEntity, isNotNull);
      },
    );

    blocTest<EmiManagementDetailCubit, EmiManagementDetailState>(
      'emits [EmiManagementDetailLoadingState, EmiManagementDetailFailureState] when getEmiRecordDetail is failure',
      build: () => cubit,
      setUp: () {
        when(() => mockEmiRepo.getEmiRecordDetail(
              id: any(named: 'id'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return EmiRecordDetailEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.UNKNOWN_ERRORS,
            response: null,
          ));
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (_) {
        return cubit.getEmiRecordDetail(id: fakeId);
      },
      expect: () => <dynamic>[
        isA<EmiManagementDetailLoadingState>(),
        isA<EmiManagementDetailFailureState>()
            .having(
              (EmiManagementDetailFailureState state) => state.errorUIModel.statusCode,
              'verify errorUIModel',
              CommonHttpClient.UNKNOWN_ERRORS,
            )
            .having(
              (EmiManagementDetailFailureState state) => state.isRefresh,
              'verify isRefresh',
              false,
            ),
      ],
    );

    blocTest<EmiManagementDetailCubit, EmiManagementDetailState>(
      'emits [EmiManagementDetailLoadingState, EmiManagementDetailFailureState] with isRefresh = true',
      build: () => cubit,
      setUp: () {
        when(() => mockEmiRepo.getEmiRecordDetail(
              id: any(named: 'id'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return EmiRecordDetailEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.UNKNOWN_ERRORS,
            response: null,
          ));
        });
      },
      act: (_) {
        return cubit.getEmiRecordDetail(id: fakeId, isRefresh: true);
      },
      expect: () => <dynamic>[
        isA<EmiManagementDetailFailureState>()
            .having(
              (EmiManagementDetailFailureState state) => state.errorUIModel.statusCode,
              'verify errorUIModel',
              CommonHttpClient.UNKNOWN_ERRORS,
            )
            .having(
              (EmiManagementDetailFailureState state) => state.isRefresh,
              'verify isRefresh',
              true,
            ),
      ],
    );
  });

  group('getEmiRecordDetailRefresh', () {
    blocTest<EmiManagementDetailCubit, EmiManagementDetailState>(
      'emits [EmiManagementDetailLoadingState, EmiManagementDetailRefreshNotChangedState] when changed = false',
      build: () => cubit,
      setUp: () {
        when(() => mockEmiRepo.getEmiRecordDetailRefresh(
              id: any(named: 'id'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return EmiRecordRefreshEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('emi_management_detail_case_not_refresh.json'),
          ));
        });

        expect(cubit.emiRecordDetailEntity, null);
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (_) {
        cubit.emiRecordDetailEntity = EmiRecordDetailEntity();
        return cubit.getEmiRecordDetailRefresh(id: fakeId);
      },
      expect: () => <dynamic>[
        isA<EmiManagementDetailLoadingState>(),
        isA<EmiManagementDetailRefreshNotChangedState>().having(
          (EmiManagementDetailRefreshNotChangedState state) => state.item,
          'verify item',
          isNotNull,
        ),
      ],
    );

    blocTest<EmiManagementDetailCubit, EmiManagementDetailState>(
      'emits [EmiManagementDetailLoadingState, EmiManagementDetailSucceedState] when changed = true',
      build: () => cubit,
      setUp: () {
        when(() => mockEmiRepo.getEmiRecordDetailRefresh(
              id: any(named: 'id'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return EmiRecordRefreshEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('emi_management_detail_case_refresh.json'),
          ));
        });

        when(() => mockEmiRepo.getEmiRecordDetail(
              id: any(named: 'id'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return EmiRecordDetailEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('emi_management_detail_case_success.json'),
          ));
        });

        expect(cubit.emiRecordDetailEntity, null);
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (_) {
        return cubit.getEmiRecordDetailRefresh(id: fakeId);
      },
      expect: () => <dynamic>[
        isA<EmiManagementDetailLoadingState>(),
        isA<EmiManagementDetailSucceedState>()
            .having(
              (EmiManagementDetailSucceedState state) => state.item,
              'verify item',
              isNotNull,
            )
            .having(
              (EmiManagementDetailSucceedState state) => state.item.record,
              'verify record',
              isNotNull,
            ),
      ],
      verify: (_) {
        expect(cubit.emiRecordDetailEntity, isNotNull);
      },
    );

    blocTest<EmiManagementDetailCubit, EmiManagementDetailState>(
      'emits [EmiManagementDetailLoadingState, EmiManagementDetailFailureState] when getEmiRecordDetail is failure',
      build: () => cubit,
      setUp: () {
        when(() => mockEmiRepo.getEmiRecordDetailRefresh(
              id: any(named: 'id'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return EmiRecordRefreshEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.UNKNOWN_ERRORS,
            response: null,
          ));
        });

        expect(cubit.emiRecordDetailEntity, null);
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (_) {
        cubit.emiRecordDetailEntity = EmiRecordDetailEntity();
        return cubit.getEmiRecordDetailRefresh(id: fakeId);
      },
      expect: () => <dynamic>[
        isA<EmiManagementDetailLoadingState>(),
        isA<EmiManagementDetailRefreshNotChangedState>().having(
          (EmiManagementDetailRefreshNotChangedState state) => state.item,
          'verify item',
          isNotNull,
        ),
      ],
    );
  });
}
