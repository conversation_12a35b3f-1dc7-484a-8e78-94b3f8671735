import 'package:evoapp/feature/emi_management/mock/mock_emi_management_detail_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MockEmiManagementDetailCase', () {
    test('success case should return correct file name', () {
      expect(getMockEmiManagementDetailCaseFileName(MockEmiManagementDetailCase.success),
          'emi_management_detail_case_success.json');
    });

    test('fail case should return correct file name', () {
      expect(getMockEmiManagementDetailCaseFileName(MockEmiManagementDetailCase.fail),
          'emi_management_detail_case_fail.json');
    });

    test('refresh case should return correct file name', () {
      expect(getMockEmiManagementDetailCaseFileName(MockEmiManagementDetailCase.refresh),
          'emi_management_detail_case_refresh.json');
    });

    test('refresh case should return correct file name', () {
      expect(getMockEmiManagementDetailCaseFileName(MockEmiManagementDetailCase.notRefresh),
          'emi_management_detail_case_not_refresh.json');
    });
  });
}
