import 'package:evoapp/feature/appsflyer/one_link_constants.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('test OneLinkURLConstants', () {
    test('Verify value of one link base url has the right value', () {
      expect(OneLinkURLConstants.oneLinkStagingUrl, 'https://evoappvn-stag.onelink.me/Blki');
      expect(OneLinkURLConstants.oneLinkUATUrl, 'https://evoappvn-uat.onelink.me/0Fmo');
      expect(OneLinkURLConstants.oneLinkPRODUrl, 'https://evoappvn.onelink.me/QA15');
    });

    test('should return prod URL when flavor is prod', () {
      FlavorConfig(
        flavor: FlavorType.prod.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
      );
      final String result = OneLinkURLConstants.getOneLinkURLByEnv();

      expect(result, OneLinkURLConstants.oneLinkPRODUrl);
    });

    test('should return UAT URL when flavor is uat', () {
      FlavorConfig(
        flavor: FlavorType.uat.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
      );

      final String result = OneLinkURLConstants.getOneLinkURLByEnv();

      expect(result, OneLinkURLConstants.oneLinkUATUrl);
    });

    test('should return staging URL when flavor is not prod or uat', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );

      final String result = OneLinkURLConstants.getOneLinkURLByEnv();

      expect(result, OneLinkURLConstants.oneLinkStagingUrl);
    });
  });

  test('should have the correct value for OneLinkKey', () {
    expect(OneLinkKey.afDP.value, 'af_dp');
    expect(OneLinkKey.c.value, 'c');
    expect(OneLinkKey.afXp.value, 'af_xp');
    expect(OneLinkKey.pid.value, 'pid');
    expect(OneLinkKey.afForceDeeplink.value, 'af_force_deeplink');
    expect(OneLinkKey.deepLinkValue.value, 'deep_link_value');
  });

  group('EvoNFCCampaign.getByEnv', () {
    test('returns nfcProd for prod flavor', () {
      FlavorConfig(
        flavor: FlavorType.prod.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
      );

      final EvoNFCCampaign result = EvoNFCCampaign.getByEnv();

      expect(result, EvoNFCCampaign.nfcCampaignProd);
    });

    test('returns nfcUAT for uat flavor', () {
      FlavorConfig(
        flavor: FlavorType.uat.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
      );

      final EvoNFCCampaign result = EvoNFCCampaign.getByEnv();

      expect(result, EvoNFCCampaign.nfcCampaignUAT);
    });

    test('returns nfcStag for stag flavor', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );

      final EvoNFCCampaign result = EvoNFCCampaign.getByEnv();

      expect(result, EvoNFCCampaign.nfcCampaignStag);
    });
  });

  group('test EvoOneLinkCampaign', () {
    test('should have the correct value for EvoOneLinkCampaign in staging', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );
      expect(EvoOneLinkCampaign.nfcCampaign, EvoNFCCampaign.nfcCampaignStag.value);
    });

    test('should have the correct value for EvoOneLinkCampaign in UAT', () {
      FlavorConfig(
        flavor: FlavorType.uat.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
      );
      expect(EvoOneLinkCampaign.nfcCampaign, EvoNFCCampaign.nfcCampaignUAT.value);
    });

    test('should have the correct value for EvoOneLinkCampaign in Prod', () {
      FlavorConfig(
        flavor: FlavorType.prod.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
      );

      expect(EvoOneLinkCampaign.nfcCampaign, EvoNFCCampaign.nfcCampaignProd.value);
    });
  });
}
