import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:evoapp/feature/login/old_device/login_on_old_device_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('LoginOnOldDeviceInitial', () {
    test('should return correct type', () {
      final LoginOnOldDeviceInitial state = LoginOnOldDeviceInitial();

      expect(state, isA<LoginOnOldDeviceState>());
    });

    test('should store isBiometricLoginAvailable correctly', () {
      final LoginOnOldDeviceInitial state1 = LoginOnOldDeviceInitial(
        isBiometricLoginAvailable: true,
      );
      final LoginOnOldDeviceInitial state2 = LoginOnOldDeviceInitial();

      expect(state1.isBiometricLoginAvailable, isTrue);
      expect(state2.isBiometricLoginAvailable, isFalse);
    });

    test('should store phoneNumber correctly', () {
      const String phoneNumber = '1234567890';
      final LoginOnOldDeviceInitial state = LoginOnOldDeviceInitial(phoneNumber: phoneNumber);

      expect(state.phoneNumber, equals(phoneNumber));
    });
  });

  group('LoginOnOldDeviceLoading', () {
    test('should return correct type', () {
      final LoginOnOldDeviceLoading state = LoginOnOldDeviceLoading();

      expect(state, isA<LoginOnOldDeviceState>());
    });
  });

  group('LoginOnOldDeviceError', () {
    test('should return correct type', () {
      final ErrorUIModel error = ErrorUIModel(statusCode: 400, userMessage: 'Error');
      const TypeLogin loginType = TypeLogin.otp;
      final LoginOnOldDeviceError state = LoginOnOldDeviceError(error, loginType);

      expect(state, isA<LoginOnOldDeviceState>());

      expect(state.error, error);
      expect(state.loginType, loginType);
    });
  });

  group('LoginOnOldDeviceSuccess', () {
    test('should return correct type', () {
      const TypeLogin loginType = TypeLogin.otp;
      final ActionEntity actionEntity = ActionEntity();
      const String challengeType = 'challengeType';
      final LoginOnOldDeviceSuccess state = LoginOnOldDeviceSuccess(
        loginType: loginType,
        entity: SignInOtpEntity(
          action: actionEntity,
          challengeType: challengeType,
        ),
      );

      expect(state, isA<LoginOnOldDeviceState>());
      expect(state.loginType, loginType);
      expect(state.entity?.action, actionEntity);
      expect(state.entity?.challengeType, challengeType);
    });
  });
}
