import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:evoapp/feature/biometric_pin_confirm/biometric_and_pin_confirmation.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';

import '../../../util/flutter_test_config.dart';

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockJwtHelper extends Mock implements JwtHelper {}

class MockConfirmBiometricAndPinCallback extends Mock implements ConfirmBiometricAndPinCallback {}

void main() {
  late BiometricAndPinConfirmation biometricAndPinConfirmation;
  late MockBiometricsAuthenticate mockBiometricsAuthenticate;
  late MockEvoLocalStorageHelper mockEvoLocalStorageHelper;
  late MockJwtHelper mockJwtHelper;
  late MockConfirmBiometricAndPinCallback mockCallback;
  final MockGlobalKeyProvider mockGlobalKeyProvider = MockGlobalKeyProvider();

  setUpAll(() {
    getIt.registerSingleton<GlobalKeyProvider>(mockGlobalKeyProvider);
  });

  setUp(() {
    mockBiometricsAuthenticate = MockBiometricsAuthenticate();
    mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
    mockJwtHelper = MockJwtHelper();
    mockCallback = MockConfirmBiometricAndPinCallback();
    when(() => mockGlobalKeyProvider.navigatorContext).thenReturn(null);
    biometricAndPinConfirmation = BiometricAndPinConfirmation(
      biometricsAuthenticate: mockBiometricsAuthenticate,
      secureStorageHelper: mockEvoLocalStorageHelper,
      jwtHelper: mockJwtHelper,
    );
  });

  group('BiometricAndPinConfirmation', () {
    test('should call onPinInputConfirm when onInputPin is called', () {
      const String pin = '123456';
      biometricAndPinConfirmation.callback = mockCallback;
      biometricAndPinConfirmation.onInputPin(pin);

      verify(() => mockCallback.onPinInputConfirm(pin)).called(1);
    });

    test('should show pin popup when isForcePin is true', () async {
      when(() => mockEvoLocalStorageHelper.isEnableBiometricAuthenticator())
          .thenAnswer((_) async => true);
      when(() => mockEvoLocalStorageHelper.getBiometricToken())
          .thenAnswer((_) async => 'biometric_token');
      when(() => mockJwtHelper.isCanUse(any())).thenReturn(true);
      when(() => mockBiometricsAuthenticate.authenticate(localizedReason: any(named: 'localizedReason')))
          .thenAnswer((_) async => BioAuthResult(isAuthSuccess: true));

      await biometricAndPinConfirmation.confirm(
        callback: mockCallback,
        isForcePin: true,
      );

      verify(() => mockCallback.onConfirmPinPopupClosed()).called(1);
    });

    test('should call onBiometricConfirm when biometric authentication is successful', () async {
      when(() => mockEvoLocalStorageHelper.isEnableBiometricAuthenticator())
          .thenAnswer((_) async => true);
      when(() => mockEvoLocalStorageHelper.getBiometricToken())
          .thenAnswer((_) async => 'biometric_token');
      when(() => mockJwtHelper.isCanUse(any())).thenReturn(true);
      when(() => mockBiometricsAuthenticate.authenticate(localizedReason: any(named: 'localizedReason')))
          .thenAnswer((_) async => BioAuthResult(isAuthSuccess: true));

      await biometricAndPinConfirmation.confirm(
        callback: mockCallback,
      );

      verify(() => mockCallback.onBiometricConfirm('biometric_token')).called(1);
    });

    test('should show pin popup when biometric authentication fails', () async {
      when(() => mockEvoLocalStorageHelper.isEnableBiometricAuthenticator())
          .thenAnswer((_) async => true);
      when(() => mockEvoLocalStorageHelper.getBiometricToken())
          .thenAnswer((_) async => 'biometric_token');
      when(() => mockJwtHelper.isCanUse(any())).thenReturn(true);
      when(() => mockBiometricsAuthenticate.authenticate(localizedReason: any(named: 'localizedReason')))
          .thenAnswer((_) async => BioAuthResult(isAuthSuccess: false));

      await biometricAndPinConfirmation.confirm(
        callback: mockCallback,
      );

      verify(() => mockCallback.onConfirmPinPopupClosed()).called(1);
    });
  });
}