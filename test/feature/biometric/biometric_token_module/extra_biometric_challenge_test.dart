import 'package:evoapp/feature/biometric/biometric_token_module/extra_biometric_challenge.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

class MockBiometricChallengeCallback implements BiometricChallengeCallback {
  bool successCalled = false;
  bool errorCalled = false;
  bool cancelCalled = false;
  String? receivedToken;
  ErrorUIModel? receivedError;

  @override
  void onBioChallengeSuccess(String? biometricToken) {
    successCalled = true;
    receivedToken = biometricToken;
  }

  @override
  void onBioChallengeError(ErrorUIModel error) {
    errorCalled = true;
    receivedError = error;
  }

  @override
  void onBioChallengeCancel() {
    cancelCalled = true;
  }
}

// Concrete implementation of BiometricChallengeWidget for testing
class TestBiometricChallengeWidget extends BiometricChallengeWidget {
  const TestBiometricChallengeWidget({super.key, super.callback});

  @override
  State<StatefulWidget> createState() => _TestBiometricChallengeState();
}

class _TestBiometricChallengeState extends BiometricChallengeState<TestBiometricChallengeWidget> {
  @override
  Widget build(BuildContext context) {
    return Container();
  }

  // Methods to simulate biometric challenge results
  void simulateSuccess(String? token) {
    widget.callback?.onBioChallengeSuccess(token);
  }

  void simulateError(ErrorUIModel error) {
    widget.callback?.onBioChallengeError(error);
  }

  void simulateCancel() {
    widget.callback?.onBioChallengeCancel();
  }
}

void main() {
  group('BiometricChallengeWidget', () {
    late MockBiometricChallengeCallback mockCallback;

    setUp(() {
      mockCallback = MockBiometricChallengeCallback();
    });

    testWidgets('calls onBioChallengeSuccess on success', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TestBiometricChallengeWidget(callback: mockCallback),
        ),
      );

      final _TestBiometricChallengeState state = tester.state<_TestBiometricChallengeState>(
          find.byType(TestBiometricChallengeWidget));

      state.simulateSuccess('test_token');

      expect(mockCallback.successCalled, isTrue);
      expect(mockCallback.receivedToken, 'test_token');
    });

    testWidgets('calls onBioChallengeError on error', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TestBiometricChallengeWidget(callback: mockCallback),
        ),
      );

      final _TestBiometricChallengeState state = tester.state<_TestBiometricChallengeState>(
          find.byType(TestBiometricChallengeWidget));

      final ErrorUIModel error = ErrorUIModel(userMessage: 'Test error');
      state.simulateError(error);

      expect(mockCallback.errorCalled, isTrue);
      expect(mockCallback.receivedError, error);
    });

    testWidgets('calls onBioChallengeCancel on cancel', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TestBiometricChallengeWidget(callback: mockCallback),
        ),
      );

      final _TestBiometricChallengeState state = tester.state<_TestBiometricChallengeState>(
          find.byType(TestBiometricChallengeWidget));

      state.simulateCancel();

      expect(mockCallback.cancelCalled, isTrue);
    });
  });
}