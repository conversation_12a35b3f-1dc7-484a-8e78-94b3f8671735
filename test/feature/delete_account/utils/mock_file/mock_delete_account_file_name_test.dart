import 'package:evoapp/feature/delete_account/utils/mock_file/mock_delete_account_file_name.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('returns correct JSON file for each DeleteAccountMockCase', () {
    expect(
      getDeleteAccountMock(DeleteAccountMockCase.success),
      'deletion_request_confirmation_success.json',
    );
    expect(
      getDeleteAccountMock(DeleteAccountMockCase.fail),
      'deletion_request_confirmation_fail.json',
    );
    expect(
      getDeleteAccountMock(DeleteAccountMockCase.expiredDeleteAccountSession),
      'deletion_request_confirmation_expired_delete_account_session.json',
    );
  });
}
