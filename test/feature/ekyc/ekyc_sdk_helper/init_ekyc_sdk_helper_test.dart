import 'package:evoapp/data/repository/ekyc_repo.dart';
import 'package:evoapp/data/response/ekyc_info_entity.dart';
import 'package:evoapp/feature/ekyc/ekyc_bridge/ekyc_bridge.dart';
import 'package:evoapp/feature/ekyc/ekyc_sdk_helper/init_ekyc_sdk_helper_impl.dart';
import 'package:evoapp/feature/ekyc/model/ekyc_for_flow_type.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/test_util.dart';

class MockEKYCSessionEntity extends Mock {
  Future<EKYCSessionEntity> mock() async {
    const String mockFileName = 'ekyc_session_initial_value.json';
    final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

    return EKYCSessionEntity.fromBaseResponse(
        BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
  }

  Future<EKYCSessionEntity> mockFail() async {
    return EKYCSessionEntity.fromBaseResponse(
        BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null));
  }
}

class MockEKYCRepo extends Mock implements EKYCRepo {}

class MockEKYCBridge extends Mock implements EkycBridge {}

void main() {
  late EKYCRepo mockEKYCRepo;
  late EkycBridge mockEKYCBridge;
  late MockEKYCSessionEntity mockEKYCSessionEntity;
  late InitEKYCSdkHelperImpl initEKYCSdkHelperImpl;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<EKYCRepo>(() => MockEKYCRepo());
    getIt.registerLazySingleton<EkycBridge>(() => MockEKYCBridge());
    mockEKYCRepo = getIt.get<EKYCRepo>();
    mockEKYCBridge = getIt.get<EkycBridge>();
    mockEKYCSessionEntity = MockEKYCSessionEntity();
    initEKYCSdkHelperImpl = InitEKYCSdkHelperImpl();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify createNewEKYCSessionIfNeeded() method', () {
    setUp(() async {
      final EKYCSessionEntity entity = await mockEKYCSessionEntity.mock();

      when(() => mockEKYCBridge.getSession()).thenAnswer((_) => entity);

      when(() => mockEKYCRepo.createEKYCSession(
          flowType: EkycFlowType.linkCard,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => entity);
    });

    tearDown(() {
      reset(mockEKYCRepo);
      reset(mockEKYCBridge);
    });

    test(
        'test createNewEKYCSessionIfNeeded() method with forceCreateNewSession = false and eKYCSessionEntity != null',
        () async {
      final EKYCSessionEntity actualEntity =
          await initEKYCSdkHelperImpl.createNewEKYCSessionIfNeeded(
              flowType: EkycFlowType.linkCard, forceCreateNewSession: false);

      expect(actualEntity.sessionToken, 'fake_session_token_2');

      verifyNever(() => mockEKYCRepo.createEKYCSession(
          flowType: EkycFlowType.linkCard, mockConfig: any(named: 'mockConfig')));
    });

    test(
        'test createNewEKYCSessionIfNeeded() method with forceCreateNewSession = true and get eKYCSessionEntity == null',
        () async {
      when(() => mockEKYCBridge.getSession()).thenAnswer((_) => null);

      final EKYCSessionEntity actualEntity =
          await initEKYCSdkHelperImpl.createNewEKYCSessionIfNeeded(
              flowType: EkycFlowType.linkCard, forceCreateNewSession: true);

      verify(() => mockEKYCRepo.createEKYCSession(
          flowType: EkycFlowType.linkCard, mockConfig: any(named: 'mockConfig'))).called(1);

      expect(actualEntity.statusCode, CommonHttpClient.SUCCESS);
      expect(actualEntity.sessionToken, 'fake_session_token_2');

      verify(() => mockEKYCBridge.setSession(any())).called(1);
    });

    test(
        'test createNewEKYCSessionIfNeeded() method with forceCreateNewSession = true and get eKYCSessionEntity == null but eKYCRepo.createEKYCSession return fail',
        () async {
      when(() => mockEKYCBridge.getSession()).thenAnswer((_) => null);

      final EKYCSessionEntity entity = await mockEKYCSessionEntity.mockFail();
      when(() => mockEKYCRepo.createEKYCSession(
          flowType: EkycFlowType.linkCard,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => entity);

      final EKYCSessionEntity actualEntity =
          await initEKYCSdkHelperImpl.createNewEKYCSessionIfNeeded(
              flowType: EkycFlowType.linkCard, forceCreateNewSession: true);

      verify(() => mockEKYCRepo.createEKYCSession(
          flowType: EkycFlowType.linkCard, mockConfig: any(named: 'mockConfig'))).called(1);

      expect(actualEntity.statusCode, CommonHttpClient.BAD_REQUEST);
      expect(actualEntity.sessionToken, null);

      verifyNever(() => mockEKYCBridge.setSession(any()));
    });

    test(
        'test createNewEKYCSessionIfNeeded() method with forceCreateNewSession = true and get eKYCSessionEntity != null',
        () async {
      final EKYCSessionEntity actualEntity =
          await initEKYCSdkHelperImpl.createNewEKYCSessionIfNeeded(
              flowType: EkycFlowType.linkCard, forceCreateNewSession: true);

      verify(() => mockEKYCRepo.createEKYCSession(
          flowType: EkycFlowType.linkCard, mockConfig: any(named: 'mockConfig'))).called(1);

      expect(actualEntity.sessionToken, 'fake_session_token_2');
      verifyNever(() => mockEKYCRepo.createEKYCSession(
          flowType: EkycFlowType.linkCard, mockConfig: any(named: 'mockConfig')));
    });
  });

  group('createEKYCSessionWithPreviousSession', () {
    setUp(() {
      when(() => mockEKYCBridge.resetFlow()).thenAnswer((_) => Future<void>.value());
      when(() => mockEKYCBridge.setSession(any())).thenAnswer((_) => Future<void>.value());
    });

    test('should reset flow and set session with provided entity', () async {
      final EKYCSessionEntity entity = EKYCSessionEntity();

      final EKYCSessionEntity? result =
          await initEKYCSdkHelperImpl.createEKYCSessionWithPreviousSession(entity: entity);

      verify(() => mockEKYCBridge.resetFlow()).called(1);
      verify(() => mockEKYCBridge.setSession(entity)).called(1);
      expect(result, entity);
    });

    test('should reset flow and set session with null entity', () async {
      final EKYCSessionEntity? result =
          await initEKYCSdkHelperImpl.createEKYCSessionWithPreviousSession();

      verify(() => mockEKYCBridge.resetFlow()).called(1);
      verify(() => mockEKYCBridge.setSession(null)).called(1);
      expect(result, null);
    });
  });
}
