import 'package:evoapp/feature/ekyc/widgets/ekyc_block_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';
import '../../profile/profile_screen/card_status/widget/card_cta_widget/card_cta_button_group_test.dart';

class MockCallback extends Mock {
  void call();
}

void main() {
  const double expectedImageHeightInPercentage = 0.23;
  const Size defaultSizeScreen = Size(1080, 1920);
  const String expectedTitle = 'fake_title';
  const String expectedDescription = 'fake_description';
  const String expectedImageUrl = 'fake_imageUrl';
  const String expectedButtonTitle = 'fake_buttonTitle';
  const double expectedImageHeight = 350;

  late CommonImageProvider commonImageProvider;
  late MockCallback mockCallback;
  late TextStyle? expectedTextStyle;

  void initWhenMethod() {
    when(
      () => commonImageProvider.asset(
        any(),
        height: any(named: 'height'),
        fit: any(named: 'fit'),
      ),
    ).thenReturn(const SizedBox());

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(expectedImageHeight);
  }

  setUpAll(() {
    registerFallbackValue(MockBuildContext());
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterTextStyle();
    getItRegisterColor();
    getItRegisterButtonStyle();
    setUtilsMockInstanceForTesting();

    mockCallback = MockCallback();

    commonImageProvider = getIt.get<CommonImageProvider>();

    expectedTextStyle = evoButtonStyles
        .primary(ButtonSize.xLarge)
        .textStyle
        ?.resolve(<WidgetState>{WidgetState.disabled});

    initWhenMethod();
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  Widget initWidget({double? imageHeightInPercentage}) {
    final Widget widget = imageHeightInPercentage != null
        ? EKYCBlockWidget(
            description: expectedDescription,
            imageUrl: expectedImageUrl,
            title: expectedTitle,
            buttonTitle: expectedButtonTitle,
            onButtonTap: mockCallback.call,
            imageHeightInPercentage: imageHeightInPercentage,
          )
        : EKYCBlockWidget(
            description: expectedDescription,
            imageUrl: expectedImageUrl,
            title: expectedTitle,
            buttonTitle: expectedButtonTitle,
            onButtonTap: mockCallback.call,
          );
    return MaterialApp(home: Scaffold(body: widget));
  }

  void verifyImageHeightInPercentage(
    WidgetTester widgetTester, {
    required double imageHeightInPercentage,
  }) {
    final Finder widgetFinder = find.byType(EKYCBlockWidget);
    expect(widgetFinder, findsOneWidget);

    final EKYCBlockWidget widget = widgetTester.widget(widgetFinder);
    expect(widget.imageHeightInPercentage, imageHeightInPercentage);
  }

  void verifySizeBox(WidgetTester widgetTester, {double? sizeBoxHeight}) {
    final Finder sizeBoxCustomFinder = find.byWidgetPredicate((Widget widget) {
      if (widget is SizedBox) {
        return widget.height == sizeBoxHeight;
      }
      return false;
    });
    expect(sizeBoxCustomFinder, findsOneWidget);
  }

  void verifyItemImage(double heightPercentage) {
    verify(() => commonImageProvider.asset(
          expectedImageUrl,
          height: expectedImageHeight,
          fit: any(named: 'fit'),
        )).called(1);

    verify(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: heightPercentage,
        )).called(1);
  }

  void verifyItemTitle(WidgetTester widgetTester) {
    final Finder textFinder = find.text(expectedTitle);
    expect(textFinder, findsOneWidget);

    final Text titleText = widgetTester.widget(textFinder);
    expect(titleText.style, evoTextStyles.h300());
    expect(titleText.textAlign, TextAlign.center);
  }

  void verifyItemDescription(WidgetTester widgetTester) {
    final Finder textFinder = find.text(expectedDescription);
    expect(textFinder, findsOneWidget);

    final Text descriptionText = widgetTester.widget(textFinder);
    expect(descriptionText.style, evoTextStyles.bodyMedium(evoColors.textPassive));
    expect(descriptionText.maxLines, 2);
    expect(descriptionText.overflow, TextOverflow.ellipsis);
    expect(descriptionText.textAlign, TextAlign.center);
  }

  Future<void> verifyItemButton(WidgetTester widgetTester) async {
    final Finder buttonFinder = find.byType(CommonButton);
    expect(buttonFinder, findsOneWidget);

    final CommonButton button = widgetTester.widget(buttonFinder);
    final TextStyle? buttonTextStyle =
        button.style.textStyle?.resolve(<WidgetState>{WidgetState.disabled});

    expect(buttonTextStyle, expectedTextStyle);

    expect(button.isWrapContent, false);
    expect(find.text(expectedButtonTitle), findsOneWidget);

    // verify on tap
    await widgetTester.tap(buttonFinder);
    await widgetTester.pumpAndSettle();

    verify(() => mockCallback.call()).called(1);
  }

  Future<void> verifyUIWidget(WidgetTester widgetTester, {required double heightPercentage}) async {
    // verify sized box height with width device
    verifySizeBox(widgetTester, sizeBoxHeight: defaultSizeScreen.width * 0.1);

    // verify sized box height 8
    verifySizeBox(widgetTester, sizeBoxHeight: 8);

    // verify sized box height 24
    verifySizeBox(widgetTester, sizeBoxHeight: 24);

    // verify sized box height 44
    verifySizeBox(widgetTester, sizeBoxHeight: 44);

    // verify item image
    verifyItemImage(heightPercentage);

    // verify item title
    verifyItemTitle(widgetTester);

    // verify item description
    verifyItemDescription(widgetTester);

    // verify item button
    await verifyItemButton(widgetTester);

    resetConfigChangeScreenSize(widgetTester);
  }

  group('test EKYCBlockWidget', () {
    testWidgets('verify UI EKYCBlockWidget with default value', (WidgetTester widgetTester) async {
      // change screen size to 1080x1920
      initConfigChangeScreenSize(widgetTester, size: defaultSizeScreen);

      await widgetTester.pumpWidget(initWidget());

      // verify image height in percentage
      verifyImageHeightInPercentage(widgetTester,
          imageHeightInPercentage: expectedImageHeightInPercentage);

      await verifyUIWidget(widgetTester, heightPercentage: expectedImageHeightInPercentage);
    });

    testWidgets('verify UI EKYCBlockWidget with custom imageHeightInPercentage',
        (WidgetTester widgetTester) async {
      const double customImageHeightInPercentage = 0.5;

      // change screen size to 1080x1920
      initConfigChangeScreenSize(widgetTester, size: defaultSizeScreen);

      await widgetTester
          .pumpWidget(initWidget(imageHeightInPercentage: customImageHeightInPercentage));

      // verify image height in percentage
      verifyImageHeightInPercentage(widgetTester,
          imageHeightInPercentage: customImageHeightInPercentage);

      await verifyUIWidget(widgetTester, heightPercentage: customImageHeightInPercentage);
    });
  });
}
