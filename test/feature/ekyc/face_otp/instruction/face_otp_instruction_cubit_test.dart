import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/response/ekyc_info_entity.dart';
import 'package:evoapp/feature/ekyc/ekyc_bridge/ekyc_bridge.dart';
import 'package:evoapp/feature/ekyc/face_otp/instruction/face_otp_instruction_cubit.dart';
import 'package:evoapp/feature/ekyc/model/ekyc_result_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/resources/ui_strings.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';
import '../../../../util/test_util.dart';

class MockEkycBridge extends Mock implements EkycBridge {}

void main() {
  late MockEkycBridge mockEkycBridge;
  late FaceOTPInstructionCubit cubit;
  const String mockFileName = 'ekyc_session_initial_value.json';
  late EKYCSessionEntity ekycSessionEntity;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockEkycBridge = MockEkycBridge();

    final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);
    ekycSessionEntity = EKYCSessionEntity.fromBaseResponse(
        BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    cubit = FaceOTPInstructionCubit(mockEkycBridge);
  });

  tearDown(() {
    cubit.userStartedFaceOTP = false;
    cubit.close();
    reset(mockEkycBridge);
  });

  group('test constructor()', () {
    test('create new instance InstructionFaceOTPCubit with InstructionFaceOTPInitialState state',
        () {
      cubit = FaceOTPInstructionCubit(mockEkycBridge);
      expect(cubit.state is FaceOTPInstructionInitialState, isTrue);
    });
  });

  group('test method initTrustVisionSDK()', () {
    blocTest<FaceOTPInstructionCubit, FaceOTPInstructionState>(
        'test initTrustVisionSDK is processing',
        setUp: () async {
          cubit.userStartedFaceOTP = true;
        },
        build: () => cubit,
        act: (FaceOTPInstructionCubit cubit) => cubit.initTrustVisionSDK(),
        expect: () => <dynamic>[],
        verify: (_) {
          verifyNever(() => mockEkycBridge.getSession());
          verifyNever(() => mockEkycBridge.initEkyc(
              accessKeyId: any(named: 'accessKeyId'),
              accessKeySecret: any(named: 'accessKeySecret'),
              endpoint: any(named: 'endpoint'),
              xRequestId: any(named: 'xRequestId'),
              xLenderRequestId: any(named: 'xLenderRequestId')));
        });

    blocTest<FaceOTPInstructionCubit, FaceOTPInstructionState>('test initTrustVisionSDK is success',
        setUp: () async {
          cubit.userStartedFaceOTP = false;
          when(() => mockEkycBridge.initEkyc(
                accessKeyId: any(named: 'accessKeyId'),
                accessKeySecret: any(named: 'accessKeySecret'),
                endpoint: any(named: 'endpoint'),
                xRequestId: any(named: 'xRequestId'),
                xLenderRequestId: any(named: 'xLenderRequestId'),
              )).thenAnswer((_) async => TVSDKResult.succeed());

          when(() => mockEkycBridge.getSession()).thenAnswer((_) => ekycSessionEntity);
        },
        build: () => cubit,
        act: (FaceOTPInstructionCubit cubit) => cubit.initTrustVisionSDK(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<FaceOTPInstructionLoadingState>(),
              isA<FaceOTPInstructionStartNow>(),
            ],
        verify: (_) {
          verify(() => mockEkycBridge.getSession()).called(1);
          verify(() => mockEkycBridge.initEkyc(
              accessKeyId: ekycSessionEntity.accessKey,
              accessKeySecret: ekycSessionEntity.secretKey,
              endpoint: ekycSessionEntity.eKYCClientUrl,
              xRequestId: ekycSessionEntity.xRequestId,
              xLenderRequestId: ekycSessionEntity.xLenderRequestId)).called(1);
          expect(cubit.userStartedFaceOTP, false);
        });

    blocTest<FaceOTPInstructionCubit, FaceOTPInstructionState>('test initTrustVisionSDK is fail',
        setUp: () async {
          cubit.userStartedFaceOTP = false;
          when(() => mockEkycBridge.initEkyc(
                accessKeyId: any(named: 'accessKeyId'),
                accessKeySecret: any(named: 'accessKeySecret'),
                endpoint: any(named: 'endpoint'),
                xRequestId: any(named: 'xRequestId'),
                xLenderRequestId: any(named: 'xLenderRequestId'),
              )).thenAnswer((_) async => TVSDKResult.failed(reason: TVSDKFailReason.unknown));

          when(() => mockEkycBridge.getSession()).thenAnswer((_) => ekycSessionEntity);
        },
        build: () => cubit,
        act: (FaceOTPInstructionCubit cubit) => cubit.initTrustVisionSDK(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<FaceOTPInstructionLoadingState>(),
              isA<FaceOTPInstructionFailState>().having(
                  (FaceOTPInstructionFailState error) => error.errorUIModel.userMessage,
                  'Return error msg',
                  CommonStrings.otherGenericErrorMessage),
            ],
        verify: (_) {
          verify(() => mockEkycBridge.getSession()).called(1);
          verify(() => mockEkycBridge.initEkyc(
              accessKeyId: ekycSessionEntity.accessKey,
              accessKeySecret: ekycSessionEntity.secretKey,
              endpoint: ekycSessionEntity.eKYCClientUrl,
              xRequestId: ekycSessionEntity.xRequestId,
              xLenderRequestId: ekycSessionEntity.xLenderRequestId)).called(1);
          expect(cubit.userStartedFaceOTP, false);
        });
  });
}
