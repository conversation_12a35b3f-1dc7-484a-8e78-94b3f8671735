import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/response/campaign_list_entity.dart';
import 'package:evoapp/feature/campaign_list/campaign_list_page.dart';
import 'package:evoapp/feature/campaign_list/campaign_view/campaign_view.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/mock_file_name_utils/mock_campaign_file_name.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test.dart';
import '../../base/evo_page_state_base_test_config.dart';
import '../../util/flutter_test_config.dart';
import '../home/<USER>/home_campaign_cubit_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  late CampaignRepo mockCampaignRepo;
  late CommonNavigatorObserver mockNavigatorObserver;
  late EvoSnackBar mockEvoSnackBar;

  setUpAll(() {
    registerFallbackValue(SnackBarType.neutral);
  });

  setUp(() {
    mockCampaignRepo = MockCampaignRepo();
    mockEvoSnackBar = MockEvoSnackBar();
    EvoAuthenticationHelper.setInstanceForTesting(MockEvoAuthenticationHelper());
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
    mockNavigatorObserver = getIt.get<CommonNavigatorObserver>();
    when(() => mockNavigatorObserver.topStackIsAPageRoute()).thenAnswer((_) {
      return true;
    });

    //mock snack bar
    getIt.registerSingleton<EvoSnackBar>(mockEvoSnackBar);

    when(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInSec: any(named: 'durationInSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).thenAnswer((_) async {
      return Future<bool?>.value();
    });

    setupMockImageProvider();

    getIt.registerLazySingleton(() => mockCampaignRepo);

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenAnswer((_) => 10);
  });

  tearDown(() {
    EvoAuthenticationHelper.resetToOriginalInstance();
    getIt.reset();
  });

  testWidgets('CampaignListPage test', (WidgetTester tester) async {
    final BaseResponse mockResponse = await getMockBaseResponse(getOffersMockFileName());
    when(() => mockCampaignRepo.getOffers(
            flowType: FlowType.offers, mockConfig: any(named: 'mockConfig')))
        .thenAnswer((_) async => CampaignListEntity.fromBaseResponse(mockResponse));

    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: CampaignListPage(),
      ),
    ));
    final Finder campaignViewFinder = find.byType(CampaignView);
    expect(campaignViewFinder, findsOneWidget);
    final CampaignView campaignView = tester.widget<CampaignView>(campaignViewFinder);
    campaignView.onErrorUIModel?.call(ErrorUIModel(statusCode: CommonHttpClient.UNKNOWN_ERRORS));
    verify(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInSec: any(named: 'durationInSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).called(1);
    await tester.pumpWidget(Container());
    await tester.pumpAndSettle();
  });
}
