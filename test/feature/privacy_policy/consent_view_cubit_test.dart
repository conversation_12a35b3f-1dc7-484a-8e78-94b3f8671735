import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/response/decree_version_entity.dart';
import 'package:evoapp/data/response/private_policy_entity.dart';
import 'package:evoapp/feature/privacy_policy/cubit/consent_view_cubit.dart';
import 'package:evoapp/feature/privacy_policy/cubit/consent_view_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late ConsentViewCubit cubit;

  setUp(() {
    cubit = ConsentViewCubit();
  });

  test('initial state is ConsentViewInitState', () {
    expect(cubit.state, isA<ConsentViewState>());
  });

  group('test loadConsentView() function', () {
    final PrivacyPolicyEntity privatePolicyEntity = PrivacyPolicyEntity(
        consented: true,
        privacyPolicyTitle: 'privacyPolicyTitle',
        createdAt: 'createdAt',
        decreeVersion: DecreeVersionEntity(
          url: 'url',
          version: 1,
          id: 1,
          effectiveFrom: 'effectiveFrom',
        ));

    blocTest<ConsentViewCubit, ConsentViewState>(
      'should emit [ConsentViewLoadedState] when loadConsentView() is called',
      build: () => cubit,
      act: (ConsentViewCubit cubit) => cubit.loadConsentView(privatePolicyEntity),
      expect: () => <dynamic>[
        isA<ConsentViewLoadedState>()
            .having((ConsentViewLoadedState state) => state.entity?.privacyPolicyTitle,
                'verify privacyPolicyTitle correct ', privatePolicyEntity.privacyPolicyTitle)
            .having((ConsentViewLoadedState state) => state.entity?.consented,
                'verify consented correct ', privatePolicyEntity.consented)
            .having((ConsentViewLoadedState state) => state.entity?.decreeVersion?.id,
                'verify decreeVersion id correct ', privatePolicyEntity.decreeVersion?.id)
            .having((ConsentViewLoadedState state) => state.entity?.decreeVersion?.url,
                'verify decreeVersion url correct ', privatePolicyEntity.decreeVersion?.url),
      ],
    );
  });
}
