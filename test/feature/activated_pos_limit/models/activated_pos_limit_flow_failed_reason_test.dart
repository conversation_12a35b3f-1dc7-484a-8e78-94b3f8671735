import 'package:evoapp/feature/activated_pos_limit/models/activated_pos_limit_flow_failed_reason.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ActivatedPOSLimitFlowFailedReason', () {
    test('should return correct value for each enum case', () {
      expect(ActivatedPOSLimitFlowFailedReason.userCancelled.value, 'user_cancelled');
      expect(
          ActivatedPOSLimitFlowFailedReason.userNotActivatedCard.value, 'user_not_activated_card');
      expect(ActivatedPOSLimitFlowFailedReason.userNotSetPosLimit.value, 'user_not_set_pos_limit');
      expect(ActivatedPOSLimitFlowFailedReason.userPayAgain.value, 'user_pay_again');
      expect(ActivatedPOSLimitFlowFailedReason.redirectToTPBApp.value, 'redirect_tpb_app');
      expect(ActivatedPOSLimitFlowFailedReason.unknown.value, 'unknown');
    });
  });
}
