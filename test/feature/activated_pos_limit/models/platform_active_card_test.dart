import 'package:evoapp/feature/activated_pos_limit/models/platform_active_card.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PlatformActiveCard', () {
    test('PlatformActiveCard.tpBank has correct value', () {
      expect(PlatformActiveCard.tpBank.value, 'tpbank');
      expect(PlatformActiveCard.evo.value, 'evo');
      expect(PlatformActiveCard.userBypassed.value, 'user_bypassed');
    });
  });
}
