import 'package:evoapp/feature/activated_pos_limit/widgets/active_pos_limit_slider_widget.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockContext extends Mock implements BuildContext {}

void main() {
  late CommonImageProvider mockCommonImageProvider;

  const List<String> images = <String>['assets/image1.png', 'assets/image2.png'];
  const double defaultHeight = 100;
  const double defaultWidth = 50;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockContext());

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    setUtilsMockInstanceForTesting();
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(defaultHeight);

    when(() => EvoUiUtils().calculateHorizontalSpace(
          context: any(named: 'context'),
          widthPercentage: any(named: 'widthPercentage'),
        )).thenReturn(defaultWidth);
  });

  testWidgets('ActivePosLimitSliderWidget displays images', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: ActivePosLimitSliderWidget(
          images: images,
        ),
      ),
    );

    expect(find.byType(PageView), findsOneWidget);

    verify(() => mockCommonImageProvider.asset(
          any(),
          width: defaultWidth + (defaultWidth * (1 - 0.82)),
          fit: BoxFit.fitHeight,
        )).called(images.length);
  });

  testWidgets('ActivePosLimitSliderWidget calls onPageChanged when page is changed',
      (WidgetTester tester) async {
    int currentIndex = 0;

    await tester.pumpWidget(
      MaterialApp(
        home: ActivePosLimitSliderWidget(
          images: images,
          onPageChanged: (int index) {
            currentIndex = index;
          },
        ),
      ),
    );

    await tester.drag(find.byType(PageView), const Offset(-400, 0));
    await tester.pumpAndSettle();

    expect(currentIndex, 1);
  });

  testWidgets('ActivePosLimitSliderWidget shows nothing when images list is empty',
      (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: ActivePosLimitSliderWidget(
          images: <String>[],
        ),
      ),
    );

    expect(find.byType(PageView), findsNothing);
  });
}
