import 'package:evoapp/feature/activated_pos_limit/models/activated_pos_limit_flow_failed_reason.dart';
import 'package:evoapp/feature/activated_pos_limit/utils/activated_pos_limit_flow/activated_pos_limit_flow.dart';
import 'package:evoapp/feature/activated_pos_limit/utils/activated_pos_limit_flow_callback.dart';
import 'package:evoapp/feature/activated_pos_limit/utils/activated_pos_limit_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockAppState extends Mock implements AppState {}

class MockActivatedPOSLimitFlowCallback extends Mock implements ActivatedPOSLimitFlowCallback {}

class MockBuildContext extends Mock implements BuildContext {}

class TestActivatedPOSLimitFlowPayload extends ActivatedPOSLimitFlowPayload {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

void main() {
  late ActivatedPOSLimitFlowImpl flowTest;

  late AppState mockAppState;
  late MockActivatedPOSLimitFlowCallback mockCallback;
  late MockBuildContext mockContext;
  late ActivatedPOSLimitState mockActivatedPOSLimitState;
  late CommonNavigator commonNavigator;

  setUpAll(() {
    registerFallbackValue(MockBuildContext());
    mockContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockContext);

    mockCallback = MockActivatedPOSLimitFlowCallback();

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    commonNavigator = getIt.get<CommonNavigator>();

    getIt.registerSingleton<AppState>(MockAppState());
    mockAppState = getIt.get<AppState>();

    mockActivatedPOSLimitState = ActivatedPOSLimitState();
  });

  setUp(() {
    flowTest = ActivatedPOSLimitFlowImpl();

    when(() => mockAppState.activatedPOSLimitState).thenReturn(mockActivatedPOSLimitState);

    when(() => commonNavigator.popUntilNamed(
          any(),
          any(),
        )).thenAnswer((_) => Future<void>.value());

    when(() => commonNavigator.pop(
          any(),
        )).thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    reset(mockAppState);
  });

  test('prepareActivatePOSLimitFlow should save info to AppState', () {
    final Screen screenName = Screen.splashScreen;
    final int orderAmount = 500;
    flowTest.prepareActivatePOSLimitFlow(
      screenName: screenName,
      callback: mockCallback,
      orderAmount: orderAmount,
    );

    expect(mockActivatedPOSLimitState.entryPoint, screenName);
    expect(mockActivatedPOSLimitState.callback, mockCallback);
    expect(mockActivatedPOSLimitState.orderAmount, orderAmount);
  });

  test('onSuccess should clear state and call onSuccess callback', () {
    final ActivatedPOSLimitFlowPayload payload = TestActivatedPOSLimitFlowPayload();
    mockActivatedPOSLimitState.callback = mockCallback;
    mockActivatedPOSLimitState.entryPoint = Screen.splashScreen;

    flowTest.onSuccess(context: mockContext, payload: payload);

    verify(() => mockCallback.onSuccess!(mockContext, payload)).called(1);
    verify(() => mockContext.popUntilNamed(Screen.splashScreen.name)).called(1);
    expect(mockActivatedPOSLimitState.entryPoint, null);
    expect(mockActivatedPOSLimitState.callback, null);
  });

  test('onFailed should clear state and call onFailed callback', () {
    final ActivatedPOSLimitFlowFailedReason reason =
        ActivatedPOSLimitFlowFailedReason.userCancelled;
    final String userMessage = 'User cancelled';
    mockActivatedPOSLimitState.callback = mockCallback;
    mockActivatedPOSLimitState.entryPoint = Screen.splashScreen;

    flowTest.onFailed(context: mockContext, reason: reason, userMessage: userMessage);

    verify(() => mockCallback.onFailed!(mockContext, reason, userMessage)).called(1);
    verify(() => mockContext.popUntilNamed(Screen.splashScreen.name)).called(1);
    expect(mockActivatedPOSLimitState.entryPoint, null);
    expect(mockActivatedPOSLimitState.callback, null);
    expect(mockActivatedPOSLimitState.orderAmount, null);
  });

  test('getActivatedPOSLimitState should return activatedPOSLimitState from AppState', () {
    flowTest.getActivatedPOSLimitState();

    verify(() => mockAppState.activatedPOSLimitState).called(1);
  });

  group('verify popToEntryPoint()', () {
    test('should pop to entry point screen if entryPointScreen is not null', () {
      final Screen entryPointScreen = Screen.splashScreen;
      mockActivatedPOSLimitState.entryPoint = entryPointScreen;

      flowTest.popToEntryPoint(entryPointScreen);

      verify(() => mockContext.popUntilNamed(entryPointScreen.name)).called(1);
    });

    test('should pop if entryPointScreen is null', () {
      mockActivatedPOSLimitState.entryPoint = null;

      flowTest.popToEntryPoint(null);

      verify(() => mockContext.pop()).called(1);
    });
  });
}
