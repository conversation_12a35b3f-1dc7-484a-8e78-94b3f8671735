import 'package:evoapp/data/response/card_activate_entity.dart';
import 'package:evoapp/data/response/setup_pos_limit_entity.dart';
import 'package:evoapp/feature/activated_pos_limit/activate_pos/cubit/activate_pos_limit_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify ActivatePOSInitial', () {
    final ActivatePOSInitial state = ActivatePOSInitial();
    expect(state, isA<ActivatePOSState>());
  });
  test('verify ActivatePOSLoading', () {
    final ActivatePOSLoading state = ActivatePOSLoading();
    expect(state, isA<ActivatePOSState>());
  });

  test('verify ActivatePOSError', () {
    final ErrorUIModel errorUIModel = ErrorUIModel();
    final ActivatePOSError state = ActivatePOSError(error: errorUIModel);
    expect(state, isA<ActivatePOSState>());
    expect(state.error, isA<ErrorUIModel>());
  });

  test('verify ActivatePOSSucceed', () {
    final ActivatePOSSucceed state = ActivatePOSSucceed(
      entity: SetPOSLimitEntity(),
    );
    expect(state, isA<ActivatePOSState>());
    expect(state.entity, isA<SetPOSLimitEntity>());
  });

  test('verify ActivateCardError', () {
    final ErrorUIModel errorUIModel = ErrorUIModel();
    final ActivateCardError state = ActivateCardError(error: errorUIModel);
    expect(state, isA<ActivatePOSState>());
    expect(state.error, isA<ErrorUIModel>());
  });

  test('verify ActivateCardSucceed', () {
    final ActivateCardSucceed state = ActivateCardSucceed(
      entity: CardActivateEntity(),
    );
    expect(state, isA<ActivatePOSState>());
    expect(state.entity, isA<CardActivateEntity>());
  });

  test('verify ActivatePOSValidateFailed', () {
    final ActivatePOSValidateFailed state = ActivatePOSValidateFailed();
    expect(state, isA<ActivatePOSState>());
  });

  test('verify ActivatePOSValidateSuccess', () {
    final ActivatePOSValidateSuccess state = ActivatePOSValidateSuccess();
    expect(state, isA<ActivatePOSState>());
  });

  test('verify ActivatePOSRedirectToTPBAppState', () {
    final ErrorUIModel errorUIModel = ErrorUIModel();
    final ActivatePOSRedirectToTPBAppState state =
        ActivatePOSRedirectToTPBAppState(error: errorUIModel);
    expect(state, isA<ActivatePOSState>());
    expect(state.error, isA<ErrorUIModel>());
  });

  test('verify ActivateCardInvalidState', () {
    expect(ActivateCardInvalidState(), isA<ActivatePOSState>());
  });

  test('verify an instance of NeedShowWaitingPopup', () {
    final NeedShowWaitingPopup state = NeedShowWaitingPopup(60);
    expect(state, isA<ActivatePOSState>());
    expect(state.remainingTime, 60);
  });

  test('verify an instance of NeedShowWaitingPopup', () {
    final NoNeedShowWaitingPopup state = NoNeedShowWaitingPopup();
    expect(state, isA<ActivatePOSState>());
  });
}
