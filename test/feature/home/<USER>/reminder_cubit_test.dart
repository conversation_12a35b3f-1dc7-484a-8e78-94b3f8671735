import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/home_repo.dart';
import 'package:evoapp/data/response/reminder_entity.dart';
import 'package:evoapp/feature/home_screen/user/reminder/reminder_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_component/ui_component.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';
import '../../../util/test_util.dart';

class MockHomeRepo extends Mock implements HomeRepo {}

void main() {
  late MockHomeRepo homeRepoMock;
  late ReminderCubit cubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    homeRepoMock = MockHomeRepo();
  });

  setUp(() {
    cubit = ReminderCubit(homeRepo: homeRepoMock);
  });

  tearDown(() {
    reset(homeRepoMock);
  });

  group('test getLatestReminder() function', () {
    blocTest<ReminderCubit, UiComponentState>(
      'test getLatestReminder with Mock is enable and api return success',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('latest_reminder.json');
        when(() => homeRepoMock.getLatestReminder(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return ReminderEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
        });
      },
      build: () => cubit,
      act: (ReminderCubit cubit) async => await cubit.getLatestReminder(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UiComponentLoading>(),
        isA<UiComponentDataLoaded<ReminderEntity>>().having(
            (UiComponentDataLoaded<ReminderEntity> dataLoaded) => dataLoaded.data?.title,
            'test ReminderEntity title with data Mock',
            'Hoàn tất mở thẻ nào bạn ơi!'),
      ],
      verify: (_) {
        verify(() => homeRepoMock.getLatestReminder(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<ReminderCubit, UiComponentState>(
      'test getLatestReminder with Mock is enable and api return fail',
      setUp: () {
        when(() => homeRepoMock.getLatestReminder(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return ReminderEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{
            'status_code': CommonHttpClient.BAD_REQUEST,
          }));
        });
      },
      build: () => cubit,
      act: (ReminderCubit cubit) async => await cubit.getLatestReminder(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UiComponentLoading>(),
        isA<UiComponentFailed>().having(
            (UiComponentFailed dataError) => dataError.errorUIModel.statusCode,
            'test statusCode when fail',
            CommonHttpClient.BAD_REQUEST),
      ],
      verify: (_) {
        verify(() => homeRepoMock.getLatestReminder(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );
  });
}
