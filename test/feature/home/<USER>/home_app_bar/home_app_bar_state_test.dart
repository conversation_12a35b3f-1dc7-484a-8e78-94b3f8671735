import 'package:evoapp/feature/home_screen/home_widgets/home_app_bar/home_app_bar_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('HomeAppBarState', () {
    test('HomeAppBarInitial can be instantiated', () {
      final HomeAppBarInitial state = HomeAppBarInitial();
      expect(state, isA<HomeAppBarInitial>());
      expect(state.props, <Object>[]);
    });

    test('HomeAppBarLoaded can be instantiated with hasHeroBanner', () {
      final HomeAppBarLoaded state = HomeAppBarLoaded(true);
      expect(state, isA<HomeAppBarLoaded>());
      expect(state.hasHeroBanner, true);
      expect(state.props, <Object>[]);
    });

    test('HomeAppBarLoading can be instantiated with isLoading', () {
      final HomeAppBarLoading state = HomeAppBarLoading(true);
      expect(state, isA<HomeAppBarLoading>());
      expect(state.isLoading, true);
      expect(state.props, <Object>[]);
    });
  });
}
