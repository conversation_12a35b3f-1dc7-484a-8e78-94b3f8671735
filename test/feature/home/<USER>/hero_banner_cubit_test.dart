import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/home_repo.dart';
import 'package:evoapp/data/response/hero_banner_entity.dart';
import 'package:evoapp/feature/home_screen/home_widgets/hero_banner/hero_banner_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_component/ui_component.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/test_util.dart';

class MockHomeRepo extends Mock implements HomeRepo {}

void main() {
  late MockHomeRepo mockHomeRepo;
  late HeroBannerCubit cubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockHomeRepo = MockHomeRepo();
  });

  group('test getHeroBanner() function', () {
    setUp(() {
      cubit = HeroBannerCubit(homeRepo: mockHomeRepo);
    });

    blocTest<HeroBannerCubit, UiComponentState>(
      'test getHeroBanner is success',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('hero_banner.json');

        when(() => mockHomeRepo.getHeroBanner(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return HeroBannerEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
        });
      },
      build: () => cubit,
      act: (HeroBannerCubit cubit) => cubit.getHeroBanner(),
      expect: () => <dynamic>[
        isA<UiComponentDataLoaded<HeroBannerEntity>>().having(
            (UiComponentDataLoaded<HeroBannerEntity> dataLoaded) => dataLoaded.data?.imageUrl,
            'test HeroBannerEntity imageUrl',
            'https://thumbs.dreamstime.com/b/landscape-grass-field-green-environment-public-park-use-as-natural-background-backdrop-78426893.jpg'),
      ],
    );

    blocTest<HeroBannerCubit, UiComponentState>(
      'test getHeroBanner is fail',
      setUp: () {
        when(() => mockHomeRepo.getHeroBanner(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return HeroBannerEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{
            'status_code': CommonHttpClient.BAD_REQUEST,
          }));
        });
      },
      build: () => cubit,
      act: (HeroBannerCubit cubit) => cubit.getHeroBanner(),
      expect: () => <dynamic>[
        isA<UiComponentFailed>().having(
            (UiComponentFailed dataError) => dataError.errorUIModel.statusCode,
            'test statusCode when fail',
            CommonHttpClient.BAD_REQUEST),
      ],
    );
  });
}
