import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/submit_link_card_entity.dart';
import 'package:evoapp/feature/manual_link_card/model/manual_link_card_shared_data.dart';
import 'package:evoapp/feature/manual_link_card/three_d_polling/cubit/link_card_three_d_polling_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../constant.dart';

class UserRepoMock extends Mock implements UserRepo {}

void main() {
  late UserRepo userRepoMock;
  late AppState appState;
  late LinkCardThreeDPollingCubit cubit;

  const String linkCardSession = 'linkCardSession';
  const String linkCardRequestId = 'linkCardRequestId';

  group('test submitLinkCard function', () {
    setUp(() {
      getIt.registerLazySingleton<UserRepo>(() => UserRepoMock());
      userRepoMock = getIt.get<UserRepo>();

      getIt.registerLazySingleton<AppState>(() => AppState());
      appState = getIt.get<AppState>();
      final ManualLinkCardSharedData shareData = appState.manualLinkCardSharedData;
      shareData.linkCardSession = linkCardSession;
      shareData.linkCardRequestId = linkCardRequestId;

      getIt.registerLazySingleton<LinkCardThreeDPollingCubit>(() => LinkCardThreeDPollingCubit(
            userRepoMock,
            appState,
          ));
      cubit = getIt.get<LinkCardThreeDPollingCubit>();
      cubit.processLinkCardRequestData();
    });

    tearDown(() {
      getIt.reset();
    });

    blocTest<LinkCardThreeDPollingCubit, LinkCardThreeDPollingState>(
      'test submitLinkCard is success',
      setUp: () {
        when(() => userRepoMock.submitLinkCard(
              linkCardSession: linkCardSession,
              linkCardRequestId: linkCardRequestId,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return SubmitLinkCardEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: null,
            ),
          );
        });
      },
      build: () => cubit,
      act: (LinkCardThreeDPollingCubit cubit) => cubit.submitLinkCard(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<LinkCardThreeDPollingLoading>(),
        isA<LinkCardThreeDPollingSuccess>().having(
          (LinkCardThreeDPollingSuccess state) => state.data,
          'verify data',
          isNotNull,
        ),
      ],
    );

    blocTest<LinkCardThreeDPollingCubit, LinkCardThreeDPollingState>(
      'test submitLinkCard is fail',
      setUp: () {
        when(() => userRepoMock.submitLinkCard(
              linkCardSession: linkCardSession,
              linkCardRequestId: linkCardRequestId,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return SubmitLinkCardEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.UNKNOWN_ERRORS,
              response: <String, dynamic>{'statusCode': CommonHttpClient.UNKNOWN_ERRORS},
            ),
          );
        });
      },
      build: () => cubit,
      act: (LinkCardThreeDPollingCubit cubit) => cubit.submitLinkCard(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<LinkCardThreeDPollingLoading>(),
        isA<LinkCardThreeDPollingError>().having(
          (LinkCardThreeDPollingError state) => state.error.statusCode,
          'verify error code',
          CommonHttpClient.UNKNOWN_ERRORS,
        ),
      ],
    );
  });
}
