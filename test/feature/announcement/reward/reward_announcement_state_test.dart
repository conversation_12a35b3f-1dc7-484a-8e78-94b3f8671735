import 'package:evoapp/data/response/announcement_entity.dart';
import 'package:evoapp/feature/announcement/reward/reward_announcement_state.dart';
import 'package:evoapp/model/type_load_list.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

void main() {
  test('RewardLoadingState can be instantiated', () {
    final RewardLoadingState state = RewardLoadingState();
    expect(state, isA<RewardLoadingState>());
  });

  test('RewardLoadedState can be instantiated with announcements, allowLoadMore, and loadListType',
      () {
    final List<AnnouncementEntity> announcements = <AnnouncementEntity>[];
    final RewardLoadedState state = RewardLoadedState(
      announcements: announcements,
      allowLoadMore: true,
      loadListType: LoadListType.refresh,
    );

    expect(state, isA<RewardLoadedState>());
    expect(state.announcements, announcements);
    expect(state.allowLoadMore, true);
    expect(state.loadListType, LoadListType.refresh);
  });

  test('RewardErrorState can be instantiated with errorUIModel and isRefresh', () {
    final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: 'Error occurred');
    final RewardErrorState state = RewardErrorState(errorUIModel, true);

    expect(state, isA<RewardErrorState>());
    expect(state.errorUIModel, errorUIModel);
    expect(state.isRefresh, true);
  });
}
