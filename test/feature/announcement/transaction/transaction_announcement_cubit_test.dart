import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/announcement_repo.dart';
import 'package:evoapp/data/request/transaction_request.dart';
import 'package:evoapp/data/response/transaction_entity.dart';
import 'package:evoapp/data/response/transaction_list_entity.dart';
import 'package:evoapp/feature/announcement/transaction/transaction_announcement_cubit.dart';
import 'package:evoapp/feature/announcement/transaction/transaction_announcement_state.dart';
import 'package:evoapp/model/type_load_list.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class MockAnnouncementRepo extends Mock implements AnnouncementRepo {}

void main() {
  late TransactionCubit transactionCubit;
  late MockAnnouncementRepo mockAnnouncementRepo;

  setUpAll(() {
    registerFallbackValue(TransactionRequest(limit: null, status: ''));
  });
  setUp(() {
    mockAnnouncementRepo = MockAnnouncementRepo();
    transactionCubit = TransactionCubit(mockAnnouncementRepo);
  });

  tearDown(() {
    transactionCubit.close();
  });

  group('TransactionCubit', () {
    final List<TransactionEntity> transactions = List<TransactionEntity>.generate(
        defaultNumberItemPerPage, (int i) => TransactionEntity(id: i));

    blocTest<TransactionCubit, TransactionState>(
      'emits [TransactionLoadedState] when getTransactions is successful and can load more',
      build: () {
        final TransactionListEntity successTransactionListEntity =
            TransactionListEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: <String, dynamic>{'data': <String, dynamic>{}}))
              ..transactions = transactions;

        when(() =>
                mockAnnouncementRepo.getTransactions(any(), mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => successTransactionListEntity);
        return transactionCubit;
      },
      act: (TransactionCubit cubit) =>
          cubit.loadTransactions(isShowLoading: true, type: LoadListType.refresh),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <TypeMatcher<TransactionState>>[
        isA<TransactionLoadingState>(),
        isA<TransactionLoadedState>()
            .having(
                (TransactionLoadedState state) => state.transactions, 'transactions', transactions)
            .having((TransactionLoadedState state) => state.isLoadMore, 'isLoadMore', true),
      ],
      verify: (_) {
        verify(() =>
                mockAnnouncementRepo.getTransactions(any(), mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<TransactionCubit, TransactionState>(
      'emits [TransactionLoadedState] when getTransactions is successful and cannot load more',
      build: () {
        final TransactionListEntity successTransactionListEntity =
            TransactionListEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: <String, dynamic>{'data': <String, dynamic>{}}));

        when(() =>
                mockAnnouncementRepo.getTransactions(any(), mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => successTransactionListEntity);
        return transactionCubit;
      },
      act: (TransactionCubit cubit) => cubit.loadTransactions(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <TypeMatcher<TransactionLoadedState>>[
        isA<TransactionLoadedState>()
            .having((TransactionLoadedState state) => state.transactions, 'transactions', isEmpty)
            .having((TransactionLoadedState state) => state.isLoadMore, 'isLoadMore', false),
      ],
      verify: (_) {
        verify(() =>
                mockAnnouncementRepo.getTransactions(any(), mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<TransactionCubit, TransactionState>(
      'emits [TransactionErrorState] when getTransactions fails',
      build: () {
        final TransactionListEntity failedTransactionListEntity =
            TransactionListEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.BAD_REQUEST,
                response: <String, dynamic>{'data': <String, dynamic>{}}));
        when(() =>
                mockAnnouncementRepo.getTransactions(any(), mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => failedTransactionListEntity);
        return transactionCubit;
      },
      act: (TransactionCubit cubit) => cubit.loadTransactions(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<TransactionErrorState>().having(
            (TransactionErrorState state) => state.errorUIModel.statusCode,
            'statusCode',
            CommonHttpClient.BAD_REQUEST),
      ],
      verify: (_) {
        verify(() =>
                mockAnnouncementRepo.getTransactions(any(), mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );
  });
}
