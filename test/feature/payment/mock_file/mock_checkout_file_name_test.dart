import 'package:evoapp/feature/payment/mock_file/mock_checkout_file_name.dart';
import 'package:evoapp/model/transaction_status_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Mock file name functions', () {
    test('createOrderMockFileName returns correct file name', () {
      expect(createOrderMockFileName(), 'create_order.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.success), 'create_order.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.orderExpired),
          'create_order_with_order_expired.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.emiUnqualified),
          'create_order_with_emi_unqualified.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.openActiveCardScreen),
          'create_order_with_open_active_card_screen.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.setPosLimitScreen),
          'create_order_with_set_pos_limit_screen.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.openActiveCardGuidanceScreen),
          'create_order_with_open_active_card_guidance_screen.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.setPosLimitGuidanceScreen),
          'create_order_with_set_pos_limit_guidance_screen.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.cardUnqualifiedToSetPosLimit),
          'create_order_with_card_unqualified_to_set_pos_limit_screen.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.emiPackageForDynamicQR),
          'create_order_with_emi_package_for_dynamic_qr.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.noEmiPackageForDynamicQR),
          'create_order_with_no_emi_package_for_dynamic_qr.json');
      expect(createOrderMockFileName(verdict: CreateOrderMockVerdict.serverError),
          'create_order_with_server_error.json');
    });

    test('returns correct file name for each ConfirmAndPayMockVerdict', () {
      expect(getConfirmAndPayMockFileName(), 'confirm_and_pay_order.json');
      expect(getConfirmAndPayMockFileName(mockVerdict: ConfirmAndPayMockVerdict.limitExceed),
          'confirm_and_pay_limit_exceed.json');
      expect(getConfirmAndPayMockFileName(mockVerdict: ConfirmAndPayMockVerdict.paymentInvalid),
          'confirm_and_pay_payment_invalid.json');
      expect(getConfirmAndPayMockFileName(mockVerdict: ConfirmAndPayMockVerdict.promotionDuplicate),
          'confirm_and_pay_promotion_duplicate.json');
      expect(
          getConfirmAndPayMockFileName(mockVerdict: ConfirmAndPayMockVerdict.promotionExpiredData),
          'confirm_and_pay_promotion_expired_data.json');
      expect(getConfirmAndPayMockFileName(mockVerdict: ConfirmAndPayMockVerdict.promotionInvalid),
          'confirm_and_pay_promotion_invalid.json');
      expect(
          getConfirmAndPayMockFileName(mockVerdict: ConfirmAndPayMockVerdict.promotionUnqualified),
          'confirm_and_pay_promotion_unqualified.json');
      expect(getConfirmAndPayMockFileName(mockVerdict: ConfirmAndPayMockVerdict.sessionNotOpened),
          'confirm_and_pay_session_not_opened.json');
      expect(getConfirmAndPayMockFileName(mockVerdict: ConfirmAndPayMockVerdict.transactionTooSoon),
          'confirm_and_pay_transaction_too_soon.json');
      expect(
          getConfirmAndPayMockFileName(
              mockVerdict: ConfirmAndPayMockVerdict.creditLimitInsufficient),
          'confirm_and_pay_credit_limit_insufficient.json');
    });

    test('getCheckOutDetailMockFileName returns correct file name', () {
      expect(getCheckOutDetailMockFileName(), 'get_checkout_detail.json');
    });

    test('getTransactionDetailMockFileName returns correct file name', () {
      expect(getTransactionDetailMockFileName(status: TransactionStatusModel.success),
          'payment_result_response_success.json');
      expect(getTransactionDetailMockFileName(status: TransactionStatusModel.processing),
          'payment_result_response_processing.json');
      expect(getTransactionDetailMockFileName(status: TransactionStatusModel.failure),
          'payment_result_response_failure.json');
      expect(getTransactionDetailMockFileName(status: TransactionStatusModel.reachMaxRetries),
          'payment_result_response_reach_max_retries.json');
      expect(getTransactionDetailMockFileName(status: TransactionStatusModel.pending),
          'payment_result_response_pending.json');
      expect(
          getTransactionDetailMockFileName(
              status: TransactionStatusModel.success, isPayWithEMI: true),
          'payment_result_with_emi_response_success.json');
      expect(
          getTransactionDetailMockFileName(
              status: TransactionStatusModel.processing, isPayWithEMI: true),
          'payment_result_with_emi_response_processing.json');
      expect(
          getTransactionDetailMockFileName(
              status: TransactionStatusModel.failure, isPayWithEMI: true),
          'payment_result_with_emi_response_failure.json');
      expect(
          getTransactionDetailMockFileName(
              status: TransactionStatusModel.reachMaxRetries, isPayWithEMI: true),
          'payment_result_with_emi_response_reach_max_retries.json');
      expect(
          getTransactionDetailMockFileName(
              status: TransactionStatusModel.pending, isPayWithEMI: true),
          'payment_result_with_emi_response_pending.json');
    });

    test('cancelTransactionMockFileName returns correct file name', () {
      expect(cancelTransactionMockFileName(), 'cancel_transaction.json');
    });

    test('updateOrderMockFileName returns correct file name', () {
      expect(updateOrderMockFileName(), 'update_order.json');
    });

    test('getTransactionsHistoryMockFileName returns correct file name', () {
      expect(getTransactionsHistoryMockFileName(), 'transaction_history_list.json');
    });

    test('getTransactionsHistoryV2MockFileName returns correct file name', () {
      expect(getTransactionsHistoryV2MockFileName(), 'transaction_history_list_v2.json');
    });

    test('getCloneOrderMockFileName returns correct file name', () {
      expect(getCloneOrderMockFileName(), 'clone_order_success.json');
      expect(getCloneOrderMockFileName(status: CloneOrderStatus.invalidVoucher),
          'clone_order_success_with_invalid_voucher.json');
      expect(getCloneOrderMockFileName(status: CloneOrderStatus.fail),
          'clone_order_fail_record_not_found.json');
    });
  });
}
