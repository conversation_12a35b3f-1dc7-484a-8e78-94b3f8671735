import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/request/order_flow_type.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/emi_tenor_offer_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/update_order_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/biometric_pin_confirm/biometric_and_pin_confirmation.dart';
import 'package:evoapp/feature/biometric_pin_confirm/biometric_pin_data.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/logging/event_tracking_shared_data.dart';
import 'package:evoapp/feature/manual_link_card/manual_link_card_cubit.dart';
import 'package:evoapp/feature/payment/base_page_payment/cubit/update_order_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/bloc/confirm_button_cubit/confirm_button_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/bloc/confirm_payment_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/bloc/order_info_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/update_confirm_payment_screen/update_confirm_payment_screen.dart';
import 'package:evoapp/feature/payment/models/payment_entry_point.dart';
import 'package:evoapp/feature/payment/payment_shared_data.dart';
import 'package:evoapp/feature/payment/qrcode_scanner/model/qr_code_type.dart';
import 'package:evoapp/feature/payment/utils/auto_apply_voucher_handler/auto_apply_voucher_handler.dart';
import 'package:evoapp/feature/payment/utils/payment_with_emi_utils.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/flutter_test_config.dart';

// Mock classes
class MockUpdateOrderCubit extends MockCubit<UpdateOrderState> implements UpdateOrderCubit {}

class MockConfirmPaymentCubit extends MockCubit<ConfirmPaymentState>
    implements ConfirmPaymentCubit {}

class MockConfirmButtonCubit extends MockCubit<ConfirmButtonState> implements ConfirmButtonCubit {}

class MockBiometricAndPinConfirmation extends Mock implements BiometricAndPinConfirmation {}

class MockOrderInfoCubit extends MockCubit<OrderInfoState> implements OrderInfoCubit {}

class MockManualLinkCardCubit extends MockCubit<ManualLinkCardState>
    implements ManualLinkCardCubit {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockAppState extends Mock implements AppState {}

class MockPaymentSharedData extends Mock implements PaymentSharedData {}

class MockPaymentWithEMIUtils extends Mock implements PaymentWithEMIUtils {}

// Mock entities
class MockOrderSessionEntity extends Mock implements OrderSessionEntity {}

class MockEmiPackageEntity extends Mock implements EmiPackageEntity {}

class MockEmiTenorOfferEntity extends Mock implements EmiTenorOfferEntity {}

class MockVoucherEntity extends Mock implements VoucherEntity {}

class MockErrorUIModel extends Mock implements ErrorUIModel {}

class MockAutoApplyVoucherHandler extends Mock implements AutoApplyVoucherHandler {}

void main() {
  late MockUpdateOrderCubit mockUpdateOrderCubit;
  late MockConfirmPaymentCubit mockConfirmPaymentCubit;
  late MockConfirmButtonCubit mockConfirmButtonCubit;
  late MockBiometricAndPinConfirmation mockBiometricAndPinConfirmation;
  late MockOrderInfoCubit mockOrderInfoCubit;
  late MockManualLinkCardCubit mockManualLinkCardCubit;
  late MockFeatureToggle mockFeatureToggle;
  late MockAppState mockAppState;
  late MockPaymentSharedData mockPaymentSharedData;
  late Widget updateConfirmPaymentScreen;
  late MockOrderSessionEntity mockOrderSession;
  late MockEmiPackageEntity? mockEmiPackage;
  late MockVoucherEntity mockVoucher;
  late MockEmiTenorOfferEntity mockEmiTenorOffer;
  late MockPaymentWithEMIUtils mockPaymentWithEMIUtils;
  late MockAutoApplyVoucherHandler mockAutoApplyVoucherHandler;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(SnackBarType.error);
    registerFallbackValue(AuthenticateType.biometricToken);
    registerFallbackValue(MockOrderSessionEntity());
    registerFallbackValue(MockEmiPackageEntity());
    registerFallbackValue(MockVoucherEntity());
    registerFallbackValue(MockErrorUIModel());
    registerFallbackValue(OrderFlowType.newOrder);
    registerFallbackValue(EvoDialogId.emiUnqualifiedAfterApplyPromotionBottomSheet);

    // Initialize mocks
    mockUpdateOrderCubit = MockUpdateOrderCubit();
    mockConfirmPaymentCubit = MockConfirmPaymentCubit();
    mockConfirmButtonCubit = MockConfirmButtonCubit();
    mockBiometricAndPinConfirmation = MockBiometricAndPinConfirmation();
    mockOrderInfoCubit = MockOrderInfoCubit();
    mockManualLinkCardCubit = MockManualLinkCardCubit();
    mockFeatureToggle = MockFeatureToggle();
    mockAppState = MockAppState();
    mockPaymentSharedData = MockPaymentSharedData();
    mockPaymentWithEMIUtils = MockPaymentWithEMIUtils();
    mockAutoApplyVoucherHandler = MockAutoApplyVoucherHandler();

    initConfigEvoPageStateBase();

    setUtilsMockInstanceForTesting();
    setUpMockConfigEvoPageStateBase();
    setupMockImageProvider();
    setUpMockSnackBarForTest();

    if (getIt.isRegistered<AppState>()) {
      getIt.unregister<AppState>();
    }

    // Now register the mocks
    getIt.registerFactory<UpdateOrderCubit>(() => mockUpdateOrderCubit);
    getIt.registerFactory<ConfirmPaymentCubit>(() => mockConfirmPaymentCubit);
    getIt.registerFactory<ConfirmButtonCubit>(() => mockConfirmButtonCubit);
    getIt.registerFactory<BiometricAndPinConfirmation>(() => mockBiometricAndPinConfirmation);
    getIt.registerFactory<OrderInfoCubit>(() => mockOrderInfoCubit);
    getIt.registerFactory<ManualLinkCardCubit>(() => mockManualLinkCardCubit);
    getIt.registerFactory<PaymentWithEMIUtils>(() => mockPaymentWithEMIUtils);
    getIt.registerSingleton<FeatureToggle>(mockFeatureToggle);
    getIt.registerSingleton<AppState>(mockAppState);
    getIt.registerSingleton<AutoApplyVoucherHandler>(mockAutoApplyVoucherHandler);
    // Setup dialog helper mock
    setupMockDialogHelper();
  });

  setUp(() {
    setUpMockSnackBarForTest();

    // Setup mock entities
    mockOrderSession = MockOrderSessionEntity();
    mockEmiPackage = MockEmiPackageEntity();
    mockVoucher = MockVoucherEntity();
    mockEmiTenorOffer = MockEmiTenorOfferEntity();

    // Setup default behaviors
    when(() => mockAppState.paymentSharedData).thenReturn(mockPaymentSharedData);
    when(() => mockEmiPackage?.offer).thenReturn(mockEmiTenorOffer);
    when(() => mockOrderSession.productCode).thenReturn('');
    when(() => mockUpdateOrderCubit.state).thenReturn(UpdateOrderInitial());
    when(() => mockConfirmPaymentCubit.state).thenReturn(ConfirmPaymentInitial());
    when(() => mockConfirmButtonCubit.state).thenReturn(EnableState());
    when(() => mockOrderInfoCubit.state).thenReturn(OrderInfoUpdateInitial());
    when(() => mockManualLinkCardCubit.state).thenReturn(ManualLinkCardInitState());

    when(() => mockUpdateOrderCubit.checkUpdateOrderIfNeeded(
          any(),
          any(),
          any(),
        )).thenReturn(true);
    when(() => mockUpdateOrderCubit.updateOrderPackage(
          orderSession: any(named: 'orderSession'),
          selectedEmiPackage: any(named: 'selectedEmiPackage'),
          selectedVoucher: any(named: 'selectedVoucher'),
          flowType: any(named: 'flowType'),
        )).thenAnswer((_) => Future<void>.value());

    // Setup EvoUiUtils mock
    when(() => EvoUiUtils().showHudLoading(loadingText: any(named: 'loadingText')))
        .thenAnswer((_) => Future<void>.value());
    when(() => EvoUiUtils().hideHudLoading()).thenAnswer((_) => Future<void>.value());
    when(() => mockFeatureToggle.enableRevampUiFeature).thenAnswer((_) => true);
    when(() => mockAppState.eventTrackingSharedData).thenReturn(EventTrackingSharedData());

    // Setup test widget
    updateConfirmPaymentScreen = MaterialApp(
      home: UpdateConfirmPaymentScreen(
        orderSession: mockOrderSession,
        emiPackage: mockEmiPackage,
        selectedVoucher: mockVoucher,
      ),
    );
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  group('UpdateConfirmPaymentScreen navigation tests', () {
    final String screenName = Screen.updateConfirmPaymentScreen.name;

    test('pushNamed pushes the correct route', () {
      UpdateConfirmPaymentScreen.pushNamed(
        orderSession: mockOrderSession,
        emiPackage: mockEmiPackage,
        selectedVoucher: mockVoucher,
      );

      verify(() => mockNavigatorContext.pushNamed(
            screenName,
            extra: any(named: 'extra'),
          )).called(1);
    });

    test('pushReplacementNamed replaces with the correct route', () {
      UpdateConfirmPaymentScreen.pushReplacementNamed(
        orderSession: mockOrderSession,
        emiPackage: mockEmiPackage,
        selectedVoucher: mockVoucher,
      );

      verify(() => mockNavigatorContext.pushReplacementNamed(
            screenName,
            extra: any(named: 'extra'),
          )).called(1);
    });

    test('removeUntilAndPushReplacementNamed removes until and replaces with the correct route',
        () {
      UpdateConfirmPaymentScreen.removeUntilAndPushReplacementNamed(
        orderSession: mockOrderSession,
        emiPackage: mockEmiPackage,
        selectedVoucher: mockVoucher,
      );

      verify(() => mockNavigatorContext.removeUntilAndPushReplacementNamed(
            screenName,
            any(),
            extra: any(named: 'extra'),
          )).called(1);
    });
  });

  group('UpdateConfirmPaymentState initialization tests', () {
    testWidgets('should initialize correctly and call updateOrderPackage',
        (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);
      await tester.pump(); // Wait for post frame callback

      verify(() => mockUpdateOrderCubit.updateOrderPackage(
            orderSession: mockOrderSession,
            selectedEmiPackage: mockEmiPackage,
            selectedVoucher: mockVoucher,
          )).called(1);
    });

    testWidgets('should not call updateOrderPackage if checkUpdateOrderIfNeeded returns false',
        (WidgetTester tester) async {
      // Setup mock to return false
      when(() => mockUpdateOrderCubit.checkUpdateOrderIfNeeded(
            any(),
            any(),
            any(),
          )).thenReturn(false);

      await tester.pumpWidget(updateConfirmPaymentScreen);
      await tester.pump(); // Wait for post frame callback

      // Verify updateOrderPackage is not called
      verifyNever(() => mockUpdateOrderCubit.updateOrderPackage(
            orderSession: any(named: 'orderSession'),
            selectedEmiPackage: any(named: 'selectedEmiPackage'),
            selectedVoucher: any(named: 'selectedVoucher'),
            flowType: any(named: 'flowType'),
          ));

      // Verify updateOrder is called instead

      verify(() => mockOrderInfoCubit.updateOrderInfo(
            order: any(named: 'order'),
            emiPackage: any(named: 'emiPackage'),
            selectedVoucher: any(named: 'selectedVoucher'),
          )).called(1);
    });
  });

  group('UpdateConfirmPaymentState handleUpdateOrderInfoState tests', () {
    testWidgets('should show loading for UpdateOrderLoading state', (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      state.handleUpdateOrderInfoState(
          tester.element(find.byType(UpdateConfirmPaymentScreen)), UpdateOrderLoading());

      verify(() => EvoUiUtils().showHudLoading()).called(1);
    });

    testWidgets('should handle error for UpdateOrderError state', (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      final MockErrorUIModel mockError = MockErrorUIModel();
      state.handleUpdateOrderInfoState(
          tester.element(find.byType(UpdateConfirmPaymentScreen)), UpdateOrderError(mockError));

      await tester.pump();
      await tester.pump(const Duration(seconds: 2));

      expect(state.mounted, isTrue);
    });

    testWidgets('should update order for UpdateOrderSuccess state', (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      state.handleUpdateOrderInfoState(
        tester.element(find.byType(UpdateConfirmPaymentScreen)),
        UpdateOrderSuccess(
          orderSession: mockOrderSession,
          emiPackage: mockEmiPackage,
          selectedVoucher: mockVoucher,
        ),
      );

      verify(() => EvoUiUtils().hideHudLoading()).called(greaterThanOrEqualTo(1));

      verify(() => mockOrderInfoCubit.updateOrderInfo(
            order: mockOrderSession,
            emiPackage: mockEmiPackage,
            selectedVoucher: mockVoucher,
          )).called(greaterThanOrEqualTo(1));
    });

    testWidgets('should update order with invalid voucher for UpdateOrderInvalidVoucher state',
        (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);

      await tester.pump(Duration(milliseconds: 100));
      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      final MockErrorUIModel mockError = MockErrorUIModel();
      state.handleUpdateOrderInfoState(
        tester.element(find.byType(UpdateConfirmPaymentScreen)),
        UpdateOrderInvalidVoucher(
          orderSession: mockOrderSession,
          emiPackage: mockEmiPackage,
          error: mockError,
        ),
      );

      verify(() => EvoUiUtils().hideHudLoading()).called(greaterThanOrEqualTo(1));

      verify(() => mockOrderInfoCubit.updateOrderInfoWithInvalidVoucher(
            order: any(named: 'order'),
            emiPackage: any(named: 'emiPackage'),
            selectedVoucher: any(named: 'selectedVoucher'),
            errorUIModel: any(named: 'errorUIModel'),
          )).called(greaterThanOrEqualTo(1));
    });

    testWidgets('should show expired order bottom sheet for UpdateOrderExpired state',
        (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      state.handleUpdateOrderInfoState(
        tester.element(find.byType(UpdateConfirmPaymentScreen)),
        UpdateOrderExpired(error: MockErrorUIModel()),
      );

      expect(state.mounted, isTrue);
    });
  });

  group('UpdateConfirmPaymentState onPromotionSelectionError tests', () {
    testWidgets('should show EMI unqualified popup for EMI unqualified verdict',
        (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      final MockErrorUIModel mockError = MockErrorUIModel();
      when(() => mockError.verdict).thenReturn(UpdateOrderEntity.verdictEmiUnqualified);
      when(() => mockError.userMessage).thenReturn('Test error message');

      state.showedEmiUnqualifiedPopup = false;

      state.onPromotionSelectionError(
        mockError,
        orderSession: mockOrderSession,
        voucher: mockVoucher,
      );

      expect(state.mounted, isTrue);
    });

    testWidgets('should call super for other error verdicts', (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      final MockErrorUIModel mockError = MockErrorUIModel();
      when(() => mockError.verdict).thenReturn('other_verdict');

      state.onPromotionSelectionError(mockError);

      expect(state.mounted, isTrue);
    });
  });

  group('UpdateConfirmPaymentState handleChangeToOutRightPurchase tests', () {
    testWidgets('should navigate to order creation screen and push new screen',
        (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      state.handleChangeToOutRightPurchase(
        sharedOrderSession: mockOrderSession,
        selectedVoucher: mockVoucher,
      );

      verify(() => mockNavigatorContext.pop()).called(greaterThanOrEqualTo(1));
      verify(() => mockNavigatorContext.pushNamed(
            Screen.updateConfirmPaymentScreen.name,
            extra: any(named: 'extra'),
          )).called(greaterThanOrEqualTo(1));
    });
  });

  group('UpdateConfirmPaymentState backToHome tests', () {
    testWidgets('should clear payment data and navigate to main screen',
        (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      state.backToHome();

      verify(() => mockPaymentSharedData.clearAll()).called(1);

      verify(() => mockNavigatorContext.removeUntilAndPushReplacementNamed(
            Screen.mainScreen.name,
            any(),
            extra: any(named: 'extra'),
          )).called(1);
    });
  });

  group('UpdateConfirmPaymentState getTenorInfo tests', () {
    testWidgets('should return tenor information from EMI package', (WidgetTester tester) async {
      final List<String> tenorInfo = <String>['Tenor info 1', 'Tenor info 2'];
      when(() => mockEmiTenorOffer.information).thenReturn(tenorInfo);

      await tester.pumpWidget(updateConfirmPaymentScreen);

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      final List<String>? result = state.getTenorInfo();

      expect(result, equals(tenorInfo));
    });

    testWidgets('should return null when EMI package is null', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: UpdateConfirmPaymentScreen(
          orderSession: mockOrderSession,
          selectedVoucher: mockVoucher,
        ),
      ));

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      final List<String>? result = state.getTenorInfo();

      expect(result, isNull);
    });
  });

  group('UpdateConfirmPaymentState showEmiUnqualifiedPopupIfNeeded tests', () {
    testWidgets('should show EMI unqualified popup when conditions are met',
        (WidgetTester tester) async {
      when(() => mockAppState.paymentSharedData)
          .thenReturn(PaymentSharedData()..paymentEntryPoint = PaymentEntryPoint.paymentWithEMI);
      setupMockDialogHelper();

      await tester.pumpWidget(MaterialApp(
        home: UpdateConfirmPaymentScreen(
          orderSession: OrderSessionEntity(
            productCode: QrProductCode.pa03String,
          ),
          selectedVoucher: mockVoucher,
        ),
      ));

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;
      state.showedEmiUnqualifiedPopup = false;

      state.showEmiUnqualifiedPopupIfNeeded();

      expect(state.showedEmiUnqualifiedPopup, isTrue);

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.emiUnqualifiedAfterApplyPromotionBottomSheet,
            title: EvoStrings.emiNotSupportTitle,
            content: any(named: 'content'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            textNegative: any(named: 'textNegative'),
            textPositive: any(named: 'textPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            isDismissible: any(named: 'isDismissible'),
          )).called(1);
    });

    testWidgets('should not show popup if conditions are not met', (WidgetTester tester) async {
      await tester.pumpWidget(updateConfirmPaymentScreen);

      final UpdateConfirmPaymentState state =
          tester.state(find.byType(UpdateConfirmPaymentScreen)) as UpdateConfirmPaymentState;

      when(() => mockPaymentWithEMIUtils.isUserWantPayWithEmi()).thenReturn(false);

      state.showedEmiUnqualifiedPopup = false;

      state.showEmiUnqualifiedPopupIfNeeded();

      expect(state.showedEmiUnqualifiedPopup, isFalse);
      verifyNever(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.emiUnqualifiedAfterApplyPromotionBottomSheet,
            title: EvoStrings.emiNotSupportTitle,
            content: any(named: 'content'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            textNegative: any(named: 'textNegative'),
            textPositive: any(named: 'textPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            isDismissible: any(named: 'isDismissible'),
          ));
    });
  });
}
