import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/payment/confirm_payment/model/order_info_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeSessionId = 'fakeSessionId';
  const double fakeConversionFee = 1.0;
  const int fakeVoucherId = 1;

  group('OrderInfoUIModel', () {
    test('should create an OrderInfoUIModel with default voucherSelectionState', () {
      final OrderSessionEntity orderSession = OrderSessionEntity(
        id: fakeSessionId,
      );

      final EmiPackageEntity emiPackage = EmiPackageEntity(
        conversionFee: fakeConversionFee,
      );

      final VoucherEntity selectedVoucher = VoucherEntity(
        id: fakeVoucherId,
      );

      final OrderInfoUIModel orderInfoUIModel = OrderInfoUIModel(
        orderSession: orderSession,
        emiPackage: emiPackage,
        selectedVoucher: selectedVoucher,
      );

      expect(orderInfoUIModel.orderSession?.id, fakeSessionId);
      expect(orderInfoUIModel.emiPackage?.conversionFee, fakeConversionFee);
      expect(orderInfoUIModel.selectedVoucher?.id, fakeVoucherId);
      expect(orderInfoUIModel.voucherSelectionState, VoucherSelectionState.noSelect);
    });

    test('should create an OrderInfoUIModel with custom voucherSelectionState', () {
      final OrderSessionEntity orderSession = OrderSessionEntity(
        id: fakeSessionId,
      );

      final EmiPackageEntity emiPackage = EmiPackageEntity(
        conversionFee: fakeConversionFee,
      );

      final VoucherEntity selectedVoucher = VoucherEntity(
        id: fakeVoucherId,
      );

      const VoucherSelectionState voucherSelectionState = VoucherSelectionState.invalidVoucher;

      final OrderInfoUIModel orderInfoUIModel = OrderInfoUIModel(
        orderSession: orderSession,
        emiPackage: emiPackage,
        selectedVoucher: selectedVoucher,
        voucherSelectionState: voucherSelectionState,
      );

      expect(orderInfoUIModel.orderSession?.id, fakeSessionId);
      expect(orderInfoUIModel.emiPackage?.conversionFee, fakeConversionFee);
      expect(orderInfoUIModel.selectedVoucher?.id, fakeVoucherId);
      expect(orderInfoUIModel.voucherSelectionState, voucherSelectionState);
    });
  });
}
