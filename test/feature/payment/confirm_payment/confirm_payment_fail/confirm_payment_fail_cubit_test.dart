import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/emi_info_entity.dart';
import 'package:evoapp/data/response/emi_offer_entity.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/emi_tenor_offer_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/payment_result_transaction_entity.dart';
import 'package:evoapp/data/response/transaction_error_entity.dart';
import 'package:evoapp/feature/payment/confirm_payment/confirm_payment_fail_screen/confirm_payment_fail_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/confirm_payment_fail_screen/confirm_payment_fail_state.dart';
import 'package:evoapp/model/transaction_status_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeEmiOfferId = 'fakeEmiOfferId';
  const String fakeEmiTenorId = 'fakeEmiTenorId';
  const String fakeUserMessage = 'fakeUserMessage';
  const int fakeAmount = 1000;

  final EmiOfferEntity emiOfferEntity = EmiOfferEntity(
    id: fakeEmiOfferId,
  );

  final EmiTenorOfferEntity emiTenorOfferEntity = EmiTenorOfferEntity(
    id: fakeEmiTenorId,
  );

  final OrderSessionEntity orderSessionEntity = OrderSessionEntity(
    userChargeAmount: fakeAmount,
    emiOffer: emiOfferEntity,
  );

  final EmiPackageEntity emiPackageEntity = EmiPackageEntity(
    offer: emiTenorOfferEntity,
  );

  final PaymentResultTransactionEntity entityWithEmiExpect = PaymentResultTransactionEntity(
      status: TransactionStatusModel.failure.value,
      userChargeAmount: fakeAmount,
      emiInfo: EmiInfoEntity(
        emiPackage: emiPackageEntity,
      ),
      paymentService: PaymentService.emi.value,
      lastError: TransactionErrorEntity(
        reason: fakeUserMessage,
      ));

  final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: fakeUserMessage);
  late ConfirmPaymentFailCubit confirmPaymentFailCubit;
  late AppState appState;

  setUpAll(() {
    getIt.registerLazySingleton<AppState>(() => AppState());
    appState = getIt.get<AppState>();
  });

  setUp(() {
    confirmPaymentFailCubit = ConfirmPaymentFailCubit(appState);

    ///reset paymentSharedData before each test
    appState.paymentSharedData.clearAll();
  });

  test('verify init state is [ConfirmPaymentFailState]', () {
    expect(confirmPaymentFailCubit.state, isA<ConfirmPaymentFailState>());
  });

  group('test initData() function', () {
    test('verify initData() return correctly value', () {
      ///init data to test
      appState.paymentSharedData.orderSession = orderSessionEntity;
      appState.paymentSharedData.selectedEmiPackage = emiPackageEntity;

      final PaymentResultTransactionEntity entity =
          confirmPaymentFailCubit.initData(errorUIModel: errorUIModel);

      ///verify data clear correctly
      expect(appState.paymentSharedData.selectedEmiPackage, isNull);
      expect(appState.paymentSharedData.orderSession, isNull);

      ///verify return value correctly
      expect(entity.toJson(), entityWithEmiExpect.toJson());
    });
  });

  group('test createPaymentResultTransactionEntity() function', () {
    test('test with EMI createPaymentResultTransactionEntity() called return value correctly', () {
      appState.paymentSharedData.orderSession = orderSessionEntity;
      appState.paymentSharedData.selectedEmiPackage = emiPackageEntity;

      final PaymentResultTransactionEntity entity =
          confirmPaymentFailCubit.createPaymentResultTransactionEntity(errorUIModel: errorUIModel);

      expect(entity.toJson(), entityWithEmiExpect.toJson());
    });

    test(
        'test with outright purchase createPaymentResultTransactionEntity() called return value correctly',
        () {
      final PaymentResultTransactionEntity entityWithOutrightPurchaseExpect =
          PaymentResultTransactionEntity(
              status: TransactionStatusModel.failure.value,
              userChargeAmount: fakeAmount,
              paymentService: PaymentService.outrightPurchase.value,
              lastError: TransactionErrorEntity(
                reason: fakeUserMessage,
              ));

      appState.paymentSharedData.orderSession = orderSessionEntity;

      final PaymentResultTransactionEntity entity =
          confirmPaymentFailCubit.createPaymentResultTransactionEntity(errorUIModel: errorUIModel);

      expect(entity.toJson(), entityWithOutrightPurchaseExpect.toJson());
    });
  });
}
