import 'package:evoapp/feature/payment/confirm_payment/confirm_payment_fail_screen/confirm_payment_fail_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ConfirmPaymentFailState', () {
    test('Initial state is ConfirmPaymentFailInitialState', () {
      final ConfirmPaymentFailState state = ConfirmPaymentFailInitialState();
      expect(state, isA<ConfirmPaymentFailState>());
      expect(state, isA<ConfirmPaymentFailInitialState>());
    });
  });
}
