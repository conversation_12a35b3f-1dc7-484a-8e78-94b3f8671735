import 'package:evoapp/feature/payment/confirm_payment/model/order_info_ui_model.dart';
import 'package:evoapp/feature/payment/widget/promotion_selected/revamp_promotion_selected/revamp_promotion_selected_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

void main() {
  late CommonImageProvider commonImageProvider;

  setUpAll(() {
    getIt.registerLazySingleton(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    commonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('test widget', () {
    const String expectTitle = 'Promotion Title';

    testWidgets('should render the correct title and images with default colors',
        (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: RevampPromotionSelectedWidget(
            voucherSelectionState: VoucherSelectionState.noSelect,
            title: expectTitle,
            iconPath: EvoImages.icMWGPaymentNoPromotion,
          ),
        ),
      ));

      expect(find.text(expectTitle), findsOneWidget);

      verify(() => commonImageProvider.asset(
            EvoImages.icMWGPaymentNoPromotion,
          )).called(1);

      expect(find.text(EvoStrings.mwgPromotionSuffixUnSelectedTitle), findsOneWidget);
    });

    testWidgets('should render the correct title and images with custom colors',
        (WidgetTester tester) async {
      const String discountText = 'Discount Text';

      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: RevampPromotionSelectedWidget(
            voucherSelectionState: VoucherSelectionState.validVoucher,
            title: expectTitle,
            iconPath: EvoImages.icMWGPaymentNoPromotion,
            borderColor: Colors.red,
            backgroundColor: Colors.blue,
            discountText: discountText,
            discountColor: Colors.green,
          ),
        ),
      ));

      expect(find.text(expectTitle), findsOneWidget);

      verify(() => commonImageProvider.asset(
            EvoImages.icMWGPaymentNoPromotion,
          )).called(1);

      expect(find.text(EvoStrings.mwgPromotionSuffixUnSelectedTitle), findsNothing);

      final Finder containerFinder = find.byType(Container).first;
      final Container container = tester.widget<Container>(containerFinder);
      expect(container.decoration, isA<BoxDecoration>());
      final BoxDecoration boxDecoration = container.decoration as BoxDecoration;
      expect(boxDecoration.border, isA<Border>());
      expect(boxDecoration.border!.top.color, Colors.red);
      expect(boxDecoration.color, Colors.blue);

      expect(find.text(discountText), findsOneWidget);
      final Text discountTextWidget = tester.widget<Text>(find.text(discountText));
      expect(discountTextWidget.style?.color, Colors.green);

      expect(find.text(EvoStrings.mwgPromotionSuffixSelectedTitle), findsOneWidget);
    });
  });
}
