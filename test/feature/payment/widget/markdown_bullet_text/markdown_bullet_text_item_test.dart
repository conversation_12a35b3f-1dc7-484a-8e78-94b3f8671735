import 'package:evoapp/feature/payment/widget/markdown_bullet_text/markdown_bullet_text_item.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String testText = 'This is a test bullet item';

  late ShapeDecoration expectedContainerDecoration;
  late ShapeDecoration expectedDotDecoration;

  setUpAll(() {
    getIt.registerLazySingleton(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());

    // Initialize expected decorations
    expectedContainerDecoration = ShapeDecoration(
      shape: RoundedRectangleBorder(
        side: BorderSide(color: evoColors.disableTextFieldBorder),
        borderRadius: BorderRadius.circular(16),
      ),
    );

    expectedDotDecoration = ShapeDecoration(
      color: evoColors.emiTenorBackground,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  });

  testWidgets('MarkDownBulletTextItem renders correctly', (WidgetTester tester) async {
    // Build the widget
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: MarkDownBulletTextItem(text: testText),
        ),
      ),
    );

    // Verify the container decoration
    final Finder containerFinder = find.byWidgetPredicate((Widget widget) {
      if (widget is Container) {
        return widget.decoration == expectedContainerDecoration;
      }
      return false;
    });
    expect(containerFinder, findsOneWidget);

    // Verify the dot decoration
    final Finder dotFinder = find.byWidgetPredicate((Widget widget) {
      if (widget is Container) {
        return widget.decoration == expectedDotDecoration;
      }
      return false;
    });
    expect(dotFinder, findsOneWidget);

    // Verify the Markdown widget
    final Finder markdownFinder = find.byType(Markdown);
    expect(markdownFinder, findsOneWidget);

    final Markdown markdown = tester.widget(markdownFinder);
    expect(markdown.data, testText);
    expect(markdown.styleSheet?.p,
        evoTextStyles.bodyMedium(evoColors.textPassive).copyWith(height: 1.42));
    expect(markdown.styleSheet?.strong,
        evoTextStyles.h200(color: evoColors.textActive).copyWith(height: 1.42));
    expect(markdown.physics, const NeverScrollableScrollPhysics());
    expect(markdown.padding, EdgeInsets.zero);
    expect(markdown.shrinkWrap, true);
  });
}
