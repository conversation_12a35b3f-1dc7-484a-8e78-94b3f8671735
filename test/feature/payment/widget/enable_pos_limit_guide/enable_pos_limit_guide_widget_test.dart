import 'package:evoapp/feature/payment/widget/enable_pos_limit_guide/enable_pos_limit_guide_widget.dart';
import 'package:evoapp/feature/payment/widget/indexed_indicator_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import '../../../../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  test('Test EnablePosLimitGuideWidgetConfigs', () {
    expect(EnablePosLimitGuideWidgetConfigs.imageHeightPercentage, 188 / 812);
    expect(EnablePosLimitGuideWidgetConfigs.imageRatio, 335 / 188);
    expect(
        EnablePosLimitGuideWidgetConfigs.animatedDurationTime, const Duration(milliseconds: 300));
  });

  group('Test EnablePosLimitGuideWidget', () {
    const List<String> fakeImages = <String>[
      'fake_img_1',
      'fake_img_2',
      'fake_img_3',
      'fake_img_4',
    ];

    setUpAll(() {
      getItRegisterMockCommonUtilFunctionAndImageProvider();
      getItRegisterColor();
      getItRegisterTextStyle();
      registerFallbackValue(MockBuildContext());
      setUtilsMockInstanceForTesting();

      when(() => evoImageProvider.asset(
            any(),
            fit: any(named: 'fit'),
          )).thenReturn(const SizedBox());

      when(() => EvoUiUtils().calculateVerticalSpace(
            context: any(named: 'context'),
            heightPercentage: any(named: 'heightPercentage'),
          )).thenReturn(100);
    });

    tearDownAll(() {
      resetUtilMockToOriginalInstance();
    });

    testWidgets('description', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: EnablePosLimitGuideWidget(
            images: fakeImages,
          ),
        ),
      );

      expect(
        find.byElementPredicate((Element element) {
          final Widget widget = element.widget;
          if (widget is SizedBox) {
            return widget.height == 100;
          }
          return false;
        }),
        findsOneWidget,
      );

      // Verify page view
      final Finder pageViewFinder = find.byType(PageView);
      expect(pageViewFinder, findsOneWidget);
      final PageView pageView = tester.widget(pageViewFinder) as PageView;
      expect(pageView.childrenDelegate.estimatedChildCount, fakeImages.length);

      // Verify item
      final Finder pageViewItemFinder = find.byType(AspectRatio);
      expect(pageViewItemFinder, findsOneWidget);
      final AspectRatio pageViewItem = tester.widget(pageViewItemFinder) as AspectRatio;
      expect(pageViewItem.aspectRatio, EnablePosLimitGuideWidgetConfigs.imageRatio);

      expect(pageView.controller?.page, 0);

      /// Verify IndexedIndicatorWidget
      final Finder indexedIndicatorFinder = find.byType(IndexedIndicatorWidget);
      expect(indexedIndicatorFinder, findsOneWidget);
      final IndexedIndicatorWidget indexedIndicator =
          tester.widget<IndexedIndicatorWidget>(indexedIndicatorFinder);
      expect(indexedIndicator.text, '1/4');
      expect(
        indexedIndicator.textStyle,
        evoTextStyles.bodySmall(color: evoColors.posLimitIndicatorTextColor),
      );
      expect(indexedIndicator.iconEnableColor, evoColors.posLimitIndicatorEnable);
      expect(indexedIndicator.iconDisableColor, evoColors.posLimitIndicatorDisable);

      // Verify next button on indicator
      final Finder nextBtnFinder = find.byElementPredicate((Element element) {
        final Widget widget = element.widget;
        if (widget is Icon) {
          return widget.icon == Icons.arrow_forward_ios;
        }
        return false;
      });
      expect(nextBtnFinder, findsOneWidget);
      await tester.tap(nextBtnFinder);
      await tester.pumpAndSettle();
      expect(pageView.controller?.page, 1);

      // Verify previous button on indicator
      final Finder preBtnFinder = find.byElementPredicate((Element element) {
        final Widget widget = element.widget;
        if (widget is Icon) {
          return widget.icon == Icons.arrow_back_ios;
        }
        return false;
      });
      expect(preBtnFinder, findsOneWidget);
      await tester.tap(preBtnFinder);
      await tester.pumpAndSettle();
      expect(pageView.controller?.page, 0);
    });
  });
}
