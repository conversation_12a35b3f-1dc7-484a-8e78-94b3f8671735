import 'package:evoapp/data/response/maintenance_info_entity.dart';
import 'package:evoapp/feature/maintenance/maintenance_handler_impl.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late MaintenanceHandlerImpl maintenanceHandler;
  final MaintenanceInfoEntity testMaintenanceInfo = MaintenanceInfoEntity(
    start: '2006-01-02T15:04:05Z07:00',
    end: '2006-01-02T15:04:05Z07:00',
  );

  setUp(() {
    maintenanceHandler = MaintenanceHandlerImpl();
  });

  group('MaintenanceHandlerImpl Test', () {
    test('should emit maintenance info through stream when emitMaintenance is called', () {
      // Arrange
      final Stream<MaintenanceInfoEntity> stream = maintenanceHandler.getStreamSubscription();

      // Assert
      expectLater(stream, emits(testMaintenanceInfo));

      // Act
      maintenanceHandler.emitMaintenance(testMaintenanceInfo);
    });

    test('should emit multiple maintenance info events correctly', () {
      // Arrange
      final Stream<MaintenanceInfoEntity> stream = maintenanceHandler.getStreamSubscription();
      final MaintenanceInfoEntity secondInfo = MaintenanceInfoEntity(
        start: '2006-01-02T15:04:05Z07:00',
        end: '2006-01-02T15:04:05Z07:00',
      );

      // Assert
      expectLater(stream, emitsInOrder(<MaintenanceInfoEntity>[testMaintenanceInfo, secondInfo]));

      // Act
      maintenanceHandler.emitMaintenance(testMaintenanceInfo);
      maintenanceHandler.emitMaintenance(secondInfo);
    });

    test('should allow multiple listeners to receive maintenance info', () async {
      // Arrange
      final Stream<MaintenanceInfoEntity> stream = maintenanceHandler.getStreamSubscription();

      // Act & Assert
      // First listener
      expectLater(stream, emits(testMaintenanceInfo));

      // Second listener
      expectLater(stream, emits(testMaintenanceInfo));

      // Emit the event
      maintenanceHandler.emitMaintenance(testMaintenanceInfo);
    });

    test('should close the stream controller when close is called', () {
      // Arrange - verify stream is not closed initially
      expect(maintenanceHandler.maintenanceController.isClosed, isFalse);

      // Act
      maintenanceHandler.close();

      // Assert
      expect(maintenanceHandler.maintenanceController.isClosed, isTrue);
    });

    test('should throw error when trying to emit after closing', () {
      // Arrange
      maintenanceHandler.close();

      // Act & Assert
      expect(
        () => maintenanceHandler.emitMaintenance(testMaintenanceInfo),
        throwsA(isA<StateError>()),
      );
    });
  });

  test('verify default value of isHandled', () {
    expect(maintenanceHandler.isHandled, isFalse);
    expect(maintenanceHandler.isHandledMaintenance(), isFalse);
  });

  test('isHandledMaintenance should return current isHandled value', () {
    maintenanceHandler.isHandled = true;

    expect(maintenanceHandler.isHandledMaintenance(), isTrue);

    maintenanceHandler.isHandled = false;

    expect(maintenanceHandler.isHandledMaintenance(), isFalse);
  });

  test('setHandledMaintenance should update isHandled value', () {
    expect(maintenanceHandler.isHandled, isFalse);

    maintenanceHandler.setHandledMaintenance(true);

    expect(maintenanceHandler.isHandled, isTrue);
    expect(maintenanceHandler.isHandledMaintenance(), isTrue);

    maintenanceHandler.setHandledMaintenance(false);

    expect(maintenanceHandler.isHandled, isFalse);
    expect(maintenanceHandler.isHandledMaintenance(), isFalse);
  });
}
