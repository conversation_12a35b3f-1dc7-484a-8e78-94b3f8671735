import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_callback.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_ui_handler.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_cubit.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/evo_text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';
import '../../dop_native/features/card_activation_status/card_activated_pos_failed/dop_native_card_activated_pos_failed_screen_test.dart';

void main() {
  setUp(() {
    EvoDialogHelper.setInstanceForTesting(MockEvoDialogHelper());
    EvoUiUtils.setInstanceForTesting(MockEvoUiUtils());
    when(() => EvoUiUtils().hideHudLoading()).thenAnswer((_) => Future<void>.value());
    when(() => EvoUiUtils().showHudLoading()).thenAnswer((_) => Future<void>.value());
    when(() => EvoDialogHelper().showDialogConfirm(
          content: any(named: 'content'),
          title: any(named: 'title'),
          dialogId: EvoDialogId.resetPinLimitErrorDialog,
          textPositive: any(named: 'textPositive'),
          textNegative: any(named: 'textNegative'),
          contentTextStyle: any(named: 'contentTextStyle'),
          onClickPositive: any(named: 'onClickPositive'),
        )).thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    EvoUiUtils.resetToOriginalInstance();
    EvoDialogHelper.resetToOriginalInstance();
  });

  group('test ResetPinHandler handleChallengeTypeOtp and showPopUpBlock', () {
    final MockBuildContext mockNavigatorContext = MockBuildContext();
    final ResetPinUiHandler resetPinHandler = ResetPinUiHandler();
    setUpAll(() {
      getItRegisterTextStyle();
      getItRegisterColor();
      getIt.registerLazySingleton<CommonNavigator>(() => MockCommonNavigator());
      setUpMockGlobalKeyProvider(mockNavigatorContext);
      when(() => mockNavigatorContext.pushNamed(any(), extra: any(named: 'extra')))
          .thenAnswer((_) => Future<void>.value());
    });

    tearDownAll(() {
      getIt.reset();
    });

    test('test handleChallengeTypeOtp', () {
      final ResetPinCallback resetPinCallback = ResetPinCallback();
      bool isHandleVerifyOtpSuccessCalled = false;
      resetPinCallback.handleVerifyOtpSuccess = (_) {
        isHandleVerifyOtpSuccessCalled = true;
      };
      resetPinHandler.handleChallengeTypeOtp(resetPinCallback: resetPinCallback);
      verify(() => EvoUiUtils().hideHudLoading()).called(1);
      final VerifyOtpPageArg argument = verify(() => mockNavigatorContext.pushNamed(
          Screen.verifyOtpScreen.name,
          extra: captureAny(named: 'extra'))).captured.single;
      argument.onPopSuccess?.call(VerifyOtpCompleted(BaseEntity()));
      expect(isHandleVerifyOtpSuccessCalled, true);
    });

    test('test showPopUpBlock', () {
      resetPinHandler.showPopUpBlock('content');
      final List<dynamic> capturedParams = verify(() => EvoDialogHelper().showDialogConfirm(
            textPositive: captureAny(named: 'textPositive'),
            content: captureAny(named: 'content'),
            title: captureAny(named: 'title'),
            dialogId: EvoDialogId.resetPinLimitErrorDialog,
            textNegative: any(named: 'textNegative'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: captureAny(named: 'onClickPositive'),
          )).captured;
      expect(capturedParams[0], equals(EvoStrings.close));
      expect(capturedParams[1], equals('content'));
      expect(capturedParams[2], equals(EvoStrings.titleLimitResetPin));

      //verify callback
      final VoidCallback onClickPositiveCallback = capturedParams[3];
      onClickPositiveCallback();
      verify(() => mockNavigatorContext.pop()).called(1);
    });
  });

  group('test ResetPinHandler showPopUpNationalId', () {
    final ResetPinUiHandler resetPinHandler = ResetPinUiHandler();
    final AuthenticationRepo mockAuthRepo = MockAuthenticationRepo();
    final EvoUtilFunction mockEvoUtilFunction = MockEvoUtilFunction();
    final CommonNavigator mockCommonNavigator = MockCommonNavigator();
    setUpAll(() {
      getItRegisterTextStyle();
      getItRegisterColor();
      getItRegisterButtonStyle();
      registerFallbackValue(MockBuildContext());
      getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
      when(() => getIt.get<CommonImageProvider>().asset(
            any(),
            fit: any(named: 'fit'),
            width: any(named: 'width'),
            height: any(named: 'height'),
          )).thenReturn(Container());
      when(() => mockEvoUtilFunction.validateMinMaxLengthNationalId(any())).thenAnswer((_) => true);
      getIt.registerLazySingleton(() => GlobalKeyProvider());
      getIt.registerLazySingleton(() => CommonUtilFunction());
      getIt.registerLazySingleton<AuthenticationRepo>(() => mockAuthRepo);
      getIt.registerLazySingleton<CommonNavigator>(() => mockCommonNavigator);
      when(() => mockCommonNavigator.pop(any())).thenAnswer((_) => Future<void>.value());
      when(() => mockCommonNavigator.pushNamed(any(), any(), extra: any(named: 'extra')))
          .thenAnswer((_) => Future<void>.value());
      getIt.registerLazySingleton<EvoUtilFunction>(() => mockEvoUtilFunction);
    });

    tearDownAll(() {
      getIt.reset();
    });

    Future<void> pumWidgetAndTapSubmit(WidgetTester widgetTester,
        {ResetPinCallback? resetPinCallback}) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ElevatedButton(
                onPressed: () {
                  resetPinHandler.showPopUpNationalId(
                      resetPinCallback, '000000000', 'fake_session_token');
                },
                child: Text('showPopUpNationalId')),
          ),
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        ),
      );
      await widgetTester.tap(find.byWidgetPredicate(
        (Widget widget) => widget is Text && widget.data! == 'showPopUpNationalId',
      ));
      await widgetTester.pumpAndSettle();
      //fill in national id
      await widgetTester.enterText(find.byType(EvoTextFieldWidget), '**********');
      //tap submit button
      await widgetTester.pump(Duration(milliseconds: 100));
      await widgetTester.tap(find.byType(CommonButton));
      await widgetTester.pump(Duration(milliseconds: 100));
    }

    Future<void> clearWidget(WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(Container());
      await widgetTester.pumpAndSettle();
    }

    testWidgets('test showPopUpNationalId onSuccess callback', (WidgetTester widgetTester) async {
      when(() => mockAuthRepo.requestResetPin(ResetPinType.verifyNationalId,
              nationalId: any(named: 'nationalId'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) => Future<ResetPinEntity>.value(ResetPinEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{}))));
      await pumWidgetAndTapSubmit(widgetTester);
      verify(() => EvoUiUtils().hideHudLoading()).called(2);
      verify(() => mockCommonNavigator.pushNamed(any(), Screen.verifyOtpScreen.name,
          extra: any(named: 'extra'))).called(1);
      await clearWidget(widgetTester);
    });

    testWidgets('test showPopUpNationalId onLimit callback', (WidgetTester widgetTester) async {
      when(() => mockAuthRepo.requestResetPin(ResetPinType.verifyNationalId,
              nationalId: any(named: 'nationalId'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) => Future<ResetPinEntity>.value(ResetPinEntity.fromBaseResponse(
              BaseResponse(
                  statusCode: CommonHttpClient.BAD_REQUEST,
                  response: <String, dynamic>{'verdict': ResetPinEntity.verdictLimitExceeded}))));
      await pumWidgetAndTapSubmit(widgetTester);
      verify(() => EvoDialogHelper().showDialogConfirm(
            content: any(named: 'content'),
            title: any(named: 'title'),
            dialogId: EvoDialogId.resetPinLimitErrorDialog,
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
      await clearWidget(widgetTester);
    });

    testWidgets('test showPopUpNationalId onError invalid token callback',
        (WidgetTester widgetTester) async {
      when(() => mockAuthRepo.requestResetPin(ResetPinType.verifyNationalId,
              nationalId: any(named: 'nationalId'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) => Future<ResetPinEntity>.value(ResetPinEntity.fromBaseResponse(
              BaseResponse(
                  statusCode: CommonHttpClient.INVALID_TOKEN,
                  response: <String, dynamic>{'verdict': ResetPinEntity.verdictFailure}))));
      final ResetPinCallback resetPinCallback = ResetPinCallback();
      bool isHandleXSessionExpiredCalled = false;
      resetPinCallback.handleXSessionExpired = () {
        isHandleXSessionExpiredCalled = true;
      };
      await pumWidgetAndTapSubmit(widgetTester, resetPinCallback: resetPinCallback);
      expect(isHandleXSessionExpiredCalled, true);
      await clearWidget(widgetTester);
    });

    testWidgets('test showPopUpNationalId onError other reason callback',
        (WidgetTester widgetTester) async {
      when(() => mockAuthRepo.requestResetPin(ResetPinType.verifyNationalId,
              nationalId: any(named: 'nationalId'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) => Future<ResetPinEntity>.value(ResetPinEntity.fromBaseResponse(
              BaseResponse(
                  statusCode: CommonHttpClient.BAD_REQUEST,
                  response: <String, dynamic>{'verdict': ResetPinEntity.verdictFailure}))));
      final ResetPinCallback resetPinCallback = ResetPinCallback();
      bool isHandleResetPinErrorCalled = false;
      resetPinCallback.handleResetPinError = (_) {
        isHandleResetPinErrorCalled = true;
      };
      await pumWidgetAndTapSubmit(widgetTester, resetPinCallback: resetPinCallback);
      expect(isHandleResetPinErrorCalled, true);
      await clearWidget(widgetTester);
    });
  });
}
