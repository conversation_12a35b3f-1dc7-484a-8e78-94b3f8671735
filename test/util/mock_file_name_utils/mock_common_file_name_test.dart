import 'package:evoapp/data/response/private_policy_entity.dart';
import 'package:evoapp/util/mock_file_name_utils/mock_common_file_name.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('getForceUpdateMockFileName', () {
    test('returns true filename when isForceUpdate is true', () {
      expect(getForceUpdateMockFileName(isForceUpdate: true), 'check_force_update_is_true.json');
    });

    test('returns false filename when isForceUpdate is false', () {
      expect(getForceUpdateMockFileName(), 'check_force_update_is_false.json');
    });

    test('returns false filename when isForceUpdate is not provided', () {
      expect(getForceUpdateMockFileName(), 'check_force_update_is_false.json');
    });
  });

  group('createPrivatePolicyFileName', () {
    test('returns the correct filename', () {
      expect(createPrivatePolicyFileName(), 'create_decree_consent_success.json');
    });
  });

  group('checkPrivatePolicyFileName', () {
    test('returns success filename when verdict is success', () {
      expect(checkPrivatePolicyFileName(PrivacyPolicyEntity.verdictSuccess),
          'check_decree_consent_13_success.json');
    });

    test('returns failure filename when verdict is failure', () {
      expect(checkPrivatePolicyFileName(PrivacyPolicyEntity.verdictFailure),
          'check_decree_consent_13_failure.json');
    });

    test('returns success filename when verdict is unknown', () {
      expect(checkPrivatePolicyFileName('unknown'), 'check_decree_consent_13_success.json');
    });
  });

  group('latestPrivatePolicyFileName', () {
    test('returns the correct filename', () {
      expect(latestPrivatePolicyFileName(), 'latest_decree_consent_13.json');
    });
  });
}
