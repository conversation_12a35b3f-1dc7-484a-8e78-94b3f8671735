import 'package:evoapp/util/device_location/data/device_location_permission_status.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DeviceLocationPermissionStatus', () {
    test('fromPermissionStatus returns denied for PermissionStatus.denied', () {
      final DeviceLocationPermissionStatus result =
          DeviceLocationPermissionStatus.fromPermissionStatus(PermissionStatus.denied);
      expect(result, DeviceLocationPermissionStatus.denied);
    });

    test('fromPermissionStatus returns granted for PermissionStatus.granted', () {
      final DeviceLocationPermissionStatus result =
          DeviceLocationPermissionStatus.fromPermissionStatus(PermissionStatus.granted);
      expect(result, DeviceLocationPermissionStatus.granted);
    });

    test('fromPermissionStatus returns granted for PermissionStatus.limited', () {
      final DeviceLocationPermissionStatus result =
          DeviceLocationPermissionStatus.fromPermissionStatus(PermissionStatus.limited);
      expect(result, DeviceLocationPermissionStatus.granted);
    });

    test('fromPermissionStatus returns deniedForever for PermissionStatus.permanentlyDenied', () {
      final DeviceLocationPermissionStatus result =
          DeviceLocationPermissionStatus.fromPermissionStatus(PermissionStatus.permanentlyDenied);
      expect(result, DeviceLocationPermissionStatus.deniedForever);
    });

    test('fromPermissionStatus returns deniedForever for PermissionStatus.restricted', () {
      final DeviceLocationPermissionStatus result =
          DeviceLocationPermissionStatus.fromPermissionStatus(PermissionStatus.restricted);
      expect(result, DeviceLocationPermissionStatus.deniedForever);
    });

    test('fromPermissionStatus returns unableToDetermine for PermissionStatus.provisional', () {
      final DeviceLocationPermissionStatus result =
          DeviceLocationPermissionStatus.fromPermissionStatus(PermissionStatus.provisional);
      expect(result, DeviceLocationPermissionStatus.unableToDetermine);
    });
  });
}
