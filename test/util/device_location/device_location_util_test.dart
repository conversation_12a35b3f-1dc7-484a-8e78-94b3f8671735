import 'dart:async';

import 'package:evoapp/feature/logging/evo_logging_event.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/device_location/data/device_location_model.dart';
import 'package:evoapp/util/device_location/data/device_location_permission_status.dart';
import 'package:evoapp/util/device_location/device_location_util.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:geolocator/geolocator.dart';
import 'package:mocktail/mocktail.dart';

// ignore: depend_on_referenced_packages
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

// Mock classes
class MockGeolocator extends Mock implements GeolocatorPlatform, MockPlatformInterfaceMixin {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  const String flutterPermission = 'flutter.baseflow.com/permissions/methods';
  const MethodChannel channel = MethodChannel(flutterPermission);
  const int permissionStatusGranted = 1;

  late MockGeolocator mockGeolocator;
  late MockLoggingRepo mockLoggingRepo;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(EvoEventType.location);
  });

  setUp(() {
    mockGeolocator = MockGeolocator();
    mockLoggingRepo = MockLoggingRepo();

    // Inject the mock objects
    GeolocatorPlatform.instance = mockGeolocator;
    getIt.registerSingleton<LoggingRepo>(mockLoggingRepo);

    when(
      () => mockLoggingRepo.logEvent(
        eventType: any(named: 'eventType'),
        data: any(named: 'data'),
      ),
    ).thenAnswer((_) async => Future<void>.value());
  });

  tearDown(() {
    reset(mockGeolocator);
    reset(mockLoggingRepo);
    getIt.unregister<LoggingRepo>();

    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, null);
  });

  test('verify static consts', () async {
    expect(DeviceLocationUtil.stepSucceed, 'succeed');
    expect(DeviceLocationUtil.stepFailed, 'failed');

    expect(DeviceLocationUtil.stepGetCurrentLocation, 'get_current_location');
    expect(DeviceLocationUtil.stepCheckLocationService, 'check_location_service');
    expect(DeviceLocationUtil.stepCheckLocationPermission, 'check_location_permission');
    expect(DeviceLocationUtil.stepRequestLocationPermission, 'request_location_permission');
  });

  group('getCurrentLocation', () {
    test('returns DeviceLocationModel on success', () async {
      final Position position = Position(
        latitude: 37.7749,
        longitude: -122.4194,
        timestamp: DateTime.now(),
        accuracy: 0.0,
        altitude: 0.0,
        heading: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
        altitudeAccuracy: 0.0,
        headingAccuracy: 0.0,
      );

      when(
        () => mockGeolocator.getCurrentPosition(locationSettings: any(named: 'locationSettings')),
      ).thenAnswer((_) async => position);

      final DeviceLocationModel? result = await DeviceLocationUtil().getCurrentLocation();

      expect(result, isNotNull);
      expect(result!.latitude, position.latitude);
      expect(result.longitude, position.longitude);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepGetCurrentLocation,
              'stepStatus': DeviceLocationUtil.stepSucceed,
              'value': 'lat = ${position.latitude}, long = ${position.longitude}',
            },
          )).called(1);
    });

    test('returns null on timeout', () async {
      when(() => mockGeolocator.getCurrentPosition(
            locationSettings: any(named: 'locationSettings'),
          )).thenThrow(TimeoutException('Timeout'));

      final DeviceLocationModel? result = await DeviceLocationUtil().getCurrentLocation();

      expect(result, isNull);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepGetCurrentLocation,
              'stepStatus': DeviceLocationUtil.stepFailed,
              'errorDetail': 'timeout when fetch location, duration = null',
            },
          )).called(1);
    });

    test('retries on timeout', () async {
      when(() => mockGeolocator.getCurrentPosition(
            locationSettings: any(named: 'locationSettings'),
          )).thenThrow(TimeoutException('Timeout'));

      final DeviceLocationModel? result = await DeviceLocationUtil().getCurrentLocation(
        retryTimesWhenTimeout: 2,
        timeoutDuration: Duration(seconds: 1),
      );

      expect(result, isNull);
      verify(() => mockGeolocator.getCurrentPosition(
            locationSettings: any(named: 'locationSettings'),
          )).called(3); // Initial call + 2 retries

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepGetCurrentLocation,
              'stepStatus': DeviceLocationUtil.stepFailed,
              'errorDetail': 'timeout when fetch location, duration = 0:00:01.000000',
            },
          )).called(3);
    });

    test('returns null on other exception', () async {
      final Exception exception = Exception('exception');
      when(() => mockGeolocator.getCurrentPosition(
            locationSettings: any(named: 'locationSettings'),
          )).thenThrow(exception);

      final DeviceLocationModel? result = await DeviceLocationUtil().getCurrentLocation();

      expect(result, isNull);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepGetCurrentLocation,
              'stepStatus': DeviceLocationUtil.stepFailed,
              'errorDetail': exception.toString(),
            },
          )).called(1);
    });
  });

  group('checkIsEnableLocationService', () {
    test('returns true when location service is enabled', () async {
      when(() => mockGeolocator.isLocationServiceEnabled()).thenAnswer((_) async => true);

      final bool result = await DeviceLocationUtil().checkIsEnableLocationService();

      expect(result, isTrue);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepCheckLocationService,
              'stepStatus': DeviceLocationUtil.stepSucceed,
              'value': 'true',
            },
          )).called(1);
    });

    test('returns false when location service is disabled', () async {
      when(() => mockGeolocator.isLocationServiceEnabled()).thenAnswer((_) async => false);

      final bool result = await DeviceLocationUtil().checkIsEnableLocationService();

      expect(result, isFalse);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepCheckLocationService,
              'stepStatus': DeviceLocationUtil.stepSucceed,
              'value': 'false',
            },
          )).called(1);
    });

    test('returns false and logs error on exception', () async {
      final Exception exception = Exception('exception');
      when(() => mockGeolocator.isLocationServiceEnabled()).thenThrow(exception);

      final bool result = await DeviceLocationUtil().checkIsEnableLocationService();

      expect(result, isFalse);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepCheckLocationService,
              'stepStatus': DeviceLocationUtil.stepFailed,
              'errorDetail': exception.toString(),
            },
          )).called(1);
    });
  });

  group('checkLocationPermissionStatus', () {
    test('returns correct status', () async {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          return permissionStatusGranted;
        },
      );

      final DeviceLocationPermissionStatus result =
          await DeviceLocationUtil().checkLocationPermissionStatus();

      expect(result, DeviceLocationPermissionStatus.granted);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepCheckLocationPermission,
              'stepStatus': DeviceLocationUtil.stepSucceed,
              'value': 'granted',
            },
          )).called(1);
    });

    test('returns unableToDetermine on exception', () async {
      final Exception exception = Exception('Exception');

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          throw exception;
        },
      );

      final DeviceLocationPermissionStatus result =
          await DeviceLocationUtil().checkLocationPermissionStatus();

      expect(result, DeviceLocationPermissionStatus.unableToDetermine);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepCheckLocationPermission,
              'stepStatus': DeviceLocationUtil.stepFailed,
              'errorDetail': isA<String>(),
            },
          )).called(1);
    });
  });

  group('requestLocationPermission', () {
    test('returns correct status', () async {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          return permissionStatusGranted;
        },
      );

      final DeviceLocationPermissionStatus result =
          await DeviceLocationUtil().requestLocationPermission();

      expect(result, DeviceLocationPermissionStatus.granted);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepRequestLocationPermission,
              'stepStatus': DeviceLocationUtil.stepSucceed,
              'value': 'granted',
            },
          )).called(1);
    });

    test('returns unableToDetermine on exception', () async {
      final Exception exception = Exception('Exception');

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          throw exception;
        },
      );

      final DeviceLocationPermissionStatus result =
          await DeviceLocationUtil().requestLocationPermission();

      expect(result, DeviceLocationPermissionStatus.unableToDetermine);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepRequestLocationPermission,
              'stepStatus': DeviceLocationUtil.stepFailed,
              'errorDetail': isA<String>(),
            },
          )).called(1);
    });
  });

  group('logEvent', () {
    test('logs event with value', () {
      DeviceLocationUtil().logEvent(
        step: DeviceLocationUtil.stepGetCurrentLocation,
        stepStatus: DeviceLocationUtil.stepSucceed,
        value: 'lat = 37.7749, long = -122.4194',
      );

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepGetCurrentLocation,
              'stepStatus': DeviceLocationUtil.stepSucceed,
              'value': 'lat = 37.7749, long = -122.4194',
            },
          )).called(1);
    });

    test('logs event with errorDetail', () {
      DeviceLocationUtil().logEvent(
        step: DeviceLocationUtil.stepGetCurrentLocation,
        stepStatus: DeviceLocationUtil.stepFailed,
        errorDetail: 'timeout when fetch location, duration = null',
      );

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepGetCurrentLocation,
              'stepStatus': DeviceLocationUtil.stepFailed,
              'errorDetail': 'timeout when fetch location, duration = null',
            },
          )).called(1);
    });

    test('logs event with value and errorDetail', () {
      DeviceLocationUtil().logEvent(
        step: DeviceLocationUtil.stepGetCurrentLocation,
        stepStatus: DeviceLocationUtil.stepFailed,
        value: 'lat = 37.7749, long = -122.4194',
        errorDetail: 'timeout when fetch location, duration = null',
      );

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepGetCurrentLocation,
              'stepStatus': DeviceLocationUtil.stepFailed,
              'value': 'lat = 37.7749, long = -122.4194',
              'errorDetail': 'timeout when fetch location, duration = null',
            },
          )).called(1);
    });

    test('logs event without value and errorDetail', () {
      DeviceLocationUtil().logEvent(
        step: DeviceLocationUtil.stepGetCurrentLocation,
        stepStatus: DeviceLocationUtil.stepSucceed,
      );

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.location,
            data: <String, dynamic>{
              'step': DeviceLocationUtil.stepGetCurrentLocation,
              'stepStatus': DeviceLocationUtil.stepSucceed,
            },
          )).called(1);
    });
  });
}
