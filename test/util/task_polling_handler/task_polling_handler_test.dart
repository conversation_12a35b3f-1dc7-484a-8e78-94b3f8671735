import 'package:evoapp/data/repository/cashback_repo.dart';
import 'package:evoapp/data/response/cashback_result_entity.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/task_polling_handler/cashback_result_polling_task.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:evoapp/util/task_polling_handler/task_polling_handler.dart';
import 'package:evoapp/util/task_polling_handler/polling_task.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';

import '../../feature/transaction_history_screen/dialog/cashback_transaction_dialog_test.dart';

class MockSharedPreferencesHelper extends Mock implements CommonSharedPreferencesHelper {}

class MockPollingTask extends Mock implements PollingTask {}

void main() {
  late TaskPollingHandlerImpl taskPollingHandler;
  late MockSharedPreferencesHelper mockSharedPreferencesHelper;

  setUp(() {
    mockSharedPreferencesHelper = MockSharedPreferencesHelper();
    taskPollingHandler =
        TaskPollingHandlerImpl(sharedPreferencesHelper: mockSharedPreferencesHelper);
    when(() => mockSharedPreferencesHelper.saveData(any(), any<List<String>>()))
        .thenAnswer((_) async => true);
    getIt.registerLazySingleton<CashbackRepo>(() => MockCashbackRepo());
    when(() => getIt<CashbackRepo>().getCashbackResult(
          transactionId: any(named: 'transactionId'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => CashBackResultEntity.fromBaseResponse(
            BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
          'data': <String, dynamic>{
            'id': '0000',
            'status': 'pending',
          }
        })));
  });

  tearDown(() {
    getIt.reset();
  });

  group('TaskPollingHandlerImpl', () {
    test('startPollingAllTasksInStorage should start all saved tasks', () async {
      when(() => mockSharedPreferencesHelper
              .readData<List<dynamic>?>(TaskPollingHandler.pollingTasksStorageKey))
          .thenAnswer((_) async => <String>[
                'task1_cashBackResult_1633024800000',
                'task2_cashBackResult_1633028400000',
              ]);

      await taskPollingHandler.startPollingAllTasksInStorage();

      verify(() => mockSharedPreferencesHelper
          .readData<List<dynamic>?>(TaskPollingHandler.pollingTasksStorageKey)).called(1);
      expect(taskPollingHandler.tasks.length, 2);
      taskPollingHandler.stopPollingAllTasks();
    });

    test('startPollingTask should start a new task', () {
      final DateTime createdTime = DateTime.now();

      final PollingTask? task = taskPollingHandler.createPollingTask(
        taskId: 'task1',
        type: PollingTaskType.cashBackResult,
        createdTime: createdTime,
      );

      expect(taskPollingHandler.tasks.containsKey('task1'), isTrue);
      expect(task, isNotNull);
      expect(task, isA<CashBackResultPollingTask>());
    });

    test('saveNewTask should save a new task to storage', () async {
      final DateTime createdTime = DateTime.now();
      when(() => mockSharedPreferencesHelper.readData<List<dynamic>?>(any()))
          .thenAnswer((_) async => <String>[]);
      when(() => mockSharedPreferencesHelper.saveData(any(), any<List<String>>()))
          .thenAnswer((_) async => true);

      await taskPollingHandler.saveNewTask('task1', PollingTaskType.cashBackResult, createdTime);

      verify(() => mockSharedPreferencesHelper.saveData(
          TaskPollingHandler.pollingTasksStorageKey, any<List<String>>())).called(1);
    });

    test('pausePolling should stop all tasks', () {
      final MockPollingTask mockTask = MockPollingTask();
      taskPollingHandler.tasks['task1'] = mockTask;

      taskPollingHandler.pausePollingAllTasks();

      verify(() => mockTask.stop()).called(1);
    });

    test('resumePolling should start all tasks', () {
      final MockPollingTask mockTask = MockPollingTask();
      taskPollingHandler.tasks['task1'] = mockTask;

      taskPollingHandler.startPollingAllTasks();

      verify(() => mockTask.start()).called(1);
    });

    test('stopPolling should clear all tasks', () {
      final MockPollingTask mockTask = MockPollingTask();
      taskPollingHandler.tasks['task1'] = mockTask;

      taskPollingHandler.stopPollingAllTasks();

      verify(() => mockTask.stop()).called(1);
      expect(taskPollingHandler.tasks.isEmpty, isTrue);
    });

    test('deleteTask should remove task from storage', () async {
      final MockPollingTask mockTask = MockPollingTask();
      when(() => mockTask.storageValue).thenReturn('task1_cashBackResult_1633024800000');
      when(() => mockTask.id).thenReturn('task1');
      when(() => mockSharedPreferencesHelper.readData<List<dynamic>?>(any()))
          .thenAnswer((_) async => <String>['task1_cashBackResult_1633024800000']);

      await taskPollingHandler.deleteTask(mockTask);

      verify(() => mockSharedPreferencesHelper.saveData(
          TaskPollingHandler.pollingTasksStorageKey, any<List<String>>())).called(1);
      expect(taskPollingHandler.tasks.containsKey('task1'), isFalse);
    });
  });
}
