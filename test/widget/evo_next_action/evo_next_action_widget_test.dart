import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/model/evo_action_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_action_handler.dart';
import 'package:evoapp/widget/evo_next_action/evo_next_action_button.dart';
import 'package:evoapp/widget/evo_next_action/evo_next_action_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockFunction extends Mock {
  void call();
}

class MockCampaignRepo extends Mock implements CampaignRepo {}

void main() {
  final MockFunction mockFunction = MockFunction();
  void mockPreActionBeforeNextAction() {
    mockFunction.call();
  }

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
    getIt.registerLazySingleton<CampaignRepo>(() => MockCampaignRepo());
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());

    setUtilsMockInstanceForTesting();

    setUpOneLinkDeepLinkRegExForTest();

    FlavorConfig(
      flavor: FlavorType.stag.name,
      values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
    );
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
    getIt.reset();
  });

  group('Test param preActionBeforeNextAction', () {
    testWidgets(
        'Give nextAction type is promotion_scan_to_pay, isUsed == false, should call preActionBeforeNextAction before handle nextAction',
        (WidgetTester tester) async {
      final EvoActionModel evoActionModel = EvoActionModel(type: 'promotion_scan_to_pay');

      await tester.runAsync(() async {
        await tester.pumpWidget(
          MaterialApp(
            home: EvoNextActionWidget(
              nextAction: evoActionModel,
              preActionBeforeNextAction: mockPreActionBeforeNextAction,
            ),
          ),
        );

        when(() => EvoActionHandler().handle(
              evoActionModel,
              arg: any(named: 'arg'),
            )).thenAnswer((_) async {
          return true;
        });

        final Finder finder = find.byType(EvoNextActionButton);
        expect(finder, findsOneWidget);

        await tester.tap(finder);
        await tester.pumpAndSettle();
        verifyInOrder(<VoidCallback>[
          () => mockPreActionBeforeNextAction(),
          () => EvoActionHandler().handle(
                evoActionModel,
                arg: any(named: 'arg'),
              ),
        ]);
      });
    });

    testWidgets(
        'Give nextAction type is not promotion_scan_to_pay, isUsed == false, should NOT call preActionBeforeNextAction before handle nextAction',
        (WidgetTester tester) async {
      final EvoActionModel mockEvoActionModel = EvoActionModel(type: 'fake_type');

      await tester.runAsync(() async {
        await tester.pumpWidget(
          MaterialApp(
            home: EvoNextActionWidget(
              nextAction: mockEvoActionModel,
              preActionBeforeNextAction: mockPreActionBeforeNextAction,
            ),
          ),
        );

        when(() => EvoActionHandler().handle(
              mockEvoActionModel,
              arg: any(named: 'arg'),
            )).thenAnswer((_) async {
          return true;
        });

        final Finder finder = find.byType(EvoNextActionButton);
        expect(finder, findsOneWidget);

        await tester.tap(finder);
        await tester.pumpAndSettle();
        verifyNever(() => mockPreActionBeforeNextAction());
        verify(() => EvoActionHandler().handle(
              mockEvoActionModel,
              arg: any(named: 'arg'),
            )).called(1);
      });
    });
  });
}
