import 'package:evoapp/widget/cta_with_powered_by_widget.dart';
import 'package:evoapp/widget/powered_by_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

class MockOnPressed extends Mock {
  void call();
}

void main() {
  const String fakeCTAText = 'Sample Text';
  final MockOnPressed onPressed = MockOnPressed();

  late CommonImageProvider commonImageProvider;

  void checkCTA(WidgetTester tester) {
    final Finder commonButtonFinder = find.byType(CommonButton);
    expect(commonButtonFinder, findsOneWidget);
    final Element ctaElement = tester.element(commonButtonFinder);
    final Padding? paddingCTAWidget = ctaElement.findAncestorWidgetOfExactType<Padding>();
    expect(paddingCTAWidget, isNotNull);
    expect(paddingCTAWidget?.padding, const EdgeInsets.symmetric(vertical: 8));
    final CommonButton commonButtonWidget = tester.widget(commonButtonFinder);
    expect(commonButtonWidget.isWrapContent, false);
  }

  void checkPoweredByWidget(WidgetTester tester) {
    final Finder poweredByFinder = find.byType(PoweredByWidget);
    expect(poweredByFinder, findsOneWidget);
    final Element poweredByElement = tester.element(poweredByFinder);
    final Padding? paddingPoweredByWidget =
        poweredByElement.findAncestorWidgetOfExactType<Padding>();
    expect(paddingPoweredByWidget, isNotNull);
    expect(paddingPoweredByWidget?.padding, const EdgeInsets.symmetric(vertical: 4));
  }

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    commonImageProvider = getIt.get<CommonImageProvider>();

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  tearDownAll(() {
    getIt.reset();
  });

  testWidgets('test CTAWithPoweredByWidget with default values', (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: CTAWithPoweredByWidget(
          text: fakeCTAText,
          onPressed: onPressed.call,
        ),
      ),
    ));

    // CTA Button
    final Finder ctaFinder = find.text(fakeCTAText);
    expect(ctaFinder, findsOneWidget);
    checkCTA(tester);

    // PoweredByWidget
    checkPoweredByWidget(tester);

    // verify onPressed
    await tester.tap(ctaFinder);
    verify(() => onPressed.call()).called(1);
  });

  testWidgets('test CTAWithPoweredByWidget with enable = false', (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: CTAWithPoweredByWidget(
          text: fakeCTAText,
          onPressed: onPressed.call,
          enable: false,
        ),
      ),
    ));

    // CTA Button
    final Finder ctaFinder = find.text(fakeCTAText);
    expect(ctaFinder, findsOneWidget);
    checkCTA(tester);

    // PoweredByWidget
    checkPoweredByWidget(tester);

    // verify onPressed
    await tester.tap(ctaFinder);
    verifyNever(() => onPressed.call());
  });
}
