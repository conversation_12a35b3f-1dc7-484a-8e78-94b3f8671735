import 'package:evoapp/widget/pdf_view/cubit/pdf_widget_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String url = 'sample_url';

  group('PdfWidgetState', () {
    test('PdfWidgetInitial should be a subclass of PdfWidgetState', () {
      expect(PdfWidgetInitial(), isA<PdfWidgetState>());
    });

    test('PdfWidgetLoading should be a subclass of PdfWidgetState', () {
      final PdfWidgetLoading state = PdfWidgetLoading(url);

      expect(state, isA<PdfWidgetState>());
      expect(state.url, url);
    });

    test('PdfWidgetError should be a subclass of PdfWidgetState', () {
      const String userMessage = 'Sample error message';
      final ErrorUIModel error = ErrorUIModel(userMessage: userMessage);
      final PdfWidgetError state = PdfWidgetError(error);

      expect(state, isA<PdfWidgetState>());
      expect(state.error.userMessage, userMessage);
    });
  });
}
