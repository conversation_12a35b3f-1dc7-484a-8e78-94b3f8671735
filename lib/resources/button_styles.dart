import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';

import 'resources.dart';

class EvoButtonStyles extends CommonButtonStyles {
  @override
  ButtonStyle tertiary(
    ButtonSize size, {
    bool isHasShadow = true,
    MaterialTapTargetSize tapTargetSize = MaterialTapTargetSize.padded,
  }) {
    final WidgetStateProperty<RoundedRectangleBorder> shape = WidgetStateProperty.resolveWith(
      (Set<WidgetState> states) {
        return RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            buttonDimensions.getCornerRadius(size),
          ),
          side: BorderSide(
            color: states.contains(WidgetState.disabled)
                ? evoColors.tertiaryButtonForegroundDisable
                : evoColors.tertiaryButtonForeground,
            width: 1.5,
          ),
        );
      },
    );

    return super
        .tertiary(
          size,
          isHasShadow: isHasShadow,
          tapTargetSize: tapTargetSize,
        )
        .copyWith(shape: shape);
  }
}
