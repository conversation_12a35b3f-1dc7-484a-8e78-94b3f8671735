import 'package:flutter/widgets.dart';

import '../../../data/response/user_deletion_verification_entity.dart';
import 'model/delete_account_reason_model.dart';
import 'reason_item_widget.dart';

class DeletionReasonGridView extends StatelessWidget {
  /// the ratio of the width to the height of each child in the grid view of reasons
  static const double childAspectRatioItem = 163.5 / 84;

  static const SliverGridDelegateWithFixedCrossAxisCount defaultGridDelegate =
      SliverGridDelegateWithFixedCrossAxisCount(
    crossAxisCount: 2,
    crossAxisSpacing: 8,
    mainAxisSpacing: 8,
    childAspectRatio: childAspectRatioItem,
  );

  final List<DeleteAccountReasonModel> deletionReasons;
  final void Function(ReasonEntity reason) onReasonTap;

  const DeletionReasonGridView(
      {required this.deletionReasons, required this.onReasonTap, super.key});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      gridDelegate: defaultGridDelegate,
      itemBuilder: (BuildContext context, int index) {
        return ReasonItemWidget(
          reasonModel: deletionReasons[index],
          onTap: (ReasonEntity reason) => onReasonTap.call(reason),
        );
      },
      itemCount: deletionReasons.length,
    );
  }
}
