import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';

import '../../../../resources/resources.dart';
import '../../../webview/models/evo_webview_arg.dart';

class AttentionNotesContent extends StatelessWidget {
  const AttentionNotesContent({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const SizedBox(height: 12),
          Text(
            EvoStrings.attentionNotesTitle,
            style: evoTextStyles.h600(evoColors.textActive),
          ),
          _buildAttentionNoteText(EvoStrings.attentionNoteContents),
          _buildAttentionNoteTextWithDot(EvoStrings.attentionNoteAfterDeleteUnlinkEvoCard),
          _buildAttentionNoteTextWithDot(EvoStrings.attentionNoteAfterDeleteCantUseEvoApp),
          _buildAttentionNoteText(EvoStrings.attentionNoteCarefullyConsider),
          InkWell(
            onTap: () {
              openFAQScreen(context);
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Text(
                EvoStrings.attentionNotesLearnMore,
                style: evoTextStyles.h200(color: evoColors.primary),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void openFAQScreen(BuildContext context) {
    final EvoWebViewArg arg = EvoWebViewArg(
      url: WebsiteUrl.evoFaqUrl,
      title: EvoStrings.frequentlyQuestions,
    );
    CommonWebView.pushReplacementNamed(arg: arg);
  }

  Widget _buildAttentionNoteText(String note) {
    return Text(
      note,
      style: evoTextStyles.bodyLarge(evoColors.textPassive),
    );
  }

  Widget _buildAttentionNoteTextWithDot(String note) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6),
          child: Text(
            '\u2022',
            style: evoTextStyles.bodyLarge(evoColors.textPassive),
          ),
        ),
        Expanded(
          child: _buildAttentionNoteText(note),
        ),
      ],
    );
  }
}
