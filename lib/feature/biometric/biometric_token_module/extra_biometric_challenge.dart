import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

abstract class BiometricChallengeCallback {
  void onBioChallengeSuccess(String? biometricToken);

  void onBioChallengeError(ErrorUIModel error);

  void onBioChallengeCancel();
}

abstract class BiometricChallengeWidget extends StatefulWidget {
  final BiometricChallengeCallback? callback;

  const BiometricChallengeWidget({super.key, this.callback});
}

abstract class BiometricChallengeState<T extends BiometricChallengeWidget> extends State<T> {}
