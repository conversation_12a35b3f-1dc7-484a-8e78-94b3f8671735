import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/evo_appbar.dart';

class ManualLinkCardResultWidget extends StatelessWidget {
  final String title, description, imageUrl, buttonTitle;
  final VoidCallback? onButtonTap;
  final VoidCallback? onCloseButtonPressed;
  final double imageHeight;

  const ManualLinkCardResultWidget({
    required this.description,
    required this.imageUrl,
    required this.title,
    required this.buttonTitle,
    required this.onButtonTap,
    super.key,
    this.onCloseButtonPressed,
    this.imageHeight = 0.23,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: evoColors.background,
      appBar: EvoAppBar(
        leading: CloseButton(color: evoColors.icon, onPressed: onCloseButtonPressed),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: <Widget>[
                const SizedBox(height: 32),
                _buildStatusImage(context),
                const SizedBox(height: 24),
                _buildStatusTitle(),
                const SizedBox(height: 8),
                _buildStatusDescription(),
                const SizedBox(height: 24),
                _buildButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusImage(BuildContext context) {
    final double calculatedHeight = EvoUiUtils().calculateVerticalSpace(
      heightPercentage: imageHeight,
      context: context,
    );
    return evoImageProvider.asset(
      imageUrl,
      height: calculatedHeight,
      fit: BoxFit.fill,
    );
  }

  Widget _buildButton() => CommonButton(
        onPressed: () => onButtonTap?.call(),
        isWrapContent: false,
        style: evoButtonStyles.primary(ButtonSize.xLarge),
        child: Text(buttonTitle),
      );

  Widget _buildStatusTitle() => Text(
        title,
        style: evoTextStyles.h300(),
        textAlign: TextAlign.center,
      );

  Widget _buildStatusDescription() => Text(
        description,
        style: evoTextStyles.bodyMedium(evoColors.textPassive).copyWith(height: 1.4),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        textAlign: TextAlign.center,
      );
}
