import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/functions.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../model/evo_action_model.dart';
import '../../../resources/resources.dart';
import '../../../util/mapper.dart';
import '../../feature_flow/feature_flow_failed_reason.dart';
import '../manual_link_card_mixin.dart';
import '../model/linked_card_status_model.dart';
import '../widgets/manual_link_card_result_widget.dart';

class DOPCardStatusScreenArgs extends PageBaseArg {
  final LinkedCardStatusModel linkedCardStatusModel;

  DOPCardStatusScreenArgs({required this.linkedCardStatusModel});
}

class DOPCardStatusScreen extends PageBase {
  final LinkedCardStatusModel linkedCardStatusModel;

  static void pushNamed({
    required LinkedCardStatusModel linkedCardStatusModel,
  }) {
    return navigatorContext?.pushNamed(
      Screen.dopCardStatusScreen.name,
      extra: DOPCardStatusScreenArgs(
        linkedCardStatusModel: linkedCardStatusModel,
      ),
    );
  }

  static void pushReplacementNamed({
    required LinkedCardStatusModel linkedCardStatusModel,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopCardStatusScreen.name,
      extra: DOPCardStatusScreenArgs(
        linkedCardStatusModel: linkedCardStatusModel,
      ),
    );
  }

  const DOPCardStatusScreen({
    required this.linkedCardStatusModel,
    super.key,
  });

  @override
  State<DOPCardStatusScreen> createState() => _DOPCardStatusScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.dopCardStatusScreen.routeName);
}

class _DOPCardStatusScreenState extends EvoPageStateBase<DOPCardStatusScreen>
    with ManualLinkCardMixin {
  final double defaultDopImageHeight = 0.23;

  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) {
        if (didPop) {
          return;
        }

        _handleUserBack();
      },
      child: ManualLinkCardResultWidget(
        description: _buildStatusDescription(widget.linkedCardStatusModel),
        imageUrl: _buildStatusImage(widget.linkedCardStatusModel),
        title: _buildStatusTitle(widget.linkedCardStatusModel),
        buttonTitle: _buildButton(widget.linkedCardStatusModel),
        onButtonTap: () => _handleAction(widget.linkedCardStatusModel),
        onCloseButtonPressed: () {
          _handleUserBack();
        },
      ),
    );
  }

  String _buildStatusImage(LinkedCardStatusModel linkedCardStatusModel) {
    final LinkedCardStatusVerdict verdict = linkedCardStatusModel.getVerdictLinkedCardStatusModel();

    switch (verdict) {
      case LinkedCardStatusVerdict.verdictUnfulfilledCard:
      case LinkedCardStatusVerdict.verdictWaitingForCardIssuing:
      case LinkedCardStatusVerdict.verdictDuplicatedLinkRequest:
      case LinkedCardStatusVerdict.verdictLinkCardLinkRequestIsProcessing:
        return EvoImages.imgDopIncomplete;
      case LinkedCardStatusVerdict.verdictUnqualifiedCard:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidPhoneNumber:
      case LinkedCardStatusVerdict.verdictLinkCardAlreadyLinked:
      case LinkedCardStatusVerdict.verdictUnqualifiedUserInformation:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidParameters:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidBankCode:
      case LinkedCardStatusVerdict.verdictLinkCardNotFoundLinkInfo:
      case LinkedCardStatusVerdict.verdictLinkCardBankProductNotSupported:
      case LinkedCardStatusVerdict.verdictLinkCardFailure:
      case LinkedCardStatusVerdict.verdictFailureAll:
        return EvoImages.imgDopError;
      case LinkedCardStatusVerdict.unknown:
      default:
        return EvoImages.imgDopIncomplete;
    }
  }

  String _buildStatusTitle(LinkedCardStatusModel linkedCardStatusModel) {
    final LinkedCardStatusVerdict verdict = linkedCardStatusModel.getVerdictLinkedCardStatusModel();

    switch (verdict) {
      case LinkedCardStatusVerdict.verdictDuplicatedLinkRequest:
      case LinkedCardStatusVerdict.verdictLinkCardLinkRequestIsProcessing:
        return EvoStrings.dopDuplicatedLinkRequestTitle;
      case LinkedCardStatusVerdict.verdictUnqualifiedUserInformation:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidPhoneNumber:
      case LinkedCardStatusVerdict.verdictLinkCardAlreadyLinked:
      case LinkedCardStatusVerdict.verdictLinkCardUnqualifiedUserInformation:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidParameters:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidBankCode:
      case LinkedCardStatusVerdict.verdictLinkCardNotFoundLinkInfo:
      case LinkedCardStatusVerdict.verdictLinkCardBankProductNotSupported:
      case LinkedCardStatusVerdict.verdictLinkCardFailure:
      case LinkedCardStatusVerdict.verdictFailureAll:
        return EvoStrings.dopUnqualifiedUserInformationTitle;
      case LinkedCardStatusVerdict.verdictUnqualifiedCard:
        return EvoStrings.dopUnqualifiedCardTitle;
      case LinkedCardStatusVerdict.unknown:
      default:
        return EvoStrings.dopCardStatusDefaultTitle;
    }
  }

  String _buildStatusDescription(LinkedCardStatusModel linkedCardStatusModel) {
    final LinkedCardStatusVerdict verdict = linkedCardStatusModel.getVerdictLinkedCardStatusModel();

    switch (verdict) {
      case LinkedCardStatusVerdict.verdictUnfulfilledCard:
        return EvoStrings.dopUnfulfilledCardDescription;
      case LinkedCardStatusVerdict.verdictWaitingForCardIssuing:
        return EvoStrings.dopWaitingForIssuingCardDescription;
      case LinkedCardStatusVerdict.verdictUnqualifiedCard:
        return EvoStrings.dopUnqualifiedCardDescription;
      case LinkedCardStatusVerdict.verdictDuplicatedLinkRequest:
        return EvoStrings.dopDuplicatedLinkRequestDescription;
      case LinkedCardStatusVerdict.verdictUnqualifiedUserInformation:
        return EvoStrings.dopUnqualifiedUserInformationDescription;

      // PREPARE LINK
      case LinkedCardStatusVerdict.verdictLinkCardInvalidPhoneNumber:
      case LinkedCardStatusVerdict.verdictLinkCardAlreadyLinked:
      case LinkedCardStatusVerdict.verdictLinkCardUnqualifiedUserInformation:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidParameters:
      case LinkedCardStatusVerdict.verdictLinkCardLinkRequestIsProcessing:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidBankCode:
      case LinkedCardStatusVerdict.verdictLinkCardNotFoundLinkInfo:
      case LinkedCardStatusVerdict.verdictLinkCardBankProductNotSupported:
      case LinkedCardStatusVerdict.verdictLinkCardFailure:
        return linkedCardStatusModel.userMessage ?? '';
      case LinkedCardStatusVerdict.verdictFailureAll:
        return EvoStrings.submitLinkCardWithPermissionDenied;
      case LinkedCardStatusVerdict.unknown:
      default:
        return EvoStrings.defaultCantLinkedCartStatusContent;
    }
  }

  String _buildButton(LinkedCardStatusModel linkedCardStatusModel) {
    final LinkedCardStatusVerdict verdict = linkedCardStatusModel.getVerdictLinkedCardStatusModel();

    switch (verdict) {
      case LinkedCardStatusVerdict.verdictUnfulfilledCard:
        return EvoStrings.dopContinueManualLinkCard;
      case LinkedCardStatusVerdict.verdictWaitingForCardIssuing:
      case LinkedCardStatusVerdict.verdictUnqualifiedCard:
      case LinkedCardStatusVerdict.verdictUnqualifiedUserInformation:
      case LinkedCardStatusVerdict.verdictDuplicatedLinkRequest:
        return EvoStrings.moveToHome;

      // PREPARE LINK
      case LinkedCardStatusVerdict.verdictLinkCardInvalidPhoneNumber:
      case LinkedCardStatusVerdict.verdictLinkCardAlreadyLinked:
      case LinkedCardStatusVerdict.verdictLinkCardUnqualifiedUserInformation:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidParameters:
      case LinkedCardStatusVerdict.verdictLinkCardLinkRequestIsProcessing:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidBankCode:
      case LinkedCardStatusVerdict.verdictLinkCardNotFoundLinkInfo:
      case LinkedCardStatusVerdict.verdictLinkCardBankProductNotSupported:
      case LinkedCardStatusVerdict.verdictLinkCardFailure:
      case LinkedCardStatusVerdict.verdictFailureAll:
      case LinkedCardStatusVerdict.unknown:
      default:
        return EvoStrings.moveToHome;
    }
  }

  void _handleAction(LinkedCardStatusModel linkedCardStatusModel) {
    final LinkedCardStatusVerdict verdict = linkedCardStatusModel.getVerdictLinkedCardStatusModel();

    switch (verdict) {
      case LinkedCardStatusVerdict.verdictUnfulfilledCard:
        final EvoActionModel? action = linkedCardStatusModel.action?.toEvoActionModel();
        _continueManualLinkCard(action);
        break;
      case LinkedCardStatusVerdict.verdictWaitingForCardIssuing:
      case LinkedCardStatusVerdict.verdictUnqualifiedCard:
      case LinkedCardStatusVerdict.verdictUnqualifiedUserInformation:
      case LinkedCardStatusVerdict.verdictDuplicatedLinkRequest:
      // PREPARE LINK
      case LinkedCardStatusVerdict.verdictLinkCardInvalidPhoneNumber:
      case LinkedCardStatusVerdict.verdictLinkCardAlreadyLinked:
      case LinkedCardStatusVerdict.verdictLinkCardUnqualifiedUserInformation:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidParameters:
      case LinkedCardStatusVerdict.verdictLinkCardLinkRequestIsProcessing:
      case LinkedCardStatusVerdict.verdictLinkCardInvalidBankCode:
      case LinkedCardStatusVerdict.verdictLinkCardNotFoundLinkInfo:
      case LinkedCardStatusVerdict.verdictLinkCardBankProductNotSupported:
      case LinkedCardStatusVerdict.verdictLinkCardFailure:
      case LinkedCardStatusVerdict.verdictFailureAll:
      case LinkedCardStatusVerdict.unknown:
      default:
        moveToHome();
        break;
    }
  }

  ///User can go to DOP and continue the step that the user was doing before.
  ///After completing DOP, user clicks Back button → Redirect user to the entry point (Scan QR/checkout) screen.
  void _continueManualLinkCard(EvoActionModel? action) {
    action?.let((EvoActionModel act) {
      ///because DOP need process in webView (external browser), so we use [commonUtilFunction.handleAction] to handle action for webView
      commonUtilFunction.handleAction(act).then((_) {
        navigatorContext?.maybePop();
      });
    });
  }

  void _handleUserBack() {
    onManualLinkCardFlowFailed(reason: FlowFailedReason.userCancelled);
  }
}
