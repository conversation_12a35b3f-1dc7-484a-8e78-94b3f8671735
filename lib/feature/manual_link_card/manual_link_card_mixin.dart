import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../data/response/linked_card_status_checking_entity.dart';
import '../../model/evo_dialog_id.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/ui_utils/evo_dialog_helper.dart';
import '../ekyc/ekyc_flow_callback.dart';
import '../ekyc/model/ekyc_for_flow_type.dart';
import '../ekyc/model/face_otp_payload.dart';
import '../ekyc_v2/ekyc_v2_flow_callback.dart';
import '../ekyc_v2/ekyc_v2_flow_type.dart';
import '../ekyc_v2/face_auth/models/facial_auth_payload.dart';
import '../feature_flow/base_flow_callback.dart';
import '../feature_flow/feature_flow_failed_reason.dart';
import '../main_screen/main_screen.dart';
import '../payment/payment_shared_data.dart';
import 'dop_status/dop_card_status_screen.dart';
import 'manual_link_card_flow_callback.dart';
import 'model/linked_card_status_model.dart';
import 'model/manual_link_card_shared_data.dart';
import 'pre_face_auth/pre_face_auth_screen.dart';
import 'pre_face_otp/pre_face_otp_screen.dart';
import 'three_d_polling/link_card_three_d_polling_screen.dart';

mixin ManualLinkCardMixin {
  Future<void> showManualLinkCardEncourageOfCheckout(
    BuildContext context, {
    VoidCallback? onLinkCardClick,
  }) async {
    await showManualLinkCardEncourageBottomSheet(
      ctx: context,
      title: EvoStrings.titleManualLinkCardCheckOut,
      content: EvoStrings.descriptionManualLinkCardCheckOut,
      buttonTitle: EvoStrings.manualLinkCardButtonTitleCheckOut,
      onLinkCardClick: () {
        /// dismiss popup
        navigatorContext?.pop();

        onLinkCardClick?.call();
      },
    );
  }

  Future<void> showManualLinkCardEncourageBottomSheet({
    required BuildContext ctx,
    required String title,
    required String content,
    required String buttonTitle,
    void Function()? onLinkCardClick,
    void Function()? onDismiss,
  }) async {
    await EvoDialogHelper().showDialogBottomSheet(
      dialogId: EvoDialogId.manualLinkCardEncourageBottomSheet,
      title: title,
      content: content,
      isShowButtonClose: true,
      header: evoImageProvider.asset(
        EvoImages.imgManualLinkCardCard,
        width: ctx.screenWidth,
        fit: BoxFit.fitWidth,
      ),
      textPositive: buttonTitle,
      onClickPositive: () {
        onLinkCardClick?.call();
      },
    );

    onDismiss?.call();
  }

  Future<void> showManualLinkCardFailedBottomSheet({required BuildContext ctx}) {
    return EvoDialogHelper().showDialogBottomSheet(
      dialogId: EvoDialogId.manualLinkCardFailedBottomSheet,
      isDismissible: false,
      header: evoImageProvider.asset(
        EvoImages.imgCantCheckLinkedCard,
        width: ctx.screenWidth,
        fit: BoxFit.fitWidth,
      ),
      title: EvoStrings.cantCheckLinkedCardTitle,
      content: EvoStrings.cantCheckLinkedCardContent,
      textPositive: EvoStrings.cantCheckLinkedCardButton,
      onClickPositive: () {
        MainScreen.removeUntilAndPushReplacementNamed(isLoggedIn: true);
      },
    );
  }

  Future<void> showDialogConfirmCancelLinkCard({
    required BuildContext context,
    required int timeToWaitingForNextLinkCard,
    VoidCallback? onClickPositive,
    VoidCallback? onClickNegative,
  }) {
    return EvoDialogHelper().showDialogConfirm(
      title: EvoStrings.linkCardCancellationWarningTitle,
      content: EvoStrings.linkCardCancellationWarningContent.replaceVariableByValue(<String>[
        '$timeToWaitingForNextLinkCard',
      ]),
      textNegative: EvoStrings.linkCardKeepCancellation,
      textPositive: EvoStrings.linkCardDiscardCancellation,
      dialogId: EvoDialogId.linkCardPollingCancellationWarningDialog,
      onClickPositive: onClickPositive,
      onClickNegative: onClickNegative,
      isDismissible: false,
    );
  }

  void handleLinkingCardStatus({
    required BuildContext context,
    required LinkedCardStatusCheckingEntity cardStatusEntity,
    bool isReplace = false,
  }) {
    _saveLinkCardRequestData(cardStatusEntity);

    switch (cardStatusEntity.verdict) {
      case LinkedCardStatusCheckingEntity.verdictSuccess:
        final Screen? screenNameEntryPoint =
            getIt.get<AppState>().manualLinkCardSharedData.entryPointScreen;
        _handleChallengeTypeWhenSuccess(
          cardStatusEntity: cardStatusEntity,
          context: context,
          isReplace: isReplace,
          entryPointScreen: screenNameEntryPoint,
        );
        break;
      default:
        final LinkedCardStatusModel linkedCardStatusModel = LinkedCardStatusModel(
          verdict: cardStatusEntity.verdict,
          action: cardStatusEntity.action,
          userMessage: cardStatusEntity.userMessage,
        );

        isReplace
            ? DOPCardStatusScreen.pushReplacementNamed(linkedCardStatusModel: linkedCardStatusModel)
            : DOPCardStatusScreen.pushNamed(linkedCardStatusModel: linkedCardStatusModel);
        break;
    }
  }

  void _handleChallengeTypeWhenSuccess({
    required LinkedCardStatusCheckingEntity cardStatusEntity,
    required BuildContext context,
    required bool isReplace,
    required Screen? entryPointScreen,
  }) {
    final String? challengeType = cardStatusEntity.challengeType;

    if (challengeType == LinkCardStatusChallengeType.none.value) {
      isReplace
          ? LinkCardThreeDPollingScreen.pushReplacementNamed()
          : LinkCardThreeDPollingScreen.pushNamed();
      return;
    }

    if (challengeType == LinkCardStatusChallengeType.faceOtp.value) {
      _moveToFaceOtpStarterScreen(
        entryPointScreen: entryPointScreen,
        isReplace: isReplace,
      );
      return;
    }

    if (challengeType == LinkCardStatusChallengeType.faceAuth.value) {
      _moveToFaceAuthStarterScreen(
        entryPointScreen: entryPointScreen,
        isReplace: isReplace,
        sessionToken: cardStatusEntity.sessionToken,
      );
      return;
    }

    throw Exception('challenge type is not supported');
  }

  void _moveToFaceOtpStarterScreen({
    required Screen? entryPointScreen,
    required bool isReplace,
  }) {
    final AppState appState = getIt.get<AppState>();
    final EkycFlowCallback? faceOtpCallback = appState.manualLinkCardSharedData.faceOtpCallback;
    if (faceOtpCallback == null) {
      /// error occurred in development phase , please init [manualLinkFaceOtpCallback] before call this method
      throw Exception('manualLinkFaceOtpCallback is null. Please check it');
    }

    if (isReplace) {
      PreFaceOtpManualLinkCardScreen.pushReplacementNamed(
        flowType: EkycFlowType.linkCard,
        callback: faceOtpCallback,
      );
    } else {
      PreFaceOtpManualLinkCardScreen.pushNamed(
        flowType: EkycFlowType.linkCard,
        callback: faceOtpCallback,
      );
    }
  }

  void _moveToFaceAuthStarterScreen({
    required Screen? entryPointScreen,
    required bool isReplace,
    String? sessionToken,
  }) {
    final AppState appState = getIt.get<AppState>();
    final EkycV2FlowCallback? faceAuthCallback = appState.manualLinkCardSharedData.faceAuthCallback;
    if (faceAuthCallback == null) {
      /// error occurred in development phase , please init [manualLinkFaceAuthCallback] before call this method
      throw Exception('manualLinkFaceOtpCallback is null. Please check it');
    }

    if (isReplace) {
      PreFaceAuthManualLinkCardScreen.pushReplacementNamed(
        flowType: EkycV2FlowType.linkCard,
        callback: faceAuthCallback,
        sessionToken: sessionToken,
      );
    } else {
      PreFaceAuthManualLinkCardScreen.pushNamed(
        flowType: EkycV2FlowType.linkCard,
        callback: faceAuthCallback,
        sessionToken: sessionToken,
      );
    }
  }

  void _saveLinkCardRequestData(LinkedCardStatusCheckingEntity cardStatusEntity) {
    final AppState appState = getIt.get<AppState>();
    final ManualLinkCardSharedData sharedData = appState.manualLinkCardSharedData;
    sharedData.linkCardRequestId = cardStatusEntity.linkCardRequestId;
    sharedData.linkCardSession = cardStatusEntity.sessionToken;
  }

  /// save entry point screen and manual-link flow callback
  /// [entryPointScreen] is screen name that user come from
  /// [manualLinkFlowCallback] is callback that will be called when manual-link flow is success or failed (include cancel)
  /// [faceOtpCallback] is callback that will be called when manual link face-otp flow is success or failed (include cancel)
  void saveEntryPointScreenAndFlowCallback({
    required Screen entryPointScreen,
    required ManualLinkCardFlowCallback manualLinkFlowCallback,
    EkycFlowCallback? faceOtpCallback,
    EkycV2FlowCallback? faceAuthCallback,
  }) {
    final AppState appState = getIt.get<AppState>();
    final ManualLinkCardSharedData sharedData = appState.manualLinkCardSharedData;
    sharedData.entryPointScreen = entryPointScreen;
    sharedData.flowCallback = manualLinkFlowCallback;
    sharedData.faceOtpCallback = faceOtpCallback;
    sharedData.faceAuthCallback = faceAuthCallback;
  }

  void onManualLinkCardFlowSuccess({ManualLinkCardFlowPayload? payload}) {
    final ManualLinkCardSharedData? manualLinkCardSharedData = _getManualLinkCardSharedData();
    final OnFlowSuccess<ManualLinkCardFlowPayload>? onSuccess =
        manualLinkCardSharedData?.flowCallback?.onSuccess;

    onSuccess?.call(payload);

    clearManualLinkCardDataInAppState();
  }

  void onManualLinkCardFlowFailed({required FlowFailedReason reason, String? userMessage}) {
    final ManualLinkCardSharedData? manualLinkCardSharedData = _getManualLinkCardSharedData();
    final OnFlowFailed? onFailed = manualLinkCardSharedData?.flowCallback?.onFailed;

    onFailed?.call(reason, userMessage);

    clearManualLinkCardDataInAppState();
  }

  ManualLinkCardSharedData? _getManualLinkCardSharedData() {
    final AppState appState = getIt.get<AppState>();
    return appState.manualLinkCardSharedData;
  }

  void clearManualLinkCardDataInAppState() {
    final AppState appState = getIt.get<AppState>();
    appState.manualLinkCardSharedData = null;

    commonLog('clear data: ${appState.manualLinkCardSharedData}');
  }

  @visibleForTesting
  void clearPaymentSharedDataInAppState() {
    final PaymentSharedData paymentSharedData = getPaymentSharedData();
    paymentSharedData.clearAll();
  }

  PaymentSharedData getPaymentSharedData() {
    final AppState appState = getIt.get<AppState>();
    return appState.paymentSharedData;
  }

  void saveLinkCardSession(String? linkCardSession) {
    final AppState appState = getIt.get<AppState>();
    appState.manualLinkCardSharedData.linkCardSession = linkCardSession;
  }

  void faceOtpMoveToLinkCardPollingScreen({
    EkycFlowPayload? payload,
  }) {
    handleFaceOtpPayload(payload);
    LinkCardThreeDPollingScreen.pushReplacementNamed();
  }

  void faceAuthMoveToLinkCardPollingScreen({
    EkycV2FlowPayload? payload,
  }) {
    handleFaceAuthPayload(payload);
    LinkCardThreeDPollingScreen.pushReplacementNamed();
  }

  @visibleForTesting
  void handleFaceOtpPayload(EkycFlowPayload? payload) {
    if (payload != null && payload is FaceOtpPayload) {
      saveLinkCardSession(payload.sessionToken);
    }
  }

  @visibleForTesting
  void handleFaceAuthPayload(EkycV2FlowPayload? payload) {
    if (payload != null && payload is FaceAuthPayload) {
      saveLinkCardSession(payload.sessionToken);
    }
  }

  void moveToHome() {
    clearManualLinkCardDataInAppState();
    clearPaymentSharedDataInAppState();
    MainScreen.removeUntilAndPushReplacementNamed(isLoggedIn: true);
  }
}
