import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/evo_appbar.dart';
import '../../../widget/evo_appbar_leading_button.dart';
import '../../ekyc/ekyc_flow_callback.dart';
import '../../ekyc/face_otp/starter_screen/face_otp_starter_screen.dart';
import '../../ekyc/model/ekyc_for_flow_type.dart';
import '../../feature_flow/feature_flow_failed_reason.dart';
import '../manual_link_card_config.dart';
import '../manual_link_card_mixin.dart';
import '../widgets/count_down_timer_button.dart';

class PreFaceOtpManualLinkCardScreenArg extends PageBaseArg {
  final EkycFlowType flowType;
  final EkycFlowCallback callback;

  PreFaceOtpManualLinkCardScreenArg({
    required this.flowType,
    required this.callback,
  });
}

class PreFaceOtpManualLinkCardScreen extends PageBase {
  static void pushNamed({
    required EkycFlowType flowType,
    required EkycFlowCallback callback,
  }) {
    return navigatorContext?.pushNamed(Screen.preFaceOtpManualLinkCardScreen.name,
        extra: PreFaceOtpManualLinkCardScreenArg(
          flowType: flowType,
          callback: callback,
        ));
  }

  static void pushReplacementNamed({
    required EkycFlowType flowType,
    required EkycFlowCallback callback,
  }) {
    return navigatorContext?.pushReplacementNamed(Screen.preFaceOtpManualLinkCardScreen.name,
        extra: PreFaceOtpManualLinkCardScreenArg(
          flowType: flowType,
          callback: callback,
        ));
  }

  final EkycFlowType flowType;
  final EkycFlowCallback callback;

  const PreFaceOtpManualLinkCardScreen({
    required this.flowType,
    required this.callback,
    super.key,
  });

  @override
  State<PreFaceOtpManualLinkCardScreen> createState() => _PreFaceOtpScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.preFaceOtpManualLinkCardScreen.routeName);
}

class _PreFaceOtpScreenState extends EvoPageStateBase<PreFaceOtpManualLinkCardScreen>
    with ManualLinkCardMixin {
  final double imageHeight = 0.41;

  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) {
          return;
        }

        _handleUserBack();
      },
      child: Scaffold(
        appBar: EvoAppBar(
          backgroundColor: evoColors.background,
          leading: EvoAppBarLeadingButton(onPressed: _handleUserBack),
        ),
        body: SafeArea(
          child: _itemBody(context),
        ),
      ),
    );
  }

  Widget _itemBody(BuildContext context) {
    return Container(
      color: evoColors.background,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: <Widget>[
          const SizedBox(height: 20),
          _itemImage(context),
          const SizedBox(height: 41),
          _itemTitle(),
          const SizedBox(height: 12),
          _itemDescription(),
          const Spacer(),
          CountDownTimerButton(
            countDownTime: ManualLinkCardConfig.preFaceOtpCountDownTimeInSecond,
            onTapAction: moveToFaceOtpStarterScreen,
            onDone: moveToFaceOtpStarterScreen,
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _itemImage(BuildContext context) {
    final double calculatedHeight = EvoUiUtils().calculateVerticalSpace(
      heightPercentage: imageHeight,
      context: context,
    );
    return evoImageProvider.asset(
      EvoImages.imagePreFaceOtpManualLinkCard,
      height: calculatedHeight,
      fit: BoxFit.fill,
    );
  }

  Widget _itemTitle() {
    return Text(
      EvoStrings.preFaceOtpTitleManualLinkCard,
      textAlign: TextAlign.center,
      style: evoTextStyles.h500(),
    );
  }

  Widget _itemDescription() {
    return Text(
      EvoStrings.preFaceOtpDescriptionManualLinkCard,
      textAlign: TextAlign.center,
      style: evoTextStyles.bodyLarge(evoColors.textPassive).copyWith(height: 1.5),
    );
  }

  void moveToFaceOtpStarterScreen() {
    FaceOtpStarterScreen.pushReplacementNamed(
      flowType: widget.flowType,
      callback: widget.callback,
      ekycSessionEntity: null,
    );
  }

  void _handleUserBack() {
    onManualLinkCardFlowFailed(reason: FlowFailedReason.userCancelled);
  }
}
