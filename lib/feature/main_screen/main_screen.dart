// coverage:ignore-file
// TODO: need to refactor this file to testable before writing unit tests (too much dependencies)
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/permission/permission_handler_mixin.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../base/evo_page_state_base.dart';
import '../../data/repository/decree_consent_repo.dart';
import '../../data/response/private_policy_entity.dart';
import '../../model/evo_dialog_id.dart';
import '../../model/user_status.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/evo_action_handler.dart';
import '../../util/evo_authentication_helper.dart';
import '../../util/functions.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../util/ui_utils/evo_dialog_helper.dart';
import '../../util/ui_utils/evo_ui_utils.dart';
import '../biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import '../campaign_list/campaign_list_page.dart';
import '../check_force_update/check_force_update_handler_mixin.dart';
import '../deep_link/deep_link_handler.dart';
import '../feature_toggle.dart';
import '../home_screen/non_user/v1/non_user_home_page.dart';
import '../home_screen/non_user/v2/non_user_home_v2_page.dart';
import '../home_screen/user/home_user_page.dart';
import '../payment/qrcode_scanner/qrcode_scanner_screen.dart';
import '../privacy_policy/privacy_policy_screen.dart';
import '../profile/profile_screen/profile_page.dart';
import '../promotion_list/promotion_list_page.dart';
import '../push_notification/notification_handler.dart';
import '../term_and_condition/term_and_condition_utils.dart';
import '../transaction_history_screen/transaction_history_page.dart';
import '../user_journey/user_journey_handler.dart';
import 'bloc/main_cubit.dart';
import 'bottom_bar_item_model.dart';
import 'bottom_bar_item_widget.dart';
import 'bottom_bar_scanner_button_widget.dart';
import 'main_screen_controller.dart';
import 'main_screen_dialog_handler/main_screen_dialog_handler.dart';
import 'main_screen_initial_action/main_screen_initial_action.dart';
import 'navigation_tab_history.dart';

class MainScreenArg extends PageBaseArg {
  bool isLoggedIn;
  MainScreenChild? initialPage;
  MainScreenInitialAction? initialAction;
  PromotionTabType? initialPromotionTab;
  ActivateBiometricUseCase? activateBiometricUseCase;
  Key? key;

  MainScreenArg({
    required this.isLoggedIn,
    this.initialPage,
    this.initialAction,
    this.initialPromotionTab,
    this.activateBiometricUseCase,
    this.key,
  });
}

class MainScreen extends PageBase {
  /// Make sure you call [EvoPageStateBase.updateUserLoginStatus] function if need
  /// NOTE: be-careful when using this func, if there already this screen in the navigation stack -> initState will not be called
  /// refer bug: https://trustingsocial1.atlassian.net/browse/EMA-6141
  static void goNamed({
    required bool isLoggedIn,
    MainScreenChild? initialPage,
    MainScreenInitialAction? initialAction,
    PromotionTabType? initialPromotionTab,
    ActivateBiometricUseCase? activateBiometricUseCase,
  }) {
    return navigatorContext?.goNamed(
      Screen.mainScreen.name,
      extra: MainScreenArg(
        isLoggedIn: isLoggedIn,
        initialPage: initialPage,
        initialAction: initialAction,
        initialPromotionTab: initialPromotionTab,
        activateBiometricUseCase: activateBiometricUseCase,
      ),
    );
  }

  /// Make sure you call [EvoPageStateBase.updateUserLoginStatus] function if need
  static void pushReplacementNamed({
    required bool isLoggedIn,
    MainScreenChild? initialPage,
    MainScreenInitialAction? initialAction,
    PromotionTabType? initialPromotionTab,
    ActivateBiometricUseCase? activateBiometricUseCase,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.mainScreen.name,
      extra: MainScreenArg(
        isLoggedIn: isLoggedIn,
        initialPage: initialPage,
        initialAction: initialAction,
        initialPromotionTab: initialPromotionTab,
        activateBiometricUseCase: activateBiometricUseCase,
        // // Add unique key to rebuild main screen when pushReplacementNamed
        key: evoUtilFunction.getScreenUniqueKey(),
      ),
    );
  }

  static void removeUntilAndPushReplacementNamed({
    required bool isLoggedIn,
    MainScreenChild? initialPage,
    MainScreenInitialAction? initialAction,
    PromotionTabType? initialPromotionTab,
    ActivateBiometricUseCase? activateBiometricUseCase,
  }) {
    return navigatorContext?.removeUntilAndPushReplacementNamed(
      Screen.mainScreen.name,
      (Route<dynamic> route) => route.isFirst,
      extra: MainScreenArg(
        isLoggedIn: isLoggedIn,
        initialPage: initialPage,
        initialAction: initialAction,
        initialPromotionTab: initialPromotionTab,
        activateBiometricUseCase: activateBiometricUseCase,
        // Add unique key to rebuild main screen when pushReplacementNamed
        key: evoUtilFunction.getScreenUniqueKey(),
      ),
    );
  }

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.mainScreen.routeName);

  final bool isLoggedIn;

  final MainScreenChild? initialPage;
  final MainScreenInitialAction? initialAction;
  final PromotionTabType? initialPromotionTab;
  final ActivateBiometricUseCase? activateBiometricUseCase;

  const MainScreen({
    required this.isLoggedIn,
    super.key,
    this.initialPage = MainScreenChild.home,
    this.initialAction,
    this.initialPromotionTab,
    this.activateBiometricUseCase,
  });

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends EvoPageStateBase<MainScreen>
    with
        NotificationUserInteractionModule,
        SingleTickerProviderStateMixin,
        PermissionHandlerMixin,
        CheckForceUpdateHandlerMixin
    implements MainScreenController {
  static const int numberOfItemNavigationTabHistory = 2;

  final MainCubit _cubit = MainCubit(
    appState: getIt.get<AppState>(),
    decreeConsentRepo: getIt.get<DecreeConsentRepo>(),
    mainScreenDialogHandler: MainScreenDialogHandler(),
    featureToggle: getIt.get<FeatureToggle>(),
  );

  DateTime? currentBackPressTime;

  final double _bottomTabBarHeight = 60;

  final List<BottomBarItemModel> _bottomBarData = <BottomBarItemModel>[];

  late PageController _pageController;

  late TabController _promotionTabController;

  final NavigationHistoryStack navigationHistoryTab = NavigationHistoryStack(
      defaultPage: MainScreenChild.home, size: numberOfItemNavigationTabHistory);

  final double _welcomeDialogImageHeightPercentage = 0.23;

  @override
  void initState() {
    super.initState();

    _promotionTabController = TabController(
      initialIndex: widget.initialPromotionTab?.index ?? 0,
      length: PromotionTabType.values.length,
      vsync: this,
    );
    _initBottomBarModel();

    navigationHistoryTab.push(widget.initialPage ?? MainScreenChild.home);

    _pageController = PageController(initialPage: navigationHistoryTab.top.pageIndex);

    SchedulerBinding.instance.addPostFrameCallback((_) async {
      // force update if needed
      await checkForceUpdate().then((_) {
        _cubit.handleFeatureProcessed(FeaturesWithDialogDisplay.forceUpdate);
      }).then((_) async {
        await _checkConsentAgreed();
        _handleInitialAction(widget.initialAction);
      });
      // show system request permission dialog
      // it can be run asynchronously without blocking other business logics
      // but it should be right before deeplink processing
      // because there is some logic between deeplink and request notification permission popup
      _showSystemRequestPermissionDialog();
      // handle deep link request if need
      final bool isDeepLinkProceed = await _handleDeepLinkRequestIfNeed();

      if (!isDeepLinkProceed) {
        await UserJourneyHandler().handleBackUserJourneyIfNeeded();
      }

      // check decree consent if need
      if (!isDeepLinkProceed && widget.isLoggedIn) {
        _cubit.checkDecreeConsent();
      }
    });
  }

  Future<void> _checkConsentAgreed() async {
    final EvoLocalStorageHelper secureStorageHelper = getIt.get<EvoLocalStorageHelper>();
    if (widget.isLoggedIn || await EvoAuthenticationHelper().isCanLogInOnOldDevice()) {
      await secureStorageHelper.setConsentAgreed(true);
      return;
    }

    await TermAndConditionUtils().checkConsentAgreed();
  }

  @override
  void didUpdateWidget(covariant MainScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    _initBottomBarModel();
  }

  @override
  void dispose() {
    _promotionTabController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return getContentWidget(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    if (isRevampHomePageForNonUserShowed()) {
      return const NonUserHomeV2Page();
    }

    // Note: Fix for show SnackBar on FAB and TabBar
    // Link: https://stackoverflow.com/a/58834439/10262450
    return BlocProvider<MainCubit>(
      create: (_) => _cubit,
      child: BlocListener<MainCubit, MainState>(
        listener: (_, MainState state) {
          _listenStateChange(state);
        },
        child: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return PopScope(
                canPop: false,
                onPopInvokedWithResult: (bool didPop, _) async {
                  if (didPop) {
                    return;
                  }

                  final bool canPop = await _onWillPop();
                  if (canPop) {
                    navigatorContext?.pop();
                    return;
                  }
                },
                child: Scaffold(
                  body: PageView(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    onPageChanged: (int index) {
                      // TODO implement onPageChanged logic
                    },
                    children: _bottomBarData.map((BottomBarItemModel e) => e.page).toList(),
                  ),
                  bottomNavigationBar: BottomAppBar(
                    color: Colors.white,
                    child: SizedBox(child: _buildBottomBarWidget()),
                  ),
                  floatingActionButton: BottomBarScannerButtonWidget(
                    onTap: () {
                      handleBottomBarScannerButton();
                    },
                  ),
                  floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
                  resizeToAvoidBottomInset: false,
                ),
              );
            },
          ),
          resizeToAvoidBottomInset: false,
        ),
      ),
    );
  }

  Widget _buildBottomBarWidget() {
    final List<Widget> bottomBarItemWidgets = <Widget>[];
    for (int index = 0; index < _bottomBarData.length; index++) {
      final BottomBarItemModel element = _bottomBarData[index];
      bottomBarItemWidgets.add(
        Flexible(
          fit: FlexFit.tight,
          child: SizedBox(
            height: _bottomTabBarHeight,
            child: BottomBarItemWidget(
              label: element.label,
              icon: element.icon,
              isSelected: index == navigationHistoryTab.top.pageIndex,
              onTap: () {
                _changeTabIfNeed(index);
              },
            ),
          ),
        ),
      );
    }

    /// Insert hidden item for scanner button
    bottomBarItemWidgets.insert(
      bottomBarItemWidgets.length ~/ 2,
      Flexible(
        fit: FlexFit.tight,
        child: SizedBox(
          height: _bottomTabBarHeight,
          child: BottomBarItemWidget(
            label: EvoStrings.bottomBarScanQRLabel,
            icon: null,
            isSelected: false,
            onTap: () {
              handleBottomBarScannerButton();
            },
          ),
        ),
      ),
    );

    return Wrap(children: <Widget>[
      Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: bottomBarItemWidgets,
      )
    ]);
  }

  void _initBottomBarModel() {
    _bottomBarData.clear();
    _bottomBarData.addAll(<BottomBarItemModel>[
      BottomBarItemModel(
          label: EvoStrings.bottomBarHomeLabel,
          icon: EvoImages.icBottomBarHome,
          page: widget.isLoggedIn
              ? HomeUserPage(mainScreenController: this)
              : NonUserHomePage(mainScreenController: this)),
      BottomBarItemModel(
          label: EvoStrings.bottomBarHistoryLabel,
          icon: EvoImages.icBottomBarHistory,
          page: TransactionHistoryPage(
            isLoggedIn: widget.isLoggedIn,
          )),
      BottomBarItemModel(
          label: EvoStrings.bottomBarRewardLabel,
          icon: EvoImages.icBottomBarReward,
          page: widget.isLoggedIn
              ? PromotionListPage(controller: _promotionTabController)
              : const CampaignListPage()),
      BottomBarItemModel(
          label:
              widget.isLoggedIn ? EvoStrings.bottomBarAccountLabel : EvoStrings.bottomBarLoginLabel,
          icon: EvoImages.icBottomProfile,
          page: ProfileScreen(mainScreenController: this))
    ]);
  }

  void _changeTabIfNeed(int index) {
    if (!widget.isLoggedIn && MainScreenChild.getByIndex(index) == MainScreenChild.account) {
      EvoActionHandler().openAuthenticationScreen();
    } else if (navigationHistoryTab.top.pageIndex != index) {
      _showPageAtIndex(index);
    }
  }

  void _showPageAtIndex(int index) {
    setState(() {
      navigationHistoryTab.push(MainScreenChild.getByIndex(index));
      _pageController.jumpToPage(index);
    });
  }

  @override
  void jumpToPage(MainScreenChild screenChild) {
    _showPageAtIndex(screenChild.pageIndex);
  }

  @override
  void jumpToPromotionPage({PromotionTabType type = PromotionTabType.campaign}) {
    _promotionTabController.index = type.value;
    _showPageAtIndex(MainScreenChild.promotion.pageIndex);
  }

  @override
  MainScreenChild getCurrentPage() {
    return navigationHistoryTab.top;
  }

  /// Exist app if the current page is [HomePage] and the user clicked Back button 2 times within 2 seconds.
  /// Otherwise show toast to guide them
  Future<bool> _onWillPop() async {
    if (navigationHistoryTab.top == MainScreenChild.home && navigationHistoryTab.length == 1) {
      final DateTime now = DateTime.now();
      if (currentBackPressTime == null ||
          commonUtilFunction.isOverDuration(
              timeToCheck: currentBackPressTime!, durationInMilliseconds: 2000)) {
        currentBackPressTime = now;
        showSnackBarNeutral(EvoStrings.existWarning);
        return Future<bool>.value(false);
      }
      return Future<bool>.value(true);
    } else {
      final MainScreenChild tab = navigationHistoryTab.pop();
      jumpToPage(tab);
      return Future<bool>.value(false);
    }
  }

  /// ** Edge case ** :
  /// Because MainPage contains subPage HomePage, HistoryPage, PromotionPage, ProfilePage (Bottom Navigation Menu)
  /// so when navigate to MainPage => MainPage & 1 of 4 above pages return isTopVisible = true
  /// => [handleBiometricChangedIfNeed] is called 2 times when app resumed. to ensure [handleBiometricChangedIfNeed]
  /// is called 1 time, set [hasRouteObserver()] = false
  @override
  bool hasRouteObserver() => false;

  void _handleInitialAction(MainScreenInitialAction? initialAction) {
    initialAction?.process();
  }

  void startScanQRPayment() {
    _cubit.preparePaymentDataIfNeed();
    QrCodeScannerScreen.openSingleInstance();
  }

  @visibleForTesting
  void handleBottomBarScannerButton() {
    if (widget.isLoggedIn) {
      startScanQRPayment();
    } else {
      _handleNonAuthorizedUserClickScan();
    }
  }

  void _handleNonAuthorizedUserClickScan() {
    EvoDialogHelper().showDialogBottomSheet(
      dialogId: EvoDialogId.nonAuthorizedUserClickScanBottomSheet,
      isShowButtonClose: true,
      header: evoImageProvider.asset(
        EvoImages.imgNonAuthorizedUserScan,
        width: context.screenWidth,
        fit: BoxFit.fitWidth,
      ),
      title: EvoStrings.nonAuthorizedUserScanPopupTitle,
      content: EvoStrings.nonAuthorizedUserScanPopupContent,
      textPositive: EvoStrings.nonAuthorizedUserScanPopupButton,
      onClickPositive: () {
        /// dismiss popup
        navigatorContext?.pop();

        EvoActionHandler().openAuthenticationScreen();
      },
    );
  }

  Future<void> _listenStateChange(MainState state) async {
    if (state is BiometricChangedState) {
      await showBiometricChangedPopupAndUpdateStatus();
      _cubit.handleFeatureProcessed(FeaturesWithDialogDisplay.biometricChanged);
      return;
    }
    if (state is BiometricUnusableState || state is BiometricValidState) {
      _cubit.handleFeatureProcessed(FeaturesWithDialogDisplay.biometricChanged);
    }

    if (state is BiometricUnusableState) {
      await showBiometricTokenUnUsableToastAndUpdateStatus();
      return;
    }

    if (state is DecreeConsentedState) {
      _handleDecreeConsented();
      return;
    }

    if (state is DecreeNotYetConsentState) {
      _handleDecreeNotYetConsent(entity: state.entity);
      return;
    }

    if (state is DecreeConsentErrorState) {
      // only check for server error 500 - refer: https://trustingsocial1.atlassian.net/browse/EMA-1096
      if (state.errorUIModel.statusCode == CommonHttpClient.INTERNAL_SERVER_ERROR) {
        _handleDecreeNotYetConsent(errorCode: state.errorUIModel.statusCode);
      }
      handleEvoApiError(state.errorUIModel);
      return;
    }

    if (state is AllFeaturesWithDialogProcessed) {
      _cubit.clearUserStatus();
      _handleUserStatus(state.userStatus);
      return;
    }
  }

  void _handleDecreeConsented() {
    _cubit.handleFeatureProcessed(FeaturesWithDialogDisplay.decreeConsent);

    if (widget.isLoggedIn) {
      _handleAfterLoggedIn();
    }
  }

  void _handleDecreeNotYetConsent({PrivacyPolicyEntity? entity, int? errorCode}) {
    PrivacyPolicyScreen.pushReplacementNamed(
      privatePolicyEntity: entity,
      errorCode: errorCode,
    );
  }

  Future<void> _handleAfterLoggedIn() async {
    final AppState appState = getIt.get<AppState>();
    if (appState.actionAfterLogin != null) {
      appState.actionAfterLogin?.call();

      // reset callback after called
      appState.actionAfterLogin = null;
    } else {
      _cubit.handleBiometricFeature(
        activateBiometricUseCase: widget.activateBiometricUseCase,
      );
    }
  }

  /// Show WelcomeBackDialog if a user has logged in within 30 days
  /// after their account was successfully deleted.
  void _handleUserStatus(UserStatus? userStatus) {
    if (userStatus != UserStatus.revokeDeletion) {
      return;
    }

    EvoDialogHelper().showDialogConfirm(
      imageHeader: evoImageProvider.asset(
        EvoImages.imgWelcomeBack,
        height: EvoUiUtils().calculateVerticalSpace(
          context: context,
          heightPercentage: _welcomeDialogImageHeightPercentage,
        ),
      ),
      isShowButtonClose: true,
      title: EvoStrings.welcomeBackDialogTitle,
      titleTextStyle: evoTextStyles.h500(color: evoColors.textActive),
      content: EvoStrings.welcomeBackDialogContent,
      contentTextStyle: evoTextStyles.bodyMedium(evoColors.textPassive),
      textPositive: EvoStrings.welcomeBackDialogBtn,
      dialogId: EvoDialogId.welcomeBackDialog,
      isDismissible: false,
      positiveButtonStyle: evoButtonStyles.primary(ButtonSize.xLarge),
      onClickPositive: () {
        /// dismiss popup
        navigatorContext?.pop();
      },
      titleTextAlign: TextAlign.center,
      contentTextAlign: TextAlign.center,
    );
  }

  /// handle DeepLink Request if need
  /// refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/**********/EVO+App+Deep+Links
  Future<bool> _handleDeepLinkRequestIfNeed() {
    final DeepLinkHandler deepLinkHandler = getIt.get<DeepLinkHandler>();
    return deepLinkHandler.executeCachedDeeplink(isLoggedIn: widget.isLoggedIn);
  }

  bool isRevampHomePageForNonUserShowed() {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();
    return !widget.isLoggedIn &&
        featureToggle.enableNonUserHomePageVersion == NonUserHomePageVersion.version_2;
  }

  Future<void> _showSystemRequestPermissionDialog() async {
    /// there is a issue that it can't open Tracking Permission dialog after opened Notification Permission dialog
    /// work-around is calling delay function (5s) before requesting Tracking Permission.
    await askingForNotificationPermission();
    await setUpNotificationListeners();

    await Future<void>.delayed(const Duration(seconds: 5));
    await requestAppTrackingAuthorizationPermission();
  }
}
