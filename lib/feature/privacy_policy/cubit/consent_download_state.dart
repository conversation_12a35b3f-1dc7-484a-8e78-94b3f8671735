import 'package:flutter_common_package/ui_model/error_ui_model.dart';

abstract class ConsentDownloadState {}

class ConsentDownloadInitialState extends ConsentDownloadState {}

class ConsentDownloadingState extends ConsentDownloadState {}

class ConsentDownloadedState extends ConsentDownloadState {}

class ConsentDownloadInvalidUrlState extends ConsentDownloadState {}

class ConsentDownloadErrorState extends ConsentDownloadState {
  final ErrorUIModel? errorUIModel;

  ConsentDownloadErrorState({this.errorUIModel});
}
