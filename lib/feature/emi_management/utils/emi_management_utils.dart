import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/intl.dart';

import '../../../data/response/emi_conversion_status_info_entity.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';

final EmiManagementUtilFunctions emiManagementUtilFunctions =
    getIt.get<EmiManagementUtilFunctions>();

class EmiManagementUtilFunctions {
  String convertDateTimeToDisplay(DateTime? inputDate) {
    if (inputDate == null) {
      return '';
    }

    final DateFormat outputFormat = DateFormat('HH:mm - dd/MM/yyyy');
    final String outputDate = outputFormat.format(inputDate);
    return outputDate;
  }

  // Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/3295412375/EMI+conversion+repayment+status+management#EMI-record-status-and-equivalent-status-on-UI
  // Mapping color with BE: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3732537795/FS+EMI+EMI+Management#Design
  Color parseEmiRecordStatus(EmiConversionStatusType? status) {
    switch (status) {
      case EmiConversionStatusType.autoApproved:
      case EmiConversionStatusType.manualApproved:
      case EmiConversionStatusType.done:
        return evoColors.emiManagementApproved;
      case EmiConversionStatusType.rejected:
        return evoColors.emiManagementRejected;
      case EmiConversionStatusType.received:
      default:
        return evoColors.emiManagementProcessing;
    }
  }
}
