import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../main_screen/main_screen.dart';

class EmiManagementEmptyWidget extends StatelessWidget {
  const EmiManagementEmptyWidget({super.key});

  @visibleForTesting
  final double defaultPaddingTopPercentage = 108 / 812;

  @override
  Widget build(BuildContext context) {
    final double topSpacing = EvoUiUtils().calculateVerticalSpace(
      heightPercentage: defaultPaddingTopPercentage,
      context: context,
    );

    return Column(
      children: <Widget>[
        SizedBox(height: topSpacing),
        evoImageProvider.asset(EvoImages.imgEmiEmptyState),
        const SizedBox(height: 24),
        Text(
          EvoStrings.emiManagementEmptyTitle,
          style: evoTextStyles.h500(),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Text(
          EvoStrings.emiManagementEmptyDescription,
          style: evoTextStyles.bodyLarge(evoColors.textPassive),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        _ctaGotoMainScreen(),
      ],
    );
  }

  Widget _ctaGotoMainScreen() {
    return CommonButton(
      onPressed: () {
        MainScreen.pushReplacementNamed(isLoggedIn: true);
      },
      style: evoButtonStyles.primary(ButtonSize.large),
      isWrapContent: false,
      child: const Text(EvoStrings.moveToHome),
    );
  }
}
