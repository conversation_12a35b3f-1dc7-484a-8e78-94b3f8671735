part of 'input_phone_number_cubit.dart';

@immutable
abstract class InputPhoneNumberState implements BlocState {}

class InputPhoneNumberInitial extends InputPhoneNumberState {}

class InputPhoneNumberLoading extends InputPhoneNumberState {}

class ChangePhoneNumber extends InputPhoneNumberState {}

class InputPhoneNumberPhoneSuccess extends InputPhoneNumberState {
  final SignInEntity? entity;

  InputPhoneNumberPhoneSuccess({this.entity});
}

class InputPhoneNumberFailed extends InputPhoneNumberState {
  final ErrorUIModel errorUIModel;

  InputPhoneNumberFailed(this.errorUIModel);
}
