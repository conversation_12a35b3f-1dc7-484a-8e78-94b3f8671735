import 'package:flutter/material.dart';

import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../biometric/model/biometric_ui_model.dart';

class LoginByBiometricWidget extends StatelessWidget {
  final VoidCallback? onClick;

  const LoginByBiometricWidget({super.key, this.onClick});

  @override
  Widget build(BuildContext context) {
    final BiometricTypeUIModel bioInfo = getIt.get<AppState>().bioTypeInfo;
    return GestureDetector(
      onTap: onClick,
      child: Container(
        margin: const EdgeInsets.only(top: 24),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: evoColors.primary.withAlpha(20),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            evoImageProvider.asset(
              bioInfo.iconPath,
              color: EvoColors().primary,
            ),
            const SizedBox(width: 12),
            Text(
              '${EvoStrings.loginScreenLoginWithBiometric} ${bioInfo.biometricTypeName}',
              style: evoTextStyles.h200(color: evoColors.textActive),
            ),
          ],
        ),
      ),
    );
  }
}
