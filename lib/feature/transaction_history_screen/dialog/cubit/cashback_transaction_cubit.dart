import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/cashback_repo.dart';
import '../../../../data/request/cashback_records_request.dart';
import '../../../../data/response/current_cashback_info_entity.dart';
import '../../../../data/response/records_cashback_entity.dart';
import '../../../../resources/global.dart';
import '../mock/mock_cashback_transaction_case.dart';

part 'cashback_transaction_state.dart';

class CashbackTransactionCubit extends CommonCubit<CashbackTransactionsState> {
  final CashbackRepo cashbackRepo;

  CashbackTransactionCubit({
    required this.cashbackRepo,
  }) : super(CashbackTransactionsInitialState());

  // Refer: https://trustingsocial1.atlassian.net/browse/EMA-5714
  @visibleForTesting
  final int defaultNumberItemPerPage = 12;

  @visibleForTesting
  final int currentPage = defaultFirstPage;

  Future<void> getCashbackRecords() async {
    emit(CashbackTransactionsLoadingState());

    final RecordsCashbackEntity entity = await cashbackRepo.getCashbackRecords(
      request: CashbackRecordsRequest(
        pageId: currentPage,
        perPage: defaultNumberItemPerPage,
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockCashbackTransactionFileName(MockCashbackTransactionCase.success),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(CashbackTransactionsSucceedState(records: entity.records));
      return;
    }

    emit(CashbackTransactionsFailureState(errorUIModel: ErrorUIModel.fromEntity(entity)));
  }
}
