import 'package:flutter/material.dart';

import '../../../../data/response/payment_result_transaction_entity.dart';
import '../../../../resources/resources.dart';
import '../../../../util/extension.dart';

class TransactionHistoryCashbackRecordItem extends StatelessWidget {
  final PaymentResultTransactionEntity? transaction;
  final VoidCallback? onTap;

  const TransactionHistoryCashbackRecordItem({
    super.key,
    this.transaction,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        GestureDetector(
          onTap: onTap,
          child: Row(
            children: <Widget>[
              Text(
                EvoStrings.refundInfoTitle,
                style: evoTextStyles.h200(),
              ),
              const SizedBox(width: 4),
              _buildTooltipWidget(),
            ],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          transaction?.updatedAtDateTime?.toTransactionDateTimeFormat(dateFormat: 'dd/MM/yyyy') ??
              '',
          style: evoTextStyles.bodySmall(color: evoColors.textPassive2),
        ),
      ],
    );
  }

  Widget _buildTooltipWidget() {
    return evoImageProvider.asset(
      EvoImages.icInfo,
      width: 16,
      fit: BoxFit.fitWidth,
      color: evoColors.textPassive2,
    );
  }
}
