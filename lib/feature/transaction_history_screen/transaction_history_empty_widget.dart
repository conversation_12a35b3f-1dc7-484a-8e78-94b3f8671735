import 'package:flutter/material.dart';

import '../../resources/resources.dart';
import '../../util/ui_utils/evo_ui_utils.dart';
import '../../widget/empty_data_container.dart';

class TransactionHistoryEmptyWidget extends StatelessWidget {
  @visibleForTesting
  final double paddingTopPercentage = 0.086;

  const TransactionHistoryEmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: EmptyDataContainer(
        assetName: EvoImages.bgTransactionHistoryEmpty,
        text: EvoStrings.transactionHistoryEmptyTittle,
        bottomWidgets: <Widget>[
          const SizedBox(height: 8),
          Text(
            EvoStrings.transactionHistoryEmptyDesc,
            style: evoTextStyles.bodyMedium(evoColors.textPassive),
            textAlign: TextAlign.center,
          ),
        ],
        paddingTop: EvoUiUtils().calculateVerticalSpace(
          context: context,
          heightPercentage: paddingTopPercentage,
        ),
      ),
    );
  }
}
