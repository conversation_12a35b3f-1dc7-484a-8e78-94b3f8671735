part of 'transaction_detail_cubit.dart';

@immutable
abstract class TransactionDetailState {}

class TransactionDetailInitial extends TransactionDetailState {}

class TransactionDetailLoading extends TransactionDetailState {}

class TransactionDetailSuccess extends TransactionDetailState {
  final PaymentResultTransactionEntity? transaction;

  TransactionDetailSuccess({this.transaction});
}

class TransactionDetailError extends TransactionDetailState {
  final ErrorUIModel? error;
  final bool isRefresh;

  TransactionDetailError({this.error, this.isRefresh = false});
}
