part of 'face_auth_selfie_capturing_cubit.dart';

@immutable
abstract class FaceAuthSelfieCapturingState implements BlocState {}

class FaceAuthSelfieCapturingInitialState extends FaceAuthSelfieCapturingState {}

class FaceAuthSelfieCapturingInProgressState extends FaceAuthSelfieCapturingState {}

class FaceAuthSelfieCapturingErrorNotInitializedState extends FaceAuthSelfieCapturingState {}

class FaceAuthSelfieCapturingSuccessState extends FaceAuthSelfieCapturingState {
  final FacialVerificationStartCapturingSuccessResult successResult;

  FaceAuthSelfieCapturingSuccessState({
    required this.successResult,
  });
}

class FaceAuthSelfieCapturingErrorState extends FaceAuthSelfieCapturingState {
  final FacialVerificationStartCapturingErrorResult errorResult;

  FaceAuthSelfieCapturingErrorState({
    required this.errorResult,
  });
}
