import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../payment/amount_validator.dart';
import '../utils/payment_with_emi_utils.dart';

part 'check_display_pay_with_emi_state.dart';

class CheckDisplayPayWithEMICubit extends CommonCubit<CheckDisplayWithEMIState> {
  final PaymentWithEMIUtils payWithEMIUtils;
  final AmountValidator amountValidator;

  CheckDisplayPayWithEMICubit({
    required this.payWithEMIUtils,
    required this.amountValidator,
  }) : super(CheckDisplayWithEMIInitial());

  void checkDisplayPayWithEMI({String? amountStr, String? productCode, String? merchantId}) {
    if (!payWithEMIUtils.isEmiFeatureEnabled() ||
        productCode == null ||
        amountStr == null ||
        merchantId == null) {
      emit(PayWithEMIIsHidden());
      return;
    }

    final int? amount = payWithEMIUtils.getAmountFromStrWithCurrency(amountStr);
    if (amount == null) {
      emit(PayWithEMIIsHidden());
      return;
    }

    final bool isAmountValidOrderRules = amountValidator.validateAmount(amount, productCode);
    if (!isAmountValidOrderRules) {
      emit(PayWithEMIIsHidden());
      return;
    }

    final bool canPayWithEMI = payWithEMIUtils.canPayWithEMI(productCode, amount, merchantId);
    if (!canPayWithEMI) {
      emit(PayWithEMIIsHidden());
      return;
    }

    emit(PayWithEMIIsDisplayed());
  }

  void hidePayWithEmi() {
    emit(PayWithEMIIsHidden());
  }
}
