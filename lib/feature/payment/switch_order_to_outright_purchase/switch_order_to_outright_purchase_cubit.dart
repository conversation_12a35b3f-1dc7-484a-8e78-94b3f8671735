import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../data/repository/checkout_repo.dart';
import '../../../data/response/create_order_entity.dart';
import '../../../data/response/qr_code_parse_entity.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../payment/qrcode_scanner/model/qr_code_type.dart';
import '../mock_file/mock_checkout_file_name.dart';
import '../utils/payment_with_emi_utils.dart';

part 'switch_order_to_outright_purchase_state.dart';

class SwitchOrderToOutRightPurchaseCubit extends CommonCubit<SwitchToOutrightPaymentState> {
  final CheckOutRepo checkOutRepo;
  final PaymentWithEMIUtils paymentForEmiUtils;

  SwitchOrderToOutRightPurchaseCubit({
    required this.checkOutRepo,
    required this.paymentForEmiUtils,
  }) : super(SwitchToOutrightPaymentInitState());

  /// at currently we only support Dynamic QR code
  Future<void> convertOrderToOutRightPayment(QrCodeParseEntity qrCodeParseEntity) async {
    final String? productCode = qrCodeParseEntity.productCode;
    switch (productCode) {
      case QrProductCode.pa03String:
        await createOrderForDynamicQrCode(qrCodeParseEntity);
        break;

      default:
        commonLog('Unsupported product code: $productCode');
        break;
    }
  }

  /// Dynamic order: Create order by id
  @visibleForTesting
  Future<void> createOrderForDynamicQrCode(QrCodeParseEntity qrCodeEntity) async {
    emit(SwitchToOutrightPaymentApiLoadingState());
    final CreateOrderEntity entity = await checkOutRepo.createOrderByOrderId(
      storeId: qrCodeEntity.storeId,
      merchantId: qrCodeEntity.merchantId,
      productCode: qrCodeEntity.productCode,
      orderId: qrCodeEntity.orderId,
      mockConfig: MockConfig(
        enable: false,
        fileName: createOrderMockFileName(verdict: CreateOrderMockVerdict.orderExpired),
        statusCode: CommonHttpClient.BAD_REQUEST,
      ),
    );

    getIt<AppState>().paymentSharedData.orderExtraInfo = entity.extraInfo;

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      paymentForEmiUtils.selectedPaymentService = PaymentService.outrightPurchase;
      emit(CreateDynamicOrderSuccessState(entity: entity));
      return;
    }

    if (entity.statusCode == CommonHttpClient.BAD_REQUEST &&
        (entity.verdict == CreateOrderEntity.verdictUnqualifiedToActive ||
            entity.verdict == CreateOrderEntity.verdictPosLimitInsufficient ||
            entity.verdict == CreateOrderEntity.verdictCardUnqualifiedToSetPosLimit)) {
      emit(CreateOrderActivatePosLimitErrorState(entity: entity));
      return;
    }

    final ErrorUIModel errorUIModel = ErrorUIModel.fromEntity(entity);
    _handleUpdateOrderError(errorUIModel);
  }

  void _handleUpdateOrderError(ErrorUIModel errorUIModel) {
    final String? verdict = errorUIModel.verdict;
    final bool isOrderExpired = paymentForEmiUtils.checkIfOrderExpiredByVerdict(verdict);
    if (isOrderExpired) {
      emit(SwitchToOutrightPaymentOrderExpiredState());
      return;
    }
    emit(SwitchToOutrightPaymentCommonErrorState(errorUIModel: errorUIModel));
  }
}
