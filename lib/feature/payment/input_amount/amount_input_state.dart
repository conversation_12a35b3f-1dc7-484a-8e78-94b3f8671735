abstract class AmountInputState {}

/// The [AmountInputIdleState] is activated when the app is waiting for user input.
/// This occurs in two situations:
/// - When the user first interacts with the UI.
/// - When the user clears all input.
class AmountInputIdleState extends AmountInputState {}

class InvalidAmountState extends AmountInputState {
  final String? errorMessage;

  InvalidAmountState({this.errorMessage});
}

/// The [ValidAmountState] is responsible for updating the UI when the amount changes from an invalid to a valid value.
/// Once the amount is valid, the UI will hide the error message.
class ValidAmountState extends AmountInputState {}
