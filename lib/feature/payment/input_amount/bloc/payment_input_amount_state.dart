import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/checkout_repo.dart';
import '../../../../data/response/action_entity.dart';
import '../../../../data/response/create_order_entity.dart';
import '../../../../data/response/emi_package_entity.dart';
import '../../../../data/response/order_session_entity.dart';

abstract class PaymentInputAmountState {}

class PaymentInputAmountInitial extends PaymentInputAmountState {}

class PaymentInputAmountNoteUpdated extends PaymentInputAmountState {
  final String? noteText;

  PaymentInputAmountNoteUpdated({this.noteText});
}

class CreatePaymentLoadingState extends PaymentInputAmountState {}

class CreatePaymentSuccessState extends PaymentInputAmountState {
  final OrderSessionEntity? orderSession;
  final List<EmiPackageEntity>? emiPackages;
  final PaymentService paymentService;
  final ActionEntity? prerequisitesAction;

  CreatePaymentSuccessState({
    required this.paymentService,
    this.orderSession,
    this.emiPackages,
    this.prerequisitesAction,
  });
}

class CreatePaymentErrorState extends PaymentInputAmountState {
  final ErrorUIModel? error;

  CreatePaymentErrorState(this.error);
}

class CreatePaymentWithEMIUnqualifiedState extends PaymentInputAmountState {
  final int amount;
  final ErrorUIModel? error;

  CreatePaymentWithEMIUnqualifiedState({required this.amount, this.error});
}

class CreatePaymentWithActivePosLimitErrorState extends PaymentInputAmountState {
  final CreateOrderEntity? entity;
  final PaymentService paymentService;

  CreatePaymentWithActivePosLimitErrorState({
    required this.entity,
    required this.paymentService,
  });
}

class ValidateSubmittedAmountError extends PaymentInputAmountState {
  final String? errorMessage;

  ValidateSubmittedAmountError({this.errorMessage});
}

class ValidateSubmittedAmountSuccess extends PaymentInputAmountState {
  final int amount;
  final PaymentService paymentService;

  ValidateSubmittedAmountSuccess({
    required this.amount,
    required this.paymentService,
  });
}
