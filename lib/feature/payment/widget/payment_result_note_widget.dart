import 'package:flutter/material.dart';

import '../../../data/response/emi_conversion_status_info_entity.dart';
import '../../../data/response/payment_result_transaction_entity.dart';
import '../../../model/transaction_status_model.dart';
import '../../../resources/resources.dart';
import '../../payment/payment_config.dart';
import 'item_text_order_summary_widget.dart';

class PaymentResultNoteWidgetArgs {
  final bool showMessageBox;
  final bool showHeaderInfo;
  final Color? statusColor;
  final Color? titleColor;
  final String? message;
  final String? title;
  final String? subTitle;
  final VoidCallback? onTapHeaderInfo;

  PaymentResultNoteWidgetArgs({
    this.showMessageBox = true,
    this.showHeaderInfo = false,
    this.statusColor,
    this.title,
    this.titleColor,
    this.subTitle,
    this.message,
    this.onTapHeaderInfo,
  });

  static PaymentResultNoteWidgetArgs? fromTransaction(
    PaymentResultTransactionEntity? transaction, {
    VoidCallback? onTapHeaderInfo,
  }) {
    if (transaction == null) {
      return null;
    }

    if (transaction.isEmiTransaction) {
      return fromEMITransactionCases(transaction, onTapHeaderInfo: onTapHeaderInfo);
    }

    return fromOtherTransactionCases(transaction);
  }

  @visibleForTesting
  static PaymentResultNoteWidgetArgs? fromEMITransactionCases(
    PaymentResultTransactionEntity transaction, {
    VoidCallback? onTapHeaderInfo,
  }) {
    final String? status = transaction.status;

    final EmiConversionStatusInfoEntity? conversionStatusInfo =
        transaction.emiInfo?.conversionStatusInfo;
    final String? title = conversionStatusInfo?.content?.title;
    final Color? titleColor = conversionStatusInfo?.status?.titleColor;
    final String? subTitle = conversionStatusInfo?.content?.subTitle;
    final String? userMessage = conversionStatusInfo?.content?.userMessage;
    final Color? statusColor = conversionStatusInfo?.status?.statusColor;

    /// only transactions status  success
    /// -> show headerInfo (title, subTitle) and messageBox depend to response from API
    /// Refer to: https://trustingsocial1.atlassian.net/browse/EMA-2427
    if (status == TransactionStatusModel.success.value) {
      return PaymentResultNoteWidgetArgs(
        showHeaderInfo: conversionStatusInfo != null,
        showMessageBox: conversionStatusInfo?.status == EmiConversionStatusType.rejected,
        title: title,
        subTitle: subTitle,
        titleColor: titleColor,
        statusColor: statusColor,
        message: userMessage,
        onTapHeaderInfo: onTapHeaderInfo,
      );
    }

    return fromOtherTransactionCases(transaction);
  }

  @visibleForTesting
  static PaymentResultNoteWidgetArgs? fromOtherTransactionCases(
    PaymentResultTransactionEntity transaction,
  ) {
    final String? reason = transaction.lastError?.reason;

    /// Don't show payment note block if the reason is null OR empty
    if (reason == null || reason.isEmpty) {
      return null;
    }

    final String? status = transaction.status;

    if (status == TransactionStatusModel.failure.value) {
      return PaymentResultNoteWidgetArgs(
        statusColor: evoColors.error,
        message: reason,
      );
    }

    if (status == TransactionStatusModel.pending.value) {
      return PaymentResultNoteWidgetArgs(
        statusColor: evoColors.pendingTransactionNoteLeftBorder,
        message: reason,
      );
    }

    return null;
  }
}

class PaymentResultNoteWidget extends StatefulWidget {
  final PaymentResultNoteWidgetArgs? args;

  const PaymentResultNoteWidget({
    this.args,
    super.key,
  });

  @override
  State<PaymentResultNoteWidget> createState() => _PaymentResultNoteWidgetState();
}

class _PaymentResultNoteWidgetState extends State<PaymentResultNoteWidget> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final PaymentResultNoteWidgetArgs? args = widget.args;
    if (args == null) {
      return const SizedBox.shrink();
    }

    return _buildContent(
      statusColor: args.statusColor,
      titleColor: args.titleColor,
      title: args.title,
      message: args.message,
      subTitle: args.subTitle,
      showMessageBox: args.showMessageBox,
      showHeaderInfo: args.showHeaderInfo,
      onTapHeaderInfo: args.onTapHeaderInfo,
    );
  }

  Widget _buildContent({
    required Color? statusColor,
    required Color? titleColor,
    required String? title,
    required String? subTitle,
    required String? message,
    bool showMessageBox = true,
    bool showHeaderInfo = false,
    VoidCallback? onTapHeaderInfo,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: showHeaderInfo ? evoColors.secondaryBackground : evoColors.background,
        border: showHeaderInfo
            ? Border(
                bottom: BorderSide(
                  color: evoColors.disableTextFieldBg,
                ),
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          if (showHeaderInfo) ...<Widget>[
            _buildHeaderInfo(
              statusColor: statusColor,
              titleColor: titleColor,
              title: title,
              subTitle: subTitle,
              onTapHeaderInfo: onTapHeaderInfo,
            ),
          ],
          if (showHeaderInfo && showMessageBox) const SizedBox(height: 12),
          if (showMessageBox) ...<Widget>[
            _buildMessageBox(
              statusColor: statusColor,
              message: message,
            ),
          ],
        ],
      ),
    );
  }

  ///https://trustingsocial1.atlassian.net/browse/EMA-1567
  void _onTapToExpand() {
    setState(() {
      _isExpanded = true;
    });
  }

  Widget _buildHeaderInfo({
    required Color? statusColor,
    required Color? titleColor,
    required String? title,
    required String? subTitle,
    VoidCallback? onTapHeaderInfo,
  }) {
    return InkWell(
      onTap: onTapHeaderInfo,
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            Align(
              alignment: Alignment.topCenter,
              child: Container(
                margin: const EdgeInsets.only(
                  right: 6,
                  top: 6,
                ),
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: statusColor,
                ),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  Text(
                    title ?? '',
                    style: evoTextStyles.h200(color: titleColor).copyWith(
                          height: 1.5,
                          letterSpacing: -0.02,
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subTitle ?? '',
                    style:
                        evoTextStyles.bodySmall(color: evoColors.textPassive).copyWith(height: 1.5),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            if (onTapHeaderInfo != null)
              Center(
                child: evoImageProvider.asset(
                  EvoImages.icSmallRightEmi,
                  height: 16,
                  width: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBox({
    required Color? statusColor,
    required String? message,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: evoColors.background,
        borderRadius: BorderRadius.circular(4),
        boxShadow: <BoxShadow>[
          BoxShadow(
            offset: const Offset(0, 15),
            color: evoColors.paymentResultEmiNoteErrorShadow.withOpacity(0.04),
            blurRadius: 32,
            spreadRadius: -16,
          ),
          BoxShadow(
            offset: const Offset(0, 2),
            color: evoColors.paymentResultEmiNoteErrorShadow.withOpacity(0.08),
            blurRadius: 24,
          ),
        ],
      ),
      child: InkWell(
        onTap: _onTapToExpand,
        child: IntrinsicHeight(
          child: Row(
            children: <Widget>[
              Container(
                width: 4,
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    bottomLeft: Radius.circular(4),
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: ItemTextOrderSummaryWidget(
                    message,
                    maxLines: _isExpanded ? null : PaymentConfig.paymentResultEmiNoteErrorMaxLines,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
