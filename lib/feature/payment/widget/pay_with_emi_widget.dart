import 'package:flutter/material.dart';

import '../../../resources/resources.dart';

class PayWithEMIWidget extends StatelessWidget {
  final VoidCallback? onPayWithEMI;

  const PayWithEMIWidget({super.key, this.onPayWithEMI});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPayWithEMI,
      child: Ink(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        decoration: BoxDecoration(
          color: evoColors.emiContainerBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: evoColors.primary,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Expanded(
              child: _contentPayWithEMI(),
            ),
            const SizedBox(width: 12),
            evoImageProvider.asset(EvoImages.icPayWithEMI),
          ],
        ),
      ),
    );
  }

  Widget _contentPayWithEMI() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          EvoStrings.payWithEMITitle,
          style: evoTextStyles.h200(),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          EvoStrings.payWithEMIDescription,
          style: evoTextStyles.bodyMedium(evoColors.textPassive),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        )
      ],
    );
  }
}
