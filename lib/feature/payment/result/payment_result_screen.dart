import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/ui_strings.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/checkout_repo.dart';
import '../../../data/response/payment_result_transaction_entity.dart';
import '../../../data/response/promotion_info_entity.dart';
import '../../../model/transaction_status_model.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/task_polling_handler/polling_task.dart';
import '../../../widget/cta_with_powered_by_widget.dart';
import '../../../widget/evo_loading_widget.dart';
import '../../emi_management/detail_screen/emi_management_detail_screen.dart';
import '../../feature_toggle.dart';
import '../../in_app_review/in_app_review_handler_mixin.dart';
import '../../payment/payment_config.dart';
import '../../payment/result/bloc/payment_result_cubit.dart';
import '../../payment/result/bloc/payment_result_state.dart';
import '../../payment/utils/dialog_enable_pos_limit_handler.dart';
import '../../transaction_detail/transaction_detail_screen.dart';
import '../../../util/task_polling_handler/task_polling_handler.dart';
import '../utils/payment_navigate_helper_mixin.dart';
import '../utils/payment_with_emi_utils.dart';
import '../widget/order_summary_widget/order_summary_widget.dart';
import '../widget/payment_result_and_amount_title_widget.dart';
import '../widget/payment_result_container_widget.dart';
import '../widget/payment_result_note_widget.dart';

class PaymentResultArg extends PageBaseArg {
  final bool needPollingProcessingStatus;
  final String? transactionId;

  PaymentResultArg({
    required this.transactionId,
    this.needPollingProcessingStatus = false,
  });
}

class PaymentResultScreen extends PageBase {
  final PaymentResultArg? arg;

  const PaymentResultScreen({super.key, this.arg});

  static void pushReplacementNamed({
    required String? transactionId,
    bool needPollingProcessingStatus = false,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.paymentResultScreen.name,
      extra: PaymentResultArg(
        transactionId: transactionId,
        needPollingProcessingStatus: needPollingProcessingStatus,
      ),
    );
  }

  @override
  State<PaymentResultScreen> createState() => PaymentResultScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.paymentResultScreen.routeName);
}

@visibleForTesting
class PaymentResultScreenState extends EvoPageStateBase<PaymentResultScreen>
    with PaymentNavigationHelperMixin, InAppReviewHandlerMixin {
  @visibleForTesting
  TaskPollingHandler? cashBackResultPollingHandler;
  @visibleForTesting
  final PaymentResultCubit paymentResultCubit = PaymentResultCubit(
    getIt.get<CheckOutRepo>(),
    getIt.get<AppState>(),
  );

  Timer? _pollingTimeOutTimer;
  bool _needPollingProcessingStatus = false;
  @visibleForTesting
  PollingTask? cashBackPollingTask;
  bool _shouldPollForCashBackResult = true;
  @visibleForTesting
  bool isAppOnForeGround = true;
  final RefreshController _refreshController = RefreshController();
  @visibleForTesting
  final DialogEnablePosLimitHandler dialogEnablePosLimitHandler = DialogEnablePosLimitHandler();

  //wait at least 5s before polling for cashback result, to make sure user has read the payment info
  static const int _waitTimeBeforePollingForCashBackResultInSec = 5;

  @override
  void initState() {
    super.initState();
    initData();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final PaymentWithEMIUtils paymentWithEMIUtils = PaymentWithEMIUtils();
      await paymentWithEMIUtils.updateFlagPostLimitWarning();
    });
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: evoColors.background,
      body: SafeArea(
        top: false,
        child: PopScope(
          canPop: false,
          child: BlocProvider<PaymentResultCubit>(
            create: (_) => paymentResultCubit,
            child: BlocConsumer<PaymentResultCubit, PaymentResultState>(
              listener: (_, PaymentResultState state) {
                _handlePaymentResultListener(state);
              },
              builder: (_, PaymentResultState state) {
                if (state is PaymentResultLoadingState) {
                  return const EvoLoadingWidget();
                }
                if (state is PaymentResultLoadedState) {
                  return _itemBody(state.transaction);
                }
                if (state is PaymentResultPosLimitState) {
                  return _itemBody(state.transaction);
                }
                if (state is PaymentResultErrorState) {
                  return _itemBody(state.savedTransaction);
                }
                return const SizedBox.shrink();
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _itemBody(PaymentResultTransactionEntity? transaction) {
    final TransactionStatusModel? status =
        TransactionStatusModel.formatStatusString(transaction?.status);
    final bool isEmiTransaction = transaction?.isEmiTransaction ?? false;

    return Column(
      children: <Widget>[
        Expanded(
          child: PaymentResultContainerWidget(
              controller: _refreshController,
              onRefresh: () {
                _getTransactionDetail(isRefresh: true);
              },
              child: Column(
                children: <Widget>[
                  PaymentResultTitleAndAmountWidget(
                    status: status ?? TransactionStatusModel.processing,
                    amount: transaction?.userChargeAmount ?? 0,
                    merchantName: transaction?.storeInfo?.merchantName ?? '',
                    isEmiTransaction: isEmiTransaction,
                    onMoreDetail: () {
                      onTapPaymentResultDetail(transaction?.id);
                    },
                  ),
                  PaymentResultNoteWidget(
                    args: PaymentResultNoteWidgetArgs.fromTransaction(
                      transaction,
                      onTapHeaderInfo: () {
                        _goEmiManagementScreen(transaction);
                      },
                    ),
                  ),
                  _buildPaymentResultContent(transaction),
                ],
              )),
        ),
        _itemCtaAndPoweredBy(),
      ],
    );
  }

  void _goEmiManagementScreen(PaymentResultTransactionEntity? transaction) {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();
    if (!featureToggle.enableEmiManagementFeature) {
      // temporary redirect to Transaction Detail by https://trustingsocial1.atlassian.net/browse/EMA-2427
      onTapPaymentResultDetail(transaction?.id);
      return;
    }

    EmiManagementDetailScreen.pushNamed(id: transaction?.emiInfo?.id);
  }

  Widget _buildPaymentResultContent(PaymentResultTransactionEntity? transaction) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: OrderSummaryWidget(
        transactionEntity: transaction,
      ),
    );
  }

  Widget _itemCtaAndPoweredBy() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: CTAWithPoweredByWidget(
        text: EvoStrings.moveToHome,
        onPressed: () {
          final bool isHandlePosLimitShowing =
              paymentResultCubit.state is PaymentResultPosLimitState &&
                  !dialogEnablePosLimitHandler.isDialogEnablePosLimitShown;

          if (isHandlePosLimitShowing) {
            dialogEnablePosLimitHandler.handleDialogEnablePosLimitShowing(
              onClickNegative: handlePaymentFlowComplete,
              onClickPositive: openPosSetupUserGuild,
            );
            return;
          }

          handlePaymentFlowComplete();
        },
      ),
    );
  }

  void onTapPaymentResultDetail(String? transactionId) {
    TransactionDetailScreen.pushNamed(transactionId: transactionId);
  }

  @visibleForTesting
  void initData() {
    _getTransactionDetail();
    _setUpPollingTimeOut();
  }

  void _clearPaymentSharedData() {
    paymentResultCubit.clearPaymentSharedData();
  }

  void _getTransactionDetail({bool isRefresh = false}) {
    paymentResultCubit.getPaymentResult(
      transactionId: widget.arg?.transactionId,
      isRefresh: isRefresh,
    );
  }

  void _setUpPollingTimeOut() {
    _needPollingProcessingStatus = widget.arg?.needPollingProcessingStatus ?? false;
    if (_needPollingProcessingStatus) {
      _pollingTimeOutTimer = Timer(
        const Duration(seconds: PaymentConfig.timeOutPollingGetCheckOutInSec),
        () {
          _markDoNotNeedPollingProcessingStatus();
        },
      );
    }
  }

  void _markDoNotNeedPollingProcessingStatus() {
    _needPollingProcessingStatus = false;
  }

  void _cancelPollingTimeOut() {
    _pollingTimeOutTimer?.cancel();
  }

  Future<void> _handlePaymentResultListener(PaymentResultState state) async {
    if (_refreshController.isRefresh) {
      _refreshController.refreshCompleted();
    }

    if (state is PaymentResultLoadedState) {
      final TransactionStatusModel? status =
          TransactionStatusModel.formatStatusString(state.transaction?.status);
      if (status == TransactionStatusModel.processing) {
        _handlePollingGetTransactionDetailIfNeeded();
      } else {
        _markDoNotNeedPollingProcessingStatus();
        _cancelPollingTimeOut();
      }
      await _startPollingCashBackStatusIfNeeded(
          state.transaction?.id, state.transaction?.promotionInfo, status);
      await _stopPollingCashBackStatusIfNeeded(status);
      await _requestReviewRatingPopup(status);
      return;
    }

    if (state is PaymentResultErrorState) {
      showSnackBarError(state.errorUIModel.userMessage ?? CommonStrings.otherGenericErrorMessage);
      return;
    }

    if (state is PaymentResultPosLimitState) {
      dialogEnablePosLimitHandler.delayToHandleDialogEnablePosLimitShowing(
        onClickNegative: handlePaymentFlowComplete,
        onClickPositive: openPosSetupUserGuild,
      );
      return;
    }
  }

  Future<void> _handlePollingGetTransactionDetailIfNeeded() async {
    await Future<void>.delayed(
      const Duration(seconds: PaymentConfig.intervalPollingGetCheckOutDetailInSec),
    );
    if (_needPollingProcessingStatus && paymentResultCubit.state is! PaymentResultLoadingState) {
      _getTransactionDetail(isRefresh: true);
    }
  }

  @override
  void listenNetworkHandler(bool hasInternet) {
    if (hasInternet) {
      // after re-connect, check condition to continue polling if needed
      _handlePollingGetTransactionDetailIfNeeded();
    }
  }

  Future<void> _requestReviewRatingPopup(TransactionStatusModel? status) async {
    if (paymentResultCubit.isFirstTransaction != true || status != TransactionStatusModel.success) {
      return;
    }

    /// Request the user: Review/rating app
    commonLog('Request the user: Review/rating app');
    await showRequestRatingDialogIfNeeded(
      title: EvoStrings.paymentResultRatingReviewPopupTitle,
      content: EvoStrings.paymentResultRatingReviewPopupContent,
    );
  }

  //refer comment https://trustingsocial1.atlassian.net/browse/EMA-6201?focusedCommentId=175844
  Future<void> _startPollingCashBackStatusIfNeeded(String? transactionId,
      PromotionInfoEntity? promotionInfoEntity, TransactionStatusModel? status) async {
    if (!getIt<FeatureToggle>().enableInstantCashbackFeature) {
      return;
    }
    final bool isPaymentWithCashBackVoucher = (promotionInfoEntity?.cashbackAmount ?? 0) > 0;
    if (transactionId != null &&
        isPaymentWithCashBackVoucher &&
        cashBackResultPollingHandler == null &&
        (status == TransactionStatusModel.processing || status == TransactionStatusModel.success)) {
      cashBackResultPollingHandler = getIt<TaskPollingHandler>();
      final DateTime taskCreatedTIme = DateTime.now();
      //save task to storage to poll later if needed
      await cashBackResultPollingHandler?.saveNewTask(
        transactionId,
        PollingTaskType.cashBackResult,
        taskCreatedTIme,
      );
      //create and add task to polling handler
      cashBackPollingTask = cashBackResultPollingHandler?.createPollingTask(
        taskId: transactionId,
        type: PollingTaskType.cashBackResult,
        createdTime: taskCreatedTIme,
      );
      Future<void>.delayed(Duration(seconds: _waitTimeBeforePollingForCashBackResultInSec), () {
        if (mounted &&
            _shouldPollForCashBackResult == true &&
            isTopVisible() &&
            isAppOnForeGround) {
          //only start polling in case of app is in foreground and top visible, and the transaction is not failed
          cashBackResultPollingHandler?.startPollingAllTasks();
        }
      });
    }
  }

  //refer comment https://trustingsocial1.atlassian.net/browse/EMA-6201?focusedCommentId=175844
  Future<void> _stopPollingCashBackStatusIfNeeded(TransactionStatusModel? status) async {
    //to handle the case that has transaction result failure when user is still in the screen,
    //so stop polling for cashback result to avoid unnecessary polling api call
    if (!getIt<FeatureToggle>().enableInstantCashbackFeature) {
      return;
    }
    if (status != TransactionStatusModel.processing && status != TransactionStatusModel.success) {
      //set this flag to handle the case that the polling is not yet started
      _shouldPollForCashBackResult = false;
      //in case the polling started, just stop it and delete task
      cashBackResultPollingHandler?.stopPollingAllTasks();
      final PollingTask? pollingTask = cashBackPollingTask;
      if (pollingTask != null) {
        await cashBackResultPollingHandler?.deleteTask(pollingTask);
      }
    }
  }

  @override
  void didPopNext() {
    super.didPopNext();
    cashBackResultPollingHandler?.startPollingAllTasks();
  }

  @override
  void didPushNext() {
    super.didPushNext();
    cashBackResultPollingHandler?.pausePollingAllTasks();
  }

  @override
  void dispose() {
    cashBackResultPollingHandler?.stopPollingAllTasks();
    _clearPaymentSharedData();
    super.dispose();
  }

  @override
  void onPaused() {
    cashBackResultPollingHandler?.pausePollingAllTasks();
    isAppOnForeGround = false;
    super.onPaused();
  }

  @override
  Future<void> onResumed() async {
    isAppOnForeGround = true;
    if (!getIt<FeatureToggle>().enableInstantCashbackFeature) {
      return;
    }
    if (isTopVisible()) {
      cashBackResultPollingHandler?.startPollingAllTasks();
    }
    super.onResumed();
  }
}
