import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/colors.dart';

import '../../../prepare_for_app_initiation.dart';

class DashSeparatorHorizontal extends StatelessWidget {
  final Color? color;

  DashSeparatorHorizontal({
    super.key,
    this.heightBox,
    this.dashWidth = 1,
    this.dashHeight = 4,
    Color? color,
  }) : color = color ?? getIt.get<CommonColors>().textHint;

  final double? heightBox;
  final double dashWidth;
  final double dashHeight;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final double boxHeight = heightBox ?? constraints.constrainHeight();
        final double width = dashWidth;
        final double height = dashHeight;
        final int dashCount = (boxHeight / (2 * height)).floor();
        return Flex(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          direction: Axis.vertical,
          children: List<Widget>.generate(dashCount, (_) {
            return SizedBox(
              width: width,
              height: height,
              child: DecoratedBox(
                decoration: BoxDecoration(color: color ?? getIt.get<CommonColors>().textHint),
              ),
            );
          }),
        );
      },
    );
  }
}
