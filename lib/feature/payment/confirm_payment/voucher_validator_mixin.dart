import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/response/invalid_voucher_entity.dart';

mixin VoucherValidatorMixin {
  ErrorUIModel? getInvalidVoucherErrorModel(List<InvalidVoucherEntity?>? invalidVouchers) {
    if (invalidVouchers == null || invalidVouchers.isEmpty) {
      return null;
    }

    final InvalidVoucherEntity? invalidVoucherEntity = invalidVouchers.first;
    if (invalidVoucherEntity == null) {
      return null;
    }

    return ErrorUIModel(
      verdict: invalidVoucherEntity.rejectedVerdict,
      userMessage: invalidVoucherEntity.userMessage,
    );
  }
}
