import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_component/ui_component.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/home_repo.dart';
import '../../../../data/response/reminder_entity.dart';
import '../../utils/mock_file/mock_home_file_name.dart';

class ReminderCubit extends CommonCubit<UiComponentState> {
  final HomeRepo homeRepo;

  ReminderCubit({required this.homeRepo}) : super(UiComponentLoading());

  Future<void> getLatestReminder() async {
    emit(UiComponentLoading());
    final ReminderEntity reminderEntity = await homeRepo.getLatestReminder(
        mockConfig: MockConfig(enable: true, fileName: getLatestReminderMockFileName()));

    if (reminderEntity.statusCode == CommonHttpClient.SUCCESS) {
      emit(UiComponentDataLoaded<ReminderEntity>(data: reminderEntity));
    } else {
      emit(UiComponentFailed(ErrorUIModel.fromEntity(reminderEntity)));
    }
  }
}
