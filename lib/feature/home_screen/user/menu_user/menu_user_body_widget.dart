import 'package:flutter/material.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../feature_toggle.dart';
import 'menu_user_item_widget.dart';
import 'menu_user_widget.dart';

class MenuUserBody extends StatelessWidget {
  final void Function(MenuUserType type)? onTapMenuUser;

  const MenuUserBody({
    super.key,
    this.onTapMenuUser,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        const SizedBox(height: 4),
        Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: <Widget>[
          _createIconMenuUserScanQr(),
          _itemIconMenuUser(EvoStrings.menuUserReward, EvoImages.icGift, MenuUserType.reward),
          _itemIconMenuUser(EvoStrings.menuUserGuideCardUsage, EvoImages.icGuideSearch, MenuUserType.cardUsageGuide),
          _itemIconMenuUser(EvoStrings.menuUserQuestion, EvoImages.icFAQ, MenuUserType.question),
        ]),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _createIconMenuUserScanQr() {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();
    if (featureToggle.enableEmiFeatureVersion == EmiFeatureVersion.version_1) {
      return _itemIconMenuUser(
        EvoStrings.menuUserEmiPayment,
        EvoImages.icMenuUserEmiPayment,
        MenuUserType.scanQr,
      );
    }

    return _itemIconMenuUser(
      EvoStrings.menuUserScanner,
      EvoImages.icScanner,
      MenuUserType.scanQr,
    );
  }

  Widget _itemIconMenuUser(
    String title,
    String pathIcon,
    MenuUserType type, {
    bool hasDotRed = false,
  }) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onTapMenuUser?.call(type),
          child: MenuUserItem(
            title: title,
            pathIcon: pathIcon,
            hasDotRed: hasDotRed,
          ),
        ),
      ),
    );
  }
}
