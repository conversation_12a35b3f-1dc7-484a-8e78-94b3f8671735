import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../home_widgets/icon_with_red_dot_widget.dart';

class MenuUserItem extends StatelessWidget {
  static const double defaultIconSize = 48;

  final String title;
  final String pathIcon;
  final bool hasDotRed;
  final double iconSize;

  const MenuUserItem({
    required this.title,
    required this.pathIcon,
    super.key,
    this.hasDotRed = false,
    this.iconSize = defaultIconSize,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        IconWithDotRedWidget(
          pathIcon,
          color: evoColors.textActive,
          iconSize: iconSize,
          hasBackground: false,
          hasDotRed: hasDotRed,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: evoTextStyles.bodyXSmall(),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        )
      ],
    );
  }
}
