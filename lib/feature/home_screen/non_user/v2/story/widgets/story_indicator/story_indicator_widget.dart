import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';

import '../../model/story_view_model.dart';
import '../../story_config.dart';
import '../story_progress_bar_widget.dart';
import 'story_indicator_cubit.dart';
import 'story_indicator_state.dart';

class StoryIndicatorController {
  VoidCallback? play;
  VoidCallback? pause;
  VoidCallback? next;
  VoidCallback? previous;
  VoidCallback? reset;
}

class StoryIndicatorWidget extends StatefulWidget {
  final List<StoryViewModel> stories;
  final Color? backgroundColor;
  final Color? activeColor;
  final StoryIndicatorController? controller;
  final VoidCallback? animateToNextPage;

  const StoryIndicatorWidget({
    required this.stories,
    this.backgroundColor,
    this.activeColor,
    this.controller,
    this.animateToNextPage,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => StoryIndicatorWidgetState();
}

@visibleForTesting
class StoryIndicatorWidgetState extends State<StoryIndicatorWidget> with TickerProviderStateMixin {
  late AnimationController _storyIndicatorController;
  final StoryIndicatorCubit _storyIndicatorCubit = StoryIndicatorCubit();
  final List<StoryViewModel> _stories = <StoryViewModel>[];

  @override
  void initState() {
    super.initState();
    _stories.addAll(_storyIndicatorCubit.initStoryView(widget.stories));
    _handleInitAnimation();
    _initController();
  }

  @override
  void dispose() {
    _storyIndicatorController.removeStatusListener(_handleAnimationStatus);
    _storyIndicatorController.dispose();
    super.dispose();
  }

  @override
  void setState(VoidCallback fn) {
    if (!mounted) {
      return;
    }
    super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<StoryIndicatorCubit>(
      create: (_) => _storyIndicatorCubit,
      child: BlocConsumer<StoryIndicatorCubit, StoryIndicatorState>(
        listener: (_, StoryIndicatorState state) {
          _handleListenerState(state);
        },
        builder: (_, StoryIndicatorState state) {
          return Row(children: _buildItemPage(state));
        },
      ),
    );
  }

  List<Widget> _buildItemPage(StoryIndicatorState state) {
    final List<Widget> listWidget = <Widget>[];

    _stories.forEachIndexed(
      (int index, StoryViewModel? model) {
        final int lastIndex = _stories.length - 1;
        final double padding = index == lastIndex ? 0 : StoryConfig.progressBarPadding;
        final double progressValue = handleProgressValue(model);
        final Widget item = Expanded(
          child: Padding(
            padding: EdgeInsets.only(right: padding),
            child: StoryProgressBarWidget(
              value: progressValue,
              backgroundColor: widget.backgroundColor,
              activeColor: widget.activeColor,
            ),
          ),
        );
        listWidget.add(item);
      },
    );
    return listWidget;
  }

  @visibleForTesting
  double handleProgressValue(StoryViewModel? model) {
    if (model?.type == StoryViewType.isPlaying) {
      final double currentValue = _storyIndicatorController.value;
      return currentValue;
    }

    final double valuePageNotShow = model?.type == StoryViewType.isShown ? 1 : 0;
    return valuePageNotShow;
  }

  void _handleInitAnimation() {
    _storyIndicatorController = AnimationController(
        duration: Duration(milliseconds: _stories.first.durationInMs), vsync: this);
    _storyIndicatorController.addStatusListener(_handleAnimationStatus);
    _storyIndicatorController.addListener(() {
      setState(() {});
    });
  }

  void _initController() {
    widget.controller?.play = _playController;
    widget.controller?.pause = _pauseController;
    widget.controller?.next = _nextController;
    widget.controller?.previous = _previousController;
    widget.controller?.reset = _resetController;
  }

  void _playController() {
    _storyIndicatorController.forward();
  }

  void _pauseController() {
    _storyIndicatorController.stop(canceled: false);
  }

  void _nextController() {
    _storyIndicatorCubit.nextStoryView();
  }

  void _previousController() {
    _storyIndicatorCubit.previousStoryView();
  }

  void _resetController() {
    _storyIndicatorController.reset();
    _storyIndicatorController.forward();
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      widget.animateToNextPage?.call();
    }
  }

  void _handleListenerState(StoryIndicatorState state) {
    if (state is IndicatorNextState) {
      _goNextStoryView(state);
      _handleUpdateDataStoryView(state.storyItems);
      return;
    }

    if (state is IndicatorCompleteState) {
      _completeStoryView();
    }
  }

  void _goNextStoryView(IndicatorNextState state) {
    _storyIndicatorController.stop();
    _replayStoryView(currentStoryIndex: state.currentStoryIndex);
  }

  void _completeStoryView() {
    _storyIndicatorController.stop();
    _replayStoryView();
  }

  void _replayStoryView({int? currentStoryIndex}) {
    _storyIndicatorController.reset();
    _changeDurationAnimation();
    _playController();
  }

  void _changeDurationAnimation() {
    final Duration duration = Duration(milliseconds: _storyIndicatorCubit.currentDurationInMs);
    _storyIndicatorController.duration = duration;
  }

  void _handleUpdateDataStoryView(List<StoryViewModel> stories) {
    _stories.clear();
    _stories.addAll(stories);
  }
}
