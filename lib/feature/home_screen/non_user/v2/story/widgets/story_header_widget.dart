import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../../../../prepare_for_app_initiation.dart';
import '../../../../../../resources/resources.dart';
import '../../../../../../util/evo_action_handler.dart';
import '../../../../../dop_native/widgets/dop_entry_point_widget.dart';
import '../../../../../feature_toggle.dart';

class StoryHeaderWidget extends StatelessWidget {
  const StoryHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 32),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            _buildIconEvo(),
            const Spacer(),
            _buildLoginButton(),
            _buildDOPNativeEntryPoint(),
          ],
        ),
      ),
    );
  }

  Widget _buildIconEvo() {
    return evoImageProvider.asset(
      EvoImages.icEvo,
      width: 69,
      fit: BoxFit.fitWidth,
    );
  }

  Widget _buildLoginButton() {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();
    final ButtonStyle btnStyle = featureToggle.enableDOPNativeFeature
        ? evoButtonStyles
        .tertiary(ButtonSize.medium)
        .copyWith(backgroundColor: WidgetStateProperty.all(Colors.transparent))
        : evoButtonStyles.primary(ButtonSize.medium);

    return CommonButton(
      onPressed: () {
        EvoActionHandler().openAuthenticationScreen();
      },
      style: btnStyle,
      child: const Text(EvoStrings.storyCtaLogin),
    );
  }

  Widget _buildDOPNativeEntryPoint() {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();
    if (!featureToggle.enableDOPNativeFeature) {
      return const SizedBox.shrink();
    }
    return const DopNativeEntryPoint(
      entryPoint: Screen.nonUserHomeScreen,
      padding: EdgeInsets.only(left: 8.0),
    );
  }
}
