import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer.dart';

class SkeletonCardBenefitsWidget extends StatelessWidget {
  final double heightCard;

  const SkeletonCardBenefitsWidget({super.key, this.heightCard = 127});

  @override
  Widget build(BuildContext context) {
    const SkeletonContainer skeletonHeight48 = SkeletonContainer(height: 48);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          SkeletonContainer(height: heightCard),
          const SizedBox(height: 20),
          const Row(
            children: <Widget>[
              Flexible(child: skeletonHeight48),
              SizedBox(width: 8),
              Flexible(child: skeletonHeight48),
            ],
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}
