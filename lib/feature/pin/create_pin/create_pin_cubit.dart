import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/response/reset_pin_entity.dart';
import '../../../data/response/sign_in_otp_entity.dart';
import '../../../resources/ui_strings.dart';
import '../../../util/functions.dart';
import '../../../util/mock_file_name_utils/mock_authentication_file_name.dart';
import '../../../util/mock_file_name_utils/mock_user_file_name.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../biometric/utils/biometrics_authenticate.dart';
import '../pin_state.dart';

class CreatePinCubit extends CommonCubit<PinState> {
  CreatePinCubit(
    this.userRepo,
    this.authenticationRepo,
    this.biometricsAuthenticate,
    this._storageHelper,
  ) : super(PinNotFullState());

  final UserRepo userRepo;
  final AuthenticationRepo authenticationRepo;
  final BiometricsAuthenticate biometricsAuthenticate;
  final EvoLocalStorageHelper _storageHelper;

  void validateLengthPin(String pin, String confirmPin) {
    final bool resultPin = evoUtilFunction.validateMaxLengthPin(pin);
    final bool resultConfirmPin = evoUtilFunction.validateMaxLengthPin(confirmPin);
    emit(resultPin && resultConfirmPin ? PinFullState() : PinNotFullState());
  }

  Future<void> comparePinCode({
    required String pin,
    required String confirmPin,
  }) async {
    if (pin == confirmPin) {
      emit(PinValidatedState());
    } else {
      emit(
        PinErrorState(
          ErrorUIModel(
            verdict: ResetPinEntity.verdictInvalidPin,
            userMessage: EvoStrings.errorConfirmPin,
          ),
        ),
      );
    }
  }

  Future<void> createPinCode({
    required String pinCode,
    required String phoneNumber,
    String? sessionToken,
  }) async {
    emit(PinLoadingState());
    final SignInOtpEntity signInOtpEntity = await userRepo.createPin(
      pin: pinCode,
      sessionToken: sessionToken,
      mockConfig: MockConfig(enable: false, fileName: createPinMockFileName()),
    );

    if (signInOtpEntity.statusCode == CommonHttpClient.SUCCESS) {
      await _storageHelper.setUserPhoneNumber(phoneNumber);
      final bool isHasBiometric = await isDeviceSupportBiometrics();
      if (isHasBiometric) {
        emit(RequestBiometricState());
      } else {
        emit(PinLoadedState(pinCode: pinCode));
      }
    } else {
      emit(PinErrorState(ErrorUIModel.fromEntity(signInOtpEntity)));
    }
  }

  Future<bool> isDeviceSupportBiometrics() async {
    return biometricsAuthenticate.isDeviceSupportBiometrics();
  }

  Future<void> resetPin(
    String pinCode,
    String phoneNumber,
    String? sessionToken,
  ) async {
    emit(PinLoadingState());
    final ResetPinEntity entity = await authenticationRepo.requestResetPin(
      ResetPinType.changePin,
      pin: pinCode,
      phoneNumber: phoneNumber,
      sessionToken: sessionToken,
      mockConfig: MockConfig(enable: false, fileName: getRequestResetPinMockFileName()),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(ResetPinSuccessState());
    } else {
      emit(PinErrorState(ErrorUIModel.fromEntity(entity)));
    }
  }
}
