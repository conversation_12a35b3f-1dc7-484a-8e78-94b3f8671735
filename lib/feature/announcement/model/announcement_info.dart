import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';

enum AnnouncementStatus {
  hasUnreadItems,
  noUnreadItems,
}

class AnnouncementInfo {
  ValueNotifier<AnnouncementStatus> statusNotifier =
      ValueNotifier<AnnouncementStatus>(AnnouncementStatus.noUnreadItems);

  /// show/hide red-dot on Announcement icon which indicate there are unread items
  /// reference: [_itemNotificationButton] method in [_HomeAppBarState] class
  void updateStatus(AnnouncementStatus newStatus) {
    if (statusNotifier.value == newStatus) {
      commonLog('Ignore update because newValue is same value with current data');
      return;
    }
    statusNotifier.value = newStatus;
  }
}
