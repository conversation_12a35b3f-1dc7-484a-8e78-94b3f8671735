import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/repository/ekyc_repo.dart';
import '../../../../data/response/ekyc_face_matching_result_entity.dart';
import '../../../../data/response/sign_in_otp_entity.dart';
import '../../../../util/functions.dart';
import '../../ekyc_bridge/ekyc_bridge.dart';
import '../mock_file/mock_face_otp_file_name.dart';

part 'face_otp_matching_process_state.dart';

class FaceOtpMatchingProcessCubit extends CommonCubit<FaceOtpMatchingProcessState> {
  final EKYCRepo ekycRepo;
  final EkycBridge ekycBridge;
  final AuthenticationRepo authenticationRepo;

  FaceOtpMatchingProcessCubit({
    required this.ekycRepo,
    required this.ekycBridge,
    required this.authenticationRepo,
  }) : super(FaceOtpMatchingProcessInit());

  Future<void> faceMatchingForLinkCard({
    required String selfieImageId,
  }) async {
    final String? sessionToken = ekycBridge.getSession()?.sessionToken;

    final EkycFaceMatchingResultEntity entity = await ekycRepo.faceOtp(
      selfieImageId: selfieImageId,
      sessionToken: sessionToken,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockFaceOtpFileName(MockFaceOtpFileName.faceOTPMatchingForLinkCard),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS &&
        entity.verdict == EkycFaceMatchingResultEntity.verdictSuccess) {
      emit(FaceOtpMatchingSuccess(entity));
    } else if (isLimitedExceeded(
        remainAttempt: entity.remainAttempt,
        statusCode: entity.statusCode,
        verdict: entity.verdict)) {
      emit(FaceOtpMatchingLimitExceed(ErrorUIModel.fromEntity(entity)));
    } else {
      emit(FaceOtpMatchingError(ErrorUIModel.fromEntity(entity)));
    }
  }

  Future<void> faceMatchingForSignIn({
    required String selfieImageId,
  }) async {
    final String? sessionToken = ekycBridge.getSession()?.sessionToken;

    final SignInOtpEntity entity = await authenticationRepo.login(
      TypeLogin.faceOTP,
      selfieImageId: selfieImageId,
      sessionToken: sessionToken,
      facialVerificationVersion: evoUtilFunction.getFacialVerificationVersion(),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockFaceOtpFileName(MockFaceOtpFileName.faceOTPMatchingForSignIn),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS &&
        entity.verdict == EkycFaceMatchingResultEntity.verdictSuccess) {
      evoUtilFunction.updateProcessUserStatus(entity.status);
      emit(FaceOtpMatchingSuccess(entity));
    } else if (isLimitedExceeded(
        remainAttempt: entity.ekycCredential?.remainAttempt,
        statusCode: entity.statusCode,
        verdict: entity.verdict)) {
      emit(FaceOtpMatchingLimitExceed(ErrorUIModel.fromEntity(entity)));
    } else {
      emit(FaceOtpMatchingError(ErrorUIModel.fromEntity(entity)));
    }
  }

  @visibleForTesting
  bool isLimitedExceeded({
    required int? remainAttempt,
    required int? statusCode,
    required String? verdict,
  }) {
    return remainAttempt == 0 ||
        (statusCode == CommonHttpClient.LIMIT_EXCEEDED &&
            verdict == EkycFaceMatchingResultEntity.verdictFaceOtpExceeded);
  }
}
