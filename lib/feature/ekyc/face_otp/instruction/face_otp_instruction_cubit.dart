import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/resources/ui_strings.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../data/response/ekyc_info_entity.dart';
import '../../ekyc_bridge/ekyc_bridge.dart';
import '../../model/ekyc_result_model.dart';

part 'face_otp_instruction_state.dart';

class FaceOTPInstructionCubit extends CommonCubit<FaceOTPInstructionState> {
  @visibleForTesting
  bool userStartedFaceOTP = false;

  final EkycBridge ekycBridge;

  FaceOTPInstructionCubit(this.ekycBridge) : super(FaceOTPInstructionInitialState());

  Future<void> initTrustVisionSDK() async {
    if (userStartedFaceOTP) {
      return;
    }

    userStartedFaceOTP = true;
    emit(FaceOTPInstructionLoadingState());
    final EKYCSessionEntity? eKYCSessionEntity = ekycBridge.getSession();
    final TVSDKResult initResult = await ekycBridge.initEkyc(
      accessKeyId: eKYCSessionEntity?.accessKey,
      accessKeySecret: eKYCSessionEntity?.secretKey,
      endpoint: eKYCSessionEntity?.eKYCClientUrl,
      xRequestId: eKYCSessionEntity?.xRequestId,
      xLenderRequestId: eKYCSessionEntity?.xLenderRequestId,
    );

    userStartedFaceOTP = false;
    if (initResult.isSuccess) {
      commonLog('init TrustVisionSDK succeed');
      emit(FaceOTPInstructionStartNow());
      return;
    }

    commonLog('init TrustVisionSDK with eKYC session failed with reason: ${initResult.failReason}');
    emit(FaceOTPInstructionFailState(
        ErrorUIModel(userMessage: CommonStrings.otherGenericErrorMessage)));
  }
}
