import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../model/evo_dialog_id.dart';
import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_dialog_helper.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../widget/evo_appbar.dart';
import '../../../../widget/evo_appbar_leading_button.dart';
import '../../../camera_permission/camera_permission_cubit.dart';
import '../../../mock_test/mock_test_feature_type.dart';
import '../../ekyc_bridge/ekyc_bridge.dart';
import '../../ekyc_error_screen/ekyc_error_screen.dart';
import '../../ekyc_flow_failed_reason.dart';
import '../../ekyc_flow_mixin.dart';
import '../../model/ekyc_for_flow_type.dart';
import '../../model/ekyc_result_model.dart';
import '../face_matching/face_otp_matching_process_screen.dart';
import '../selfie_capture/selfie_capturing_cubit.dart';
import 'face_otp_instruction_cubit.dart';
import 'face_otp_screen_mock_testing.dart';
import 'widgets/face_otp_instruction_content_widget.dart';

class FaceOTPInstructionScreenArg extends PageBaseArg {
  final EkycFlowType flowType;
  final bool forceCreateNewEKYCSession;

  FaceOTPInstructionScreenArg({
    required this.flowType,
    required this.forceCreateNewEKYCSession,
  });
}

class FaceOTPInstructionScreen extends PageBase {
  static void pushReplacementNamed({
    required EkycFlowType flowType,
    required bool forceCreateNewEKYCSession,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.faceOTPInstructionScreen.name,
      extra: FaceOTPInstructionScreenArg(
        flowType: flowType,
        forceCreateNewEKYCSession: forceCreateNewEKYCSession,
      ),
    );
  }

  final FaceOTPInstructionScreenArg arg;

  const FaceOTPInstructionScreen({required this.arg, super.key});

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.faceOTPInstructionScreen.routeName);

  @override
  State<FaceOTPInstructionScreen> createState() => _FaceOTPInstructionScreenState();
}

class _FaceOTPInstructionScreenState extends EvoPageStateBase<FaceOTPInstructionScreen>
    with EkycFlowMixin, FaceOtpInstructionScreenMockTesting {
  final FaceOTPInstructionCubit _faceOTPInstructionCubit =
      FaceOTPInstructionCubit(getIt.get<EkycBridge>());
  final CameraPermissionCubit _permissionCubit = CameraPermissionCubit();

  final SelfieCapturingCubit _selfieCapturingCubit =
      SelfieCapturingCubit(ekycBridge: getIt.get<EkycBridge>());

  @override
  MockTestFeatureType get mockTestFeatureType => switch (widget.arg.flowType) {
        EkycFlowType.linkCard => MockTestFeatureType.manualLinkCard,
        EkycFlowType.faceOtpSignIn => MockTestFeatureType.signIn,
      };

  @override
  void dispose() {
    _permissionCubit.close();
    _faceOTPInstructionCubit.close();
    _selfieCapturingCubit.close();
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return MultiBlocProvider(
      providers: <BlocProvider<dynamic>>[
        BlocProvider<FaceOTPInstructionCubit>(
          create: (_) => _faceOTPInstructionCubit,
        ),
        BlocProvider<CameraPermissionCubit>(
          create: (_) => _permissionCubit,
        ),
        BlocProvider<SelfieCapturingCubit>(
          create: (_) => _selfieCapturingCubit,
        ),
      ],
      child: MultiBlocListener(
        listeners: <BlocListener<dynamic, dynamic>>[
          BlocListener<FaceOTPInstructionCubit, FaceOTPInstructionState>(
            listener: _listenNoticeFaceOTPScreen,
          ),
          BlocListener<CameraPermissionCubit, CameraPermissionState>(
            listener: _listenPermissionState,
          ),
          BlocListener<SelfieCapturingCubit, SelfieCapturingState>(
            listener: _listenSelfieCapturingCubit,
          ),
        ],
        child: BlocBuilder<FaceOTPInstructionCubit, FaceOTPInstructionState>(
          builder: (_, FaceOTPInstructionState state) {
            return PopScope(
              canPop: false,
              onPopInvokedWithResult: (bool didPop, _) {
                if (didPop) {
                  return;
                }

                _backToEntryPoint(reason: EkycFlowFailedReason.userCancelled);
              },
              child: Scaffold(
                backgroundColor: evoColors.background,
                appBar: EvoAppBar(
                  leading: EvoAppBarLeadingButton(
                    onPressed: () {
                      _backToEntryPoint(reason: EkycFlowFailedReason.userCancelled);
                    },
                  ),
                ),
                body: SafeArea(
                  child: _buildInstructionContent(state),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  String get instructionDescription {
    return switch (widget.arg.flowType) {
      EkycFlowType.linkCard => EvoStrings.verifyFaceOtpLinkCardDescription,
      EkycFlowType.faceOtpSignIn => EvoStrings.verifyFaceOtpSignInDescription,
    };
  }

  Widget _buildInstructionContent(FaceOTPInstructionState state) {
    final VoidCallback? onStartCallBack =
        state is FaceOTPInstructionLoadingState ? null : _startFaceOTPCapturing;
    return FaceOtpInstructionContentWidget(
      onPressStart: onStartCallBack,
      description: instructionDescription,
    );
  }

  void _listenNoticeFaceOTPScreen(BuildContext context, FaceOTPInstructionState state) {
    if (state is FaceOTPInstructionLoadingState) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    if (state is FaceOTPInstructionFailState) {
      handleEvoApiError(state.errorUIModel);
      _backToEntryPoint(error: state.errorUIModel);
      return;
    }

    if (state is FaceOTPInstructionStartNow) {
      _selfieCapturingCubit.startSelfieCapturing();
      return;
    }
  }

  void _backToEntryPoint({ErrorUIModel? error, EkycFlowFailedReason? reason}) {
    onEkycFailed(
      context: context,
      reason: reason ?? EkycFlowFailedReason.unknown,
      userMessage: error?.userMessage,
    );
  }

  Future<void> _listenPermissionState(BuildContext context, CameraPermissionState state) async {
    if (state is CameraPermissionGrantedState) {
      _faceOTPInstructionCubit.initTrustVisionSDK();
      return;
    }

    if (state is CameraPermissionDeniedState) {
      await _showAppSettingDialog();
      return;
    }
  }

  Future<void> _showAppSettingDialog() {
    return EvoDialogHelper().showDialogBottomSheet(
      dialogId: EvoDialogId.appSettingCameraBottomSheet,
      isDismissible: false,
      title: EvoStrings.cameraPermissionTitle,
      content: EvoStrings.cameraFaceOtpPermissionDescription,
      textPositive: EvoStrings.settingTitle,
      textNegative: EvoStrings.ignoreTitle,
      onClickPositive: () {
        _permissionCubit.resetState();

        /// dismiss popup
        navigatorContext?.pop();

        openAppSettings();
      },
      onClickNegative: () {
        _permissionCubit.resetState();

        /// dismiss popup
        navigatorContext?.pop();
      },
    );
  }

  void _listenSelfieCapturingCubit(BuildContext context, SelfieCapturingState state) {
    if (state is SelfieCapturingSuccessState) {
      final String? imageId = state.imageId;
      commonLog('imageId: $imageId');
      FaceOtpMatchingProcessScreen.pushReplacementNamed(
        selfieImageId: imageId,
        flowType: widget.arg.flowType,
      );
      return;
    }

    if (state is SelfieCapturingFailedState) {
      _handleSelfieCapturingFail(state.reason);
      return;
    }
  }

  void _handleSelfieCapturingFail(TVSDKFailReason? reason) {
    if (reason == TVSDKFailReason.userCancelled) {
      // do nothing if user cancel capturing by back button
      // user can press CTA button to capture again
      // ref: https://trustingsocial1.atlassian.net/browse/EMA-4835
      return;
    }

    EkycErrorScreen.pushNamed(
      errorReason: reason,
      onActionButtonTap: (BuildContext context) {
        //close error screen
        navigatorContext?.pop();

        handleRetrySelfieCapturingIfEkycHasError(
          context: context,
          reason: reason,
          cubit: _selfieCapturingCubit,
        );
      },
    );
  }

  void _startFaceOTPCapturing() {
    // handle Mock Test Face OTP
    if (isEnableMockFaceOtpTestFlow) {
      requestStoragePermissionForMockTest();
      return;
    }
    // the SDK already initial and start but cancel it previously (back button)
    // now we can start it again without request permission or init SDK
    if (_faceOTPInstructionCubit.state is FaceOTPInstructionStartNow) {
      _selfieCapturingCubit.startSelfieCapturing();
      return;
    }
    // request permission to init SDK
    _permissionCubit.requestPermission();
  }

  @override
  void onIgnoreMockTest() {
    _startFaceOTPCapturing();
  }

  @override
  void onMockDataIsReady() {
    /// get image id from mock data
    FaceOtpMatchingProcessScreen.pushReplacementNamed(
      selfieImageId: mockFaceOTPImageId,
      flowType: widget.arg.flowType,
    );
  }
}
