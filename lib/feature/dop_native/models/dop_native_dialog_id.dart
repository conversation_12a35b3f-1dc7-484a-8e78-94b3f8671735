enum DOPNativeDialogId {
  /// Bottom sheet

  /// Dialog
  inputPhoneNumberDialog('dop_native_input_phone_number_dialog'),
  confirmCloseDOPNativeFlowDialog('dop_native_confirm_close_dop_native_flow_dialog'),
  dopNativeAddressDialog('dop_native_address_dialog'),
  dopNativeEmploymentDialog('dop_native_employment_dialog'),
  dopNativeLinkCardDialog('dop_native_link_card_dialog'),
  setupPosLimitDialog('dop_native_set_pos_limit_dialog'),
  dopRetryNFCVerificationDialog('dop_retry_nfc_verification_dialog'),
  dopNFCVerificationInvalidTokenDialog('dop_nfc_verification_invalid_token_dialog'),
  dopNFCVerificationNetworkErrorDialog('dop_nfc_verification_network_error_dialog'),
  dopNFCVerificationEnableNfcDialog('dop_nfc_verification_enable_nfc_dialog'),
  dopNFCVerificationErrorWrongInfoDialog('dop_nfc_verification_error_wrong_info_dialog'),
  dopNFCVerificationErrorUnknownDialog('dop_nfc_verification_error_unknown_dialog'),
  dopNFCVerificationErrorTimeoutDialog('dop_nfc_verification_error_timeout_dialog');

  const DOPNativeDialogId(this.id);

  final String id;
}
