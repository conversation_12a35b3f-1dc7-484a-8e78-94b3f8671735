import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';

import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../widget/animation/lottie_animation_widget.dart';
import '../../resources/dop_animations.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../widgets/text_field/dop_native_text_field_widget.dart';
import 'cubit/metadata_popup_cubit.dart';
import 'cubit/metadata_popup_state.dart';
import 'metadata_popup_header_widget.dart';

class DOPNativeMetadataPopUp<T> extends StatefulWidget {
  const DOPNativeMetadataPopUp({
    required this.itemBuilder,
    required this.data,
    required this.height,
    required this.title,
    required this.onBack,
    required this.filterBy,
    this.loading = false,
    super.key,
  });

  final List<T> data;
  final double height;

  final void Function()? onBack;
  final Widget Function(BuildContext context, T item) itemBuilder;
  final String title;
  final String Function(T data) filterBy;
  final bool loading;

  @override
  State<DOPNativeMetadataPopUp<T>> createState() => _DOPNativeMetadataPopUpState<T>();
}

class _DOPNativeMetadataPopUpState<T> extends State<DOPNativeMetadataPopUp<T>> {
  final TextEditingController _inputController = TextEditingController();
  final Duration animationDuration = const Duration(milliseconds: 200);
  late final MetadataPopupCubit<T> _metadataPopUpCubit;

  @override
  void initState() {
    super.initState();
    _metadataPopUpCubit = MetadataPopupCubit<T>(
      filterBy: widget.filterBy,
      initialData: widget.data,
    );
    _inputController.addListener(_onQueryChanged);
  }

  @override
  void dispose() {
    _inputController.removeListener(_onQueryChanged);
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant DOPNativeMetadataPopUp<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data) {
      EvoUiUtils().hideKeyboard();
      _metadataPopUpCubit.updateData(widget.data);
      _inputController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<MetadataPopupCubit<T>>(
      create: (BuildContext context) => _metadataPopUpCubit,
      child: Center(
        child: SingleChildScrollView(
          physics: const NeverScrollableScrollPhysics(),
          child: Dialog(
            insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            backgroundColor: Colors.transparent,
            shadowColor: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 20.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  MetadataPopUpHeader(
                    title: widget.title,
                    onBack: widget.onBack,
                  ),
                  const SizedBox(height: 17),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: _buildSearchInput(),
                  ),
                  Container(
                    constraints: BoxConstraints(
                      maxHeight: widget.height - MediaQuery.of(context).viewInsets.bottom,
                    ),
                    child: LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return AnimatedCrossFade(
                          firstChild: ConstrainedBox(
                            constraints: BoxConstraints.tight(
                              constraints.biggest,
                            ),
                            child: _buildLoadingWidget(),
                          ),
                          secondChild: ConstrainedBox(
                            constraints: BoxConstraints.tight(
                              constraints.biggest,
                            ),
                            child: _buildPopUpBody(),
                          ),
                          crossFadeState:
                              widget.loading ? CrossFadeState.showFirst : CrossFadeState.showSecond,
                          duration: animationDuration,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: LottieAnimationWidget(
        DOPAnimation.dopLoadingAnimation,
        size: 34,
      ),
    );
  }

  Widget _buildSearchInput() {
    return DOPNativeTextField(
      controller: _inputController,
      suffixIcon: const Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: Icon(Icons.search),
      ),
      hintText: DOPNativeStrings.dopNativeSearchInputHint,
    );
  }

  Widget _buildPopUpBody() {
    return BlocBuilder<MetadataPopupCubit<T>, MetadataPopUpState>(
      builder: (BuildContext context, MetadataPopUpState state) {
        final List<T> data = state is MetadataPopUpStateLoaded<T> ? state.data : <T>[];
        return Align(
          alignment: Alignment.topCenter,
          child: AnimatedSwitcher(
            duration: animationDuration,
            child: ListView.separated(
              key: ValueKey<String>('${data.hashCode}'),
              itemCount: data.length,
              itemBuilder: (BuildContext context, int index) {
                final T item = data[index];
                return widget.itemBuilder(context, item);
              },
              separatorBuilder: (BuildContext context, int index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Divider(
                    thickness: 1,
                    height: 0,
                    color: dopNativeColors.divider,
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _onQueryChanged() {
    _metadataPopUpCubit.onQueryChanged(_inputController.text);
  }
}
