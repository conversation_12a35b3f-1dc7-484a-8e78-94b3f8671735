import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../util/dop_functions.dart';
import 'debouncer.dart';
import 'metadata_popup_state.dart';

class MetadataPopupCubit<T> extends CommonCubit<MetadataPopUpState> {
  MetadataPopupCubit({
    required this.filterBy,
    required List<T> initialData,
  })  : _initialData = initialData,
        super(
          MetadataPopUpStateLoaded<T>(
            data: initialData,
          ),
        );

  List<T> _initialData = <T>[];
  final String Function(T data) filterBy;
  static const int _delayTextInputInMilliseconds = 120;
  @visibleForTesting
  static const Duration debouncingDuration = Duration(milliseconds: _delayTextInputInMilliseconds);
  final Debouncer _debouncer = Debouncer(
    duration: debouncingDuration,
  );

  void updateData(List<T> initialData) {
    _initialData = List<T>.of(initialData);
    emit(
      MetadataPopUpStateLoaded<T>(
        data: _initialData,
      ),
    );
  }

  void onQueryChanged(String query) {
    _debouncer.run(() => _mappingData(query));
  }

  void _mappingData(String query) {
    final List<T> filtered = dopUtilFunction.queryDiacriticsFilter(
      query: query,
      data: _initialData,
      filterBy: filterBy,
    );
    emit(
      MetadataPopUpStateLoaded<T>(
        data: filtered,
      ),
    );
  }
}
