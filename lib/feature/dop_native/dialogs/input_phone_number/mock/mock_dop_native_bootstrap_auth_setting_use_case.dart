enum MockDOPNativeBootstrapAuthSetting {
  getBootstrapAuthTypeOTP('dop_native_get_bootstrap_auth_otp.json'),
  getBootstrapAfterInputtingPhoneNumber(
      'dop_native_get_bootstrap_after_inputting_phone_number.json'),
  getBootstrapAuthTypeESignOTP('dop_native_get_bootstrap_auth_esign_otp.json'),
  getBootstrapAuthTypeIDCardAuth('dop_native_get_bootstrap_auth_id_card_auth.json');

  final String value;

  const MockDOPNativeBootstrapAuthSetting(this.value);
}

String getMockDOPNativeBootstrapAuthSetting(MockDOPNativeBootstrapAuthSetting mockCase) {
  return mockCase.value;
}
