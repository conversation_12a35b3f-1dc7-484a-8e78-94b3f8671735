import '../../../resources/dop_native_ui_strings.dart';

class DOPNativeCifConfirmUIArgs {
  final String title;
  final String? subtitle;
  final List<String> notes;
  final bool showOpenCardCTA;
  final bool showConfirmInfoUpdated;
  final bool showViewNearestBranches;

  DOPNativeCifConfirmUIArgs({
    required this.title,
    required this.notes,
    this.subtitle,
    this.showOpenCardCTA = false,
    this.showConfirmInfoUpdated = false,
    this.showViewNearestBranches = false,
  });

  /// With CIF Info
  /// Refer to: https://www.figma.com/file/j8MS3RYzC28OPJZ8vDFU7O/EVO-App-Design?type=design&node-id=25153-55771&mode=design&t=ey1R87ayzi51bbKL-4
  factory DOPNativeCifConfirmUIArgs.difNationIdWithCifInfo() => DOPNativeCifConfirmUIArgs(
        title: DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle,
        notes: <String>[
          DOPNativeStrings.cifConfirmDifNationIdNote,
          DOPNativeStrings.cifConfirmWait24hNote,
          DOPNativeStrings.cifConfirmOpenWithRegisteredIdCardNote,
          DOPNativeStrings.cifConfirmUpdateIDCardNote,
        ],
        showOpenCardCTA: true,
        showConfirmInfoUpdated: true,
        showViewNearestBranches: true,
      );

  /// Refer to: https://www.figma.com/file/j8MS3RYzC28OPJZ8vDFU7O/EVO-App-Design?type=design&node-id=25153-55840&mode=design&t=ey1R87ayzi51bbKL-4
  factory DOPNativeCifConfirmUIArgs.otherDifWithCifInfo() => DOPNativeCifConfirmUIArgs(
        title: DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle,
        notes: <String>[
          DOPNativeStrings.cifConfirmDifOtherInfoNote,
          DOPNativeStrings.cifConfirmWait24hNote,
          DOPNativeStrings.cifConfirmForSafetyNote,
        ],
        showConfirmInfoUpdated: true,
        showViewNearestBranches: true,
      );

  /// Without CIF Info
  /// Refer to: https://www.figma.com/file/j8MS3RYzC28OPJZ8vDFU7O/EVO-App-Design?type=design&node-id=25555-35299&mode=design&t=ey1R87ayzi51bbKL-4
  factory DOPNativeCifConfirmUIArgs.difNationIdWithoutCifInfo() => DOPNativeCifConfirmUIArgs(
        title: DOPNativeStrings.cifConfirmOpenCardInfoTitle,
        subtitle: DOPNativeStrings.cifConfirmExistingCustomerSubtitle,
        notes: <String>[
          DOPNativeStrings.cifConfirmPleaseOpenCardNote,
          DOPNativeStrings.cifConfirmUpdateIDCardNote,
        ],
        showOpenCardCTA: true,
        showConfirmInfoUpdated: true,
      );

  /// Refer to: https://www.figma.com/file/j8MS3RYzC28OPJZ8vDFU7O/EVO-App-Design?type=design&node-id=25153-55794&mode=design&t=ey1R87ayzi51bbKL-4
  factory DOPNativeCifConfirmUIArgs.difPhone() => DOPNativeCifConfirmUIArgs(
        title: DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle,
        subtitle: DOPNativeStrings.cifConfirmExistingCustomerSubtitle,
        notes: <String>[
          DOPNativeStrings.cifConfirmDifPhoneNote,
          DOPNativeStrings.cifConfirmUpdateAtCounterNote,
        ],
        showConfirmInfoUpdated: true,
        showViewNearestBranches: true,
      );

  /// Refer to: https://www.figma.com/file/j8MS3RYzC28OPJZ8vDFU7O/EVO-App-Design?type=design&node-id=25153-55863&mode=design&t=ey1R87ayzi51bbKL-4
  factory DOPNativeCifConfirmUIArgs.difCif() => DOPNativeCifConfirmUIArgs(
        title: DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle,
        subtitle: DOPNativeStrings.cifConfirmExistingCustomerSubtitle,
        notes: <String>[
          DOPNativeStrings.cifConfirmDifCifNote,
          DOPNativeStrings.cifConfirmUpdateAtCounterNote,
        ],
        showConfirmInfoUpdated: true,
        showViewNearestBranches: true,
      );

  /// Refer to: https://www.figma.com/file/j8MS3RYzC28OPJZ8vDFU7O/EVO-App-Design?type=design&node-id=25153-55886&mode=design&t=ey1R87ayzi51bbKL-4
  factory DOPNativeCifConfirmUIArgs.difInfo() => DOPNativeCifConfirmUIArgs(
        title: DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle,
        subtitle: DOPNativeStrings.cifConfirmExistingCustomerSubtitle,
        notes: <String>[
          DOPNativeStrings.cifConfirmDifInfoNote,
          DOPNativeStrings.cifConfirmUpdateAtCounterNote,
        ],
        showConfirmInfoUpdated: true,
        showViewNearestBranches: true,
      );

  /// Refer to: https://www.figma.com/file/j8MS3RYzC28OPJZ8vDFU7O/EVO-App-Design?type=design&node-id=25153-55909&mode=design&t=ey1R87ayzi51bbKL-4
  factory DOPNativeCifConfirmUIArgs.cifReopen() => DOPNativeCifConfirmUIArgs(
        title: DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle,
        subtitle: DOPNativeStrings.cifConfirmExistingCustomerSubtitle,
        notes: <String>[
          DOPNativeStrings.cifConfirmCifReopenNote,
          DOPNativeStrings.cifConfirmUpdateAtCounterNote,
        ],
        showConfirmInfoUpdated: true,
        showViewNearestBranches: true,
      );
}
