import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../mock_file/mock_dop_native_application_next_state_use_case.dart';
import 'dop_native_inform_success_state.dart';

class DOPNativeInformSuccessCubit extends CommonCubit<DOPNativeInformSuccessState> {
  final DOPNativeRepo dopNativeRepo;

  DOPNativeInformSuccessCubit(this.dopNativeRepo) : super(DOPNativeInformSuccessInitial());

  Future<void> getApplicationNextState() async {
    emit(DOPNativeApplicationNextStateLoading());

    final BaseEntity entity = await dopNativeRepo.getApplicationNextState(
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeApplicationNextStateFileName(
          MockDOPNativeApplicationNextStateUseCase.applicationNextStateSuccess,
        ),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(DOPNativeApplicationNextStateLoaded());
      return;
    }

    emit(DOPNativeApplicationNextStateError(ErrorUIModel.fromEntity(entity)));
  }
}
