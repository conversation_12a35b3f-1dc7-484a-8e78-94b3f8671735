import 'package:flutter/cupertino.dart';

import '../../../../../resources/resources.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';

class InformSuccessAutoDescWidget extends StatelessWidget {
  const InformSuccessAutoDescWidget({
    required this.amount,
    super.key,
  });

  final String amount;

  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        children: <TextSpan>[
          TextSpan(
            text: DOPNativeStrings.dopNativeRegisterSuccessAutoDesc1,
            style: dopNativeTextStyles.bodyLarge(evoColors.textPassive),
          ),
          TextSpan(
            text: '\n$amount ${DOPNativeStrings.dopNativeVietnameseCurrencySymbolFull}.',
            style: dopNativeTextStyles
                .bodyLarge(evoColors.textActive)
                .copyWith(fontWeight: FontWeight.w700),
          ),
          TextSpan(
            text: '\n${DOPNativeStrings.dopNativeRegisterSuccessAutoDesc2}',
            style: dopNativeTextStyles.bodyLarge(evoColors.textPassive),
          ),
        ],
      ),
    );
  }
}
