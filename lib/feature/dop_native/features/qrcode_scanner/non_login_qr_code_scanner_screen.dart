import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../model/evo_dialog_id.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_dialog_helper.dart';
import '../../../deep_link/deep_link_handler.dart';
import '../../../qrcode_scanner/base_qrcode_scanner_screen.dart';
import '../../../webview/models/evo_webview_arg.dart';
import 'non_login_camera_permission_guide_widget.dart';
import 'non_login_qr_code_scanner_cubit.dart';

class NonLoginQrCodeScannerScreen extends BaseQrCodeScannerScreen {
  /// This function ensures only one [QrCodeScannerScreen] instance in navigator stack,
  /// to prevent the issue in https://github.com/juliansteenbakker/mobile_scanner/issues/539
  static void openSingleInstance() {
    final BuildContext? ctx = navigatorContext;
    return ctx?.popUntilNamed(
      Screen.nonLoginQrCodeScannerScreen.name,
      onPageNotFound: () {
        return ctx.pushNamed(Screen.nonLoginQrCodeScannerScreen.name);
      },
    );
  }

  const NonLoginQrCodeScannerScreen({super.key});

  @override
  State<NonLoginQrCodeScannerScreen> createState() {
    return NonLoginQrCodeScannerScreenState();
  }

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.nonLoginQrCodeScannerScreen.routeName);
}

class NonLoginQrCodeScannerScreenState
    extends BaseQrCodeScannerScreenState<NonLoginQrCodeScannerScreen> {
  final NonLoginQrCodeScannerCubit _nonLoginQrCodeScannerCubit = NonLoginQrCodeScannerCubit(
    deepLinkHandler: getIt.get<DeepLinkHandler>(),
    appState: getIt.get<AppState>(),
  );

  /// clear state to ready scan new QR code when user back from another page to this page
  /// or close popup bottom-sheet, dialog
  @override
  Future<void> didPopNext() async {
    super.didPopNext();

    if (!enableMockTestFlow()) {
      _nonLoginQrCodeScannerCubit.unlockParsingQrCode();
    }
  }

  @override
  void onInitializeCompleted() {
    /// adding here if need to request permission for mock test
  }

  @override
  void onDetectQRCode(String? rawData) {
    super.onDetectQRCode(rawData);
    _nonLoginQrCodeScannerCubit.parseQrCode(rawData);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<NonLoginQrCodeScannerCubit>(
      create: (_) => _nonLoginQrCodeScannerCubit,
      child: BlocListener<NonLoginQrCodeScannerCubit, NonLoginQrCodeScannerState>(
        listener: (BuildContext context, NonLoginQrCodeScannerState state) {
          _handleNonLoginQRCodeState(state);
        },
        child: super.getContentWidget(context),
      ),
    );
  }

  void _handleNonLoginQRCodeState(NonLoginQrCodeScannerState state) {
    if (state is NonLoginDetectGoEvoUrlQrCodeState) {
      CommonWebView.pushNamed(
        arg: EvoWebViewArg(
          title: EvoStrings.scanQRGoEvoUrlWebViewTitle,
          url: state.url,
        ),
      );
      return;
    }

    if (state is NonLoginInvalidQrCodeState) {
      handleShowInvalidQrCodePopup();
      return;
    }
  }

  void handleShowInvalidQrCodePopup({
    String title = EvoStrings.dopNativeScanInvalidQrCodeTitle,
    String message = EvoStrings.dopNativeScanInvalidQrCodeDescription,
    String textPositive = EvoStrings.scanQRRetry,
  }) {
    EvoDialogHelper().showDialogBottomSheet(
      dialogId: EvoDialogId.nonLoginScanInvalidQrCodeBottomSheet,
      title: title,
      content: message,
      textPositive: textPositive,
      onClickPositive: () {
        /// dismiss popup
        navigatorContext?.pop();

        _nonLoginQrCodeScannerCubit.unlockParsingQrCode();
      },
    );
  }

  @override
  Widget buildPermissionGuideWidget() {
    /// this UI will be shown when user deny camera permission
    /// it is placed at the bottom of the screen
    return const Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: NonLoginCameraPermissionGuideWidget(),
    );
  }
}
