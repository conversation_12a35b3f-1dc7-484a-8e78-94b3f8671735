part of 'dop_native_id_card_back_side_verification_cubit.dart';

@immutable
abstract class DOPNativeIdCardBackSideVerificationState extends BlocState {}

class IdCardVerificationInitial extends DOPNativeIdCardBackSideVerificationState {}

class IdCardVerificationProcessing extends DOPNativeIdCardBackSideVerificationState {}

class IdCardBackVerificationSuccess extends DOPNativeIdCardBackSideVerificationState {}

class IdCardUploadBackIdCardFailed extends DOPNativeIdCardBackSideVerificationState {
  final EkycErrorUIModel error;

  IdCardUploadBackIdCardFailed({required this.error});
}

class IdCardProcessIdCardVerificationSuccess extends DOPNativeIdCardBackSideVerificationState {}

class IdCardProcessIdCardFailed extends DOPNativeIdCardBackSideVerificationState {
  final EkycErrorUIModel error;

  IdCardProcessIdCardFailed({required this.error});
}
