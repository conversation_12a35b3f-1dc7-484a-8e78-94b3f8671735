import 'package:trust_vision_plugin/result/tv_nfc_info_result.dart';

import '../../../../../ekyc/model/ekyc_result_model.dart';
import 'liveness_mode.dart';
import 'tv_image_wrapper.dart';

typedef OnNewFrameBatchListener = void Function(Map<String, dynamic> params);

abstract class EkycUiOnlyBridge {
  // List of selfie base64 images
  List<TVImageWrapper>? getSelfieImages();

  // Id card front side base64 image
  String? getIdFrontSideBase64Image();

  // Id card QR front side base64 image
  String? getQrFrontSideBase64Image();

  // Id card back side base64 image
  String? getIdBackSideBase64Image();

  String? getCardTypeId();

  // get info NFC Info
  TVNfcInfoResult? getTVNfcInfoResult();

  // determine if the SDK is initialized
  bool isInitialized();

  Future<TVSDKResult> initEkyc({
    required String? jsonConfigurationByServer,
    String languageCode,
  });

  Future<TVSDKResult> startIdCapturing({
    required bool skipConfirmScreen,
    required bool isReadBothSide,
    bool isBackSide,
  });

  Future<TVSDKResult> startQRCodeCapturing({
    required bool skipConfirmScreen,
  });

  /// Document: https://ekyc.trustingsocial.com/sdks/Flutter-SDK#4-nfc-reader
  /// [dateOfBirth] format "dd/MM/yyyy"
  /// [dateOfExpiry] format "dd/MM/yyyy"
  /// [dateOfBirth] and [dateOfExpiry] are used for iOS only
  /// Refer: Document iOS https://ekyc.trustingsocial.com/sdks/IOS-SDK#33-scan-nfc
  Future<TVSDKResult> readNfc({
    required String idCardNumber,
    String? dateOfBirth,
    String? dateOfExpiry,
  });

  // Refer: https://ekyc.trustingsocial.com/sdks/Flutter-SDK#45-check-if-the-device-supports-nfc
  Future<bool> checkNfcSupport();

  Future<TVSDKResult> startSelfieCapturing({
    required LivenessModeWrapper livenessMode,
    required OnNewFrameBatchListener onNewFrameBatchListener,
    bool skipConfirmScreen = false,
  });

  void resetFlow();

  void closeScanNFCScreenOfSDK();
}
