import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import '../../../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../../../data/response/dop_native/dop_native_ekyc_status_entity.dart';
import '../../../../../../../data/response/dop_native/dop_native_selfie_submit_gestures_entity.dart';
import '../../../../../util/dop_native_submit_status_polling/dop_native_submit_status_polling.dart';
import '../../../dop_native_ekyc_config.dart';
import '../../../mock/mock_dop_native_ekyc_use_case.dart';
import '../../../mock/mock_dop_native_get_submit_status_use_case.dart';
import '../../../sdk_bridge/tv_ekyc/liveness_mode.dart';
import '../../../sdk_bridge/tv_ekyc/tv_image_wrapper.dart';
import '../../../ui_model/ekyc_error_ui_model.dart';
import '../../../utils/dop_native_ekyc_api_response_handler/dop_native_ekyc_api_response_handler.dart';

part 'dop_native_ekyc_selfie_verification_cubit_state.dart';

class DopNativeEkycSelfieVerificationCubit
    extends CommonCubit<DopNativeEkycSelfieVerificationState> {
  final DopNativeEkycUIOnlyRepo dopNativeEkycUiOnlyRepo;
  final EkycCommonApiResponsesHandler commonApisResponseHandler;
  final DOPNativeSubmitStatusPolling dopNativeSubmitStatusPolling;

  DopNativeEkycSelfieVerificationCubit({
    required this.dopNativeEkycUiOnlyRepo,
    required this.commonApisResponseHandler,
    required this.dopNativeSubmitStatusPolling,
  }) : super(SelfieVerificationInitial());

  Future<void> submitGestures({
    required String batchId,
    required LivenessModeWrapper livenessMode,
    required List<TVImageWrapper>? selfieImages,
  }) async {
    emit(SelfieVerificationLoading());

    final List<BaseEntity> entities = await uploadSelfieImagesAndSubmitGestureType(
      batchId: batchId,
      selfieImages: selfieImages,
      livenessMode: livenessMode,
    );

    for (final BaseEntity entity in entities) {
      final EkycErrorUIModel? eKycErrorUIModel = getSelfieImageError(entity);
      if (eKycErrorUIModel != null) {
        emit(SelfieVerificationFailed(error: eKycErrorUIModel));
        return;
      }
    }

    final DOPNativeSelfieSubmitGesturesEntity entity = await submitGestureImages(batchId: batchId);

    final EkycErrorUIModel? eKycErrorUIModel = getSelfieImageError(entity);

    if (eKycErrorUIModel == null) {
      // success
      pollingSubmitStatus(entity.jobId);
    } else {
      emit(SelfieVerificationFailed(error: eKycErrorUIModel));
    }
  }

  Future<DOPNativeSelfieSubmitGesturesEntity> submitGestureImages({
    required String batchId,
  }) async {
    final DOPNativeSelfieSubmitGesturesEntity entity =
        await dopNativeEkycUiOnlyRepo.submitGestureImages(
      batchId: batchId,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeEkycFileNameByCase(
            MockTestDOPNativeEkycUseCase.selfieSubmitGesturesSuccess),
      ),
    );

    return entity;
  }

  @visibleForTesting
  Future<List<BaseEntity>> uploadSelfieImagesAndSubmitGestureType({
    required String batchId,
    required List<TVImageWrapper>? selfieImages,
    required LivenessModeWrapper livenessMode,
  }) async {
    if (selfieImages == null || selfieImages.isEmpty) {
      return <BaseEntity>[convertFailureToBaseEntity()];
    }

    final List<Future<BaseEntity>> uploadFutures = selfieImages
        .map((TVImageWrapper image) => uploadSelfieImage(batchId: batchId, selfieImage: image))
        .toList();
    uploadFutures.add(submitSelfieType(batchId: batchId, livenessMode: livenessMode));

    final List<BaseEntity> entities = await Future.wait(uploadFutures);
    return entities;
  }

  @visibleForTesting
  Future<BaseEntity> submitSelfieType({
    required String batchId,
    required LivenessModeWrapper livenessMode,
  }) async {
    final BaseEntity entity = await dopNativeEkycUiOnlyRepo.submitSelfieType(
      batchId: batchId,
      selfieType: livenessMode.value,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeEkycFileNameByCase(
            MockTestDOPNativeEkycUseCase.selfieSubmitSelfieTypeSuccess),
      ),
    );

    return entity;
  }

  @visibleForTesting
  Future<BaseEntity> uploadSelfieImage({
    required String batchId,
    required TVImageWrapper selfieImage,
  }) async {
    final String? image = selfieImage.rawImageBase64;
    if (image == null || image.isEmpty) {
      return convertFailureToBaseEntity();
    }

    final BaseEntity entity = await dopNativeEkycUiOnlyRepo.uploadEkycImage(
      label: DOPNativeEKYCConfig.labelUploadSelfieImage,
      base64Image: image,
      batchId: batchId,
      direction: selfieImage.direction?.value,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeEkycFileNameByCase(
          MockTestDOPNativeEkycUseCase.uploadEkycImageSuccess,
        ),
      ),
    );

    return entity;
  }

  @visibleForTesting
  Future<void> pollingSubmitStatus(String? jobId) async {
    final DOPNativeEkycStatusEntity entity = await dopNativeEkycUiOnlyRepo.getSubmitStatus(
      jobId: jobId,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeGetSubmitStatusFileNameByCase(
          MockDOPNativeGetSubmitStatusUseCase.success,
        ),
      ),
    );

    final EkycErrorUIModel? eKycErrorUIModel = commonApisResponseHandler.getStatusError(entity);

    if (eKycErrorUIModel != null) {
      emit(SelfieVerificationFailed(error: eKycErrorUIModel));
      return;
    }

    // success
    _handlePollingSubmitStatusSuccess(entity, jobId);
  }

  void _handlePollingSubmitStatusSuccess(
    DOPNativeEkycStatusEntity entity,
    String? jobId,
  ) {
    final String? submitStatus = entity.status;
    switch (submitStatus) {
      /// Stop polling and return success
      case DOPNativeEkycStatusEntity.statusSuccess:
        emit(SelfieVerificationSuccess());
        break;

      /// Stop polling and return error
      case DOPNativeEkycStatusEntity.statusFailure:
        emit(SelfieVerificationFailed(
          error: EkycErrorUIModel.fromHttpCode(
            statusCode: entity.statusCode,
            message: entity.userMessage,
          ),
        ));
        break;

      /// Continue polling
      case DOPNativeEkycStatusEntity.statusInProgress:
      case DOPNativeEkycStatusEntity.statusPending:
      default:
        dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: () {
          pollingSubmitStatus(jobId);
        });
        break;
    }
  }

  void cancelPollingSubmitStatus() {
    dopNativeSubmitStatusPolling.cancel();
  }

  /// get error from the selfie image response entity
  @visibleForTesting
  EkycErrorUIModel? getSelfieImageError(BaseEntity entity) {
    if (entity.verdict == BaseEntity.verdictSuccess) {
      return null;
    }

    final String? message = entity.userMessage;

    switch (entity.verdict) {
      case DOPNativeSelfieSubmitGesturesEntity.verdictLimitExceed:
        return EkycErrorUIModel(code: EkycErrorCode.limitExceed, message: message);
      case DOPNativeSelfieSubmitGesturesEntity.verdictFailure:
        if (entity.statusCode == CommonHttpClient.BAD_REQUEST) {
          return EkycErrorUIModel(code: EkycErrorCode.otherEkycError, message: message);
        }
        break;
    }

    return EkycErrorUIModel.fromHttpCode(statusCode: entity.statusCode, message: message);
  }

  @visibleForTesting
  BaseEntity convertFailureToBaseEntity() {
    return BaseEntity(
      statusCode: CommonHttpClient.BAD_REQUEST,
      verdict: DOPNativeSelfieSubmitGesturesEntity.verdictFailure,
    );
  }
}
