part of 'dop_native_selfie_capture_cubit.dart';

@immutable
abstract class DopNativeSelfieCaptureState implements BlocState {}

class DopNativeSelfieCaptureInitial extends DopNativeSelfieCaptureState {}

class DopNativeSelfieCaptureSuccess extends DopNativeSelfieCaptureState {
  final String batchId;
  final List<TVImageWrapper>? selfieImages;

  DopNativeSelfieCaptureSuccess({
    required this.batchId,
    required this.selfieImages,
  });
}

class DopNativeSelfieCaptureFailure extends DopNativeSelfieCaptureState {
  final TVSDKResult tvSDKResult;

  DopNativeSelfieCaptureFailure({required this.tvSDKResult});
}
