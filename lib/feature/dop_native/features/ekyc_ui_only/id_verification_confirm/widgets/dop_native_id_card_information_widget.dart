import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../resources/dop_native_ui_strings.dart';
import '../../../../util/dop_functions.dart';
import '../models/dop_native_ocr_data_model.dart';
import 'dop_native_edit_old_id_card_widget.dart';
import 'dop_native_infomation_item_widget.dart';

class DOPNativeIdCardInformationWidget extends StatelessWidget {
  final DOPNativeOCRDataModel? information;
  final void Function(String)? onEditOldIdCard;

  const DOPNativeIdCardInformationWidget({
    required this.information,
    this.onEditOldIdCard,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        DOPNativeInformationItemWidget(
          title: DOPNativeStrings.dopNativeIdVerificationName,
          description: information?.fullName,
        ),
        DOPNativeInformationItemWidget(
          title: DOPNativeStrings.dopNativeIdVerificationIdCard,
          description: formatIdCard(information?.idCard) ?? '',
        ),
        DOPNativeInformationItemWidget(
          title: DOPNativeStrings.dopNativeIdVerificationOldIdCard,
          description: formatIdCard(information?.oldIDCard),
          descriptionWidget: buildDescriptionOldIdCard(
            information?.allowEditOldIDCard ?? false,
            information?.oldIDCard,
          ),
        ),
        DOPNativeInformationItemWidget(
          title: DOPNativeStrings.dopNativeIdVerificationDOB,
          description: dopUtilFunction.formatDOPBirthday(information?.birthday),
        ),
        DOPNativeInformationItemWidget(
          title: DOPNativeStrings.dopNativeIdVerificationGender,
          description: information?.gender?.value,
        ),
        DOPNativeInformationItemWidget(
          title: DOPNativeStrings.dopNativeIdVerificationPlaceOfIssue,
          description: information?.idIssuePlaceName,
        ),
        DOPNativeInformationItemWidget(
          title: DOPNativeStrings.dopNativeIdVerificationPermanentResidence,
          description: information?.residenceAddressName,
          isShown: information?.residenceAddressName?.isNotEmpty == true,
        ),
      ],
    );
  }

  @visibleForTesting
  Widget? buildDescriptionOldIdCard(bool allowEditOldIDCard, String? oldIDCard) {
    if (!allowEditOldIDCard) {
      return null;
    }

    return DOPNativeEditOldIdCardWidget(
      oldIDCard: oldIDCard,
      onEditOldIdCard: (String value) {
        onEditOldIdCard?.call(value);
      },
    );
  }

  @visibleForTesting
  String? formatIdCard(String? idCard) {
    if (idCard == null || idCard.isEmpty) {
      return null;
    }

    return idCard.applyStringFormat(
        stringFormatType: StringFormatType.idCard, prefixGroup: 3, suffixGroup: 3);
  }
}
