import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../resources/resources.dart';
import '../../../base/dop_native_page_state_base.dart';
import '../../../resources/dop_native_images.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../util/metadata/dop_native_metadata_utils_impl.dart';
import '../../../util/validation/cubit/dop_native_validation_utils_cubit.dart';
import '../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../widgets/dop_native_form_header_widget.dart';
import '../../../widgets/text_field/dop_native_text_field_widget.dart';
import 'additional_info_screen/dop_native_ekyc_confirm_additional_info_screen.dart';
import 'cubit/dop_native_ekyc_confirm_cta_cubit.dart';
import 'cubit/dop_native_ekyc_confirm_cta_state.dart';
import 'cubit/dop_native_id_verification_confirm_cubit.dart';
import 'dialogs/address_dialog/dop_native_address_dialog.dart';
import 'models/dop_native_ocr_data_model.dart';
import 'widgets/dop_native_id_card_information_widget.dart';

class DOPNativeEkycConfirmScreen extends PageBase {
  const DOPNativeEkycConfirmScreen({super.key});

  @override
  DOPNativePageStateBase<DOPNativeEkycConfirmScreen> createState() =>
      _DOPNativeEkycConfirmScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: Screen.dopNativeEKYCConfirmScreen.routeName,
      );

  static void pushNamed() {
    return navigatorContext?.pushNamed(
      Screen.dopNativeEKYCConfirmScreen.name,
    );
  }

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeEKYCConfirmScreen.name,
    );
  }
}

class _DOPNativeEkycConfirmScreenState extends DOPNativePageStateBase<DOPNativeEkycConfirmScreen> {
  final DOPNativeIDVerificationConfirmCubit _cubit = DOPNativeIDVerificationConfirmCubit(
    dopNativeRepo: getIt.get<DOPNativeRepo>(),
    dopNativeEkycUIOnlyRepo: getIt.get<DopNativeEkycUIOnlyRepo>(),
    appState: getIt.get<AppState>(),
    metadataUtils: DOPNativeMetadataUtilsImpl(dopNativeRepo: getIt.get<DOPNativeRepo>()),
  );
  final DOPNativeValidationUtilsCubit _validationUtilsCubit = DOPNativeValidationUtilsCubit();
  final DOPNativeEkycConfirmCTACubit _ctaCubit = DOPNativeEkycConfirmCTACubit();
  final AppState _appState = getIt.get<AppState>();
  final TextEditingController _streetTextController = TextEditingController();
  final TextEditingController _permanentResidenceTextController = TextEditingController();
  String? _oldIdCardValue;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _cubit.getOCRData();
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: MultiBlocProvider(
        providers: <BlocProvider<dynamic>>[
          BlocProvider<DOPNativeIDVerificationConfirmCubit>(
            create: (_) => _cubit,
          ),
          BlocProvider<DOPNativeValidationUtilsCubit>(
            create: (_) => _validationUtilsCubit,
          ),
          BlocProvider<DOPNativeEkycConfirmCTACubit>(
            create: (_) => _ctaCubit,
          )
        ],
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: _buildContent(),
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    return MultiBlocListener(
      listeners: <BlocListener<dynamic, dynamic>>[
        BlocListener<DOPNativeIDVerificationConfirmCubit, DOPNativeIDVerificationConfirmState>(
          listener: (BuildContext context, DOPNativeIDVerificationConfirmState state) {
            _handleListener(state);
          },
        ),
        BlocListener<DOPNativeValidationUtilsCubit, DOPNativeValidationUtilsState>(
          listener: (BuildContext context, DOPNativeValidationUtilsState state) {
            _handleValidationListener(state);
          },
        ),
      ],
      child: BlocBuilder<DOPNativeIDVerificationConfirmCubit, DOPNativeIDVerificationConfirmState>(
        buildWhen: (DOPNativeIDVerificationConfirmState previous,
            DOPNativeIDVerificationConfirmState current) {
          return current is DOPNativeGetOCRDataSucceed || current is DOPNativeGetOCRDataFailed;
        },
        builder: (_, DOPNativeIDVerificationConfirmState state) {
          DOPNativeOCRDataModel? ocrDataModel;
          if (state is DOPNativeGetOCRDataSucceed) {
            ocrDataModel = state.ocrData;
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const DOPNativeFormHeaderWidget(
                currentStep: 1,
                titleStep: DOPNativeStrings.dopNativeIdVerificationConfirmIndicatorTitle,
              ),
              const SizedBox(height: 20),
              _buildTitleScreenWidget(),
              const SizedBox(height: 32),
              DOPNativeIdCardInformationWidget(
                information: ocrDataModel,
                onEditOldIdCard: (String value) {
                  _oldIdCardValue = value;
                },
              ),
              const SizedBox(height: 32),
              _buildInputPermanentResidence(ocrDataModel?.residenceAddressName),
              _buildCTA(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTitleScreenWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          DOPNativeStrings.dopNativeIdVerificationConfirmScreenTitle,
          style: dopNativeTextStyles.h500(),
        ),
        const SizedBox(height: 8),
        Text(
          DOPNativeStrings.dopNativeIdVerificationConfirmScreenDesc,
          style: dopNativeTextStyles.bodyLarge(dopNativeColors.textPassive),
        ),
      ],
    );
  }

  Widget _buildInputPermanentResidence(String? permanentResidence) {
    final bool ignoreInputPermanentResidence = _hasResidenceAddress(permanentResidence);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          ignoreInputPermanentResidence
              ? '${DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextTitle} (${DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextTitleAddition})'
              : DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextTitle,
          style: dopNativeTextStyles.h200(),
        ),
        const SizedBox(height: 16),
        _buildInputResidenceAddress(ignoreInputPermanentResidence),
        _buildStreetPermanentAddress(),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildInputResidenceAddress(bool ignoreInputPermanentResidence) {
    return ignoreInputPermanentResidence
        ? const SizedBox.shrink()
        : Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: GestureDetector(
              onTap: () {
                _openAddressDialog();
              },
              child: BlocBuilder<DOPNativeValidationUtilsCubit, DOPNativeValidationUtilsState>(
                buildWhen: (_, DOPNativeValidationUtilsState current) {
                  return current is DOPNativeResidenceAddressValid ||
                      current is DOPNativeResidenceAddressFailed;
                },
                builder: (BuildContext context, DOPNativeValidationUtilsState state) {
                  String? errorMsg;
                  if (state is DOPNativeResidenceAddressFailed) {
                    errorMsg = state.errorMsg;
                  }

                  return DOPNativeTextField(
                    backgroundColor: dopNativeColors.background,
                    controller: _permanentResidenceTextController,
                    isEnabled: false,
                    hintText:
                        DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextHint,
                    suffixIcon: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: evoImageProvider.asset(
                        DOPNativeImages.icArrowDown,
                        width: 20,
                        height: 20,
                      ),
                    ),
                    errorText: errorMsg,
                  );
                },
              ),
            ),
          );
  }

  Widget _buildStreetPermanentAddress() {
    return BlocBuilder<DOPNativeValidationUtilsCubit, DOPNativeValidationUtilsState>(
      buildWhen: (_, DOPNativeValidationUtilsState current) {
        return current is DOPNativeAddressValid || current is DOPNativeAddressFailed;
      },
      builder: (BuildContext context, DOPNativeValidationUtilsState state) {
        String? errorMsg;
        if (state is DOPNativeAddressFailed) {
          errorMsg = state.errorMsg;
        }

        if (state is DOPNativeAddressValid) {
          _appState.dopNativeState.ocrData?.copyWith(
            familyAddressValue: state.address,
          );
        }

        return DOPNativeTextField(
          backgroundColor: dopNativeColors.background,
          controller: _streetTextController,
          hintText: DOPNativeStrings.dopNativeIdVerificationStreetInputTextHint,
          onChanged: (String value) {
            _validationUtilsCubit.validateDOPNativeAddress(value);
          },
          errorText: errorMsg,
        );
      },
    );
  }

  Widget _buildCTA() {
    return BlocBuilder<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
        builder: (_, DOPNativeEkycConfirmCTAState state) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: CommonButton(
          isWrapContent: false,
          onPressed: state.enable
              ? () {
                  _onNextButtonPressed();
                }
              : null,
          style: dopNativeButtonStyles.primary(ButtonSize.medium),
          child: const Text(DOPNativeStrings.dopNativeNext),
        ),
      );
    });
  }

  void _onNextButtonPressed() {
    final bool canSaveInfo = _checkBeforeSave();
    if (!canSaveInfo) {
      return;
    }

    appState.dopNativeState.ocrData = _appState.dopNativeState.ocrData?.copyWith(
      familyAddressValue: _streetTextController.text,
      oldIDCard: _oldIdCardValue,
    );

    DOPNativeEkycConfirmAdditionalInfoScreen.pushNamed();
  }

  bool _checkBeforeSave() {
    final bool isStreetValid =
        _validationUtilsCubit.validateDOPNativeAddress(_streetTextController.text);
    if (!isStreetValid) {
      return false;
    }

    final String? residenceAddressName = appState.dopNativeState.ocrData?.residenceAddressName;
    final bool isValidResidenceAddress =
        _validationUtilsCubit.validateResidenceAddress(residenceAddressName);
    if (!isValidResidenceAddress) {
      return false;
    }

    return true;
  }

  Future<void> _openAddressDialog() async {
    await DOPNativeAddressDialog.show(
      addressData: _cubit.saveInAppState,
    );

    final String? residenceAddress = _appState.dopNativeState.ocrData?.residenceAddressName;
    _validationUtilsCubit.validateResidenceAddress(residenceAddress);
    _permanentResidenceTextController.text = residenceAddress ?? '';
  }

  void _handleListener(DOPNativeIDVerificationConfirmState state) {
    if (state is DOPNativeIDVerificationConfirmLoading) {
      showDOPLoading();
      return;
    }

    hideDOPLoading();

    if (state is DOPNativeGetOCRDataSucceed) {
      _cubit.saveOCRDataToAppState(state.ocrData);
      if (_hasResidenceAddress(state.ocrData?.residenceAddressName)) {
        _ctaCubit.updateResidenceAddress(FieldState.noneCheck);
      }
      return;
    }

    if (state is DOPNativeGetOCRDataFailed) {
      handleEvoApiError(state.error);
      return;
    }
  }

  void _handleValidationListener(DOPNativeValidationUtilsState state) {
    /// Residence address
    if (state is DOPNativeResidenceAddressValid) {
      _ctaCubit.updateResidenceAddress(FieldState.valid);
      return;
    }
    if (state is DOPNativeResidenceAddressFailed) {
      _ctaCubit.updateResidenceAddress(FieldState.invalid);
      return;
    }

    /// Street address
    if (state is DOPNativeAddressValid) {
      _ctaCubit.updateStreetAddress(FieldState.valid);
      return;
    }
    if (state is DOPNativeAddressFailed) {
      _ctaCubit.updateStreetAddress(FieldState.invalid);
      return;
    }
  }

  bool _hasResidenceAddress(String? permanentResidence) {
    return permanentResidence?.isNotEmpty == true;
  }
}
