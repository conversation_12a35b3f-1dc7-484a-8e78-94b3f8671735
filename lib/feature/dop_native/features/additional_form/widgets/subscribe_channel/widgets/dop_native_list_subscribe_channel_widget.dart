import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';

import '../../../../../../../data/response/dop_native/dop_native_metadata_item_entity.dart';
import '../../../../../resources/dop_native_resources.dart';
import '../../../../../resources/dop_native_ui_strings.dart';
import '../../../../../widgets/dop_native_labeled_check_box_widget.dart';
import '../utils/subscribe_channel_constants.dart';

class DOPNativeListSubscribeChannelWidget extends StatefulWidget {
  final void Function(List<DOPNativeMetadataItemEntity> selectedList) onValueChanged;
  final List<DOPNativeMetadataItemEntity> subscribeChannels;

  const DOPNativeListSubscribeChannelWidget({
    required this.onValueChanged,
    required this.subscribeChannels,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _DOPNativeListSubscribeChannelWidgetState();
}

class _DOPNativeListSubscribeChannelWidgetState extends State<DOPNativeListSubscribeChannelWidget> {
  final List<DOPNativeMetadataItemEntity> _listSelected = <DOPNativeMetadataItemEntity>[];

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _selectDefaultChannels();
    });
    super.initState();
  }

  void _selectDefaultChannels() {
    final DOPNativeMetadataItemEntity? zalo = widget.subscribeChannels.firstWhereOrNull(
        (DOPNativeMetadataItemEntity e) => e.code == SubscribeChannelConstants.zalo.value);

    if (zalo != null) {
      _handleSelectItem(true, zalo);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          DOPNativeStrings.dopNativeFavoriteInfoChannel,
          style: dopNativeTextStyles.h300(),
        ),
        const SizedBox(height: 16),
        _buildSubscribeChannels(widget.subscribeChannels),
      ],
    );
  }

  Widget _buildSubscribeChannels(List<DOPNativeMetadataItemEntity> subscribeChannels) {
    final List<Widget> rowChannel = <Widget>[];
    final List<Widget> colChannel = <Widget>[];
    for (int i = 0; i < subscribeChannels.length; i++) {
      if (i.isEven) {
        rowChannel.add(_buildItemChannel(subscribeChannels[i]));
      } else {
        rowChannel.add(_buildItemChannel(subscribeChannels[i]));
        colChannel.add(Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(children: <Widget>[...rowChannel]),
        ));
        rowChannel.clear();
      }
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: colChannel,
    );
  }

  Widget _buildItemChannel(DOPNativeMetadataItemEntity channel) {
    final bool isThisChannelSelected = _listSelected.contains(channel);
    return Expanded(
      child: DOPNativeLabeledCheckboxWidget(
        label: channel.name ?? '',
        value: isThisChannelSelected,
        onChanged: (bool isSelect) {
          _handleSelectItem(isSelect, channel);
        },
      ),
    );
  }

  void _handleSelectItem(bool isSelect, DOPNativeMetadataItemEntity channel) {
    if (isSelect && !_listSelected.contains(channel)) {
      _listSelected.add(channel);
    } else if (!isSelect && _listSelected.contains(channel)) {
      _listSelected.remove(channel);
    }
    widget.onValueChanged(_listSelected);
    setState(() {});
  }
}
