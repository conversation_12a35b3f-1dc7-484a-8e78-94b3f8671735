part of 'dop_native_address_additional_info_cubit.dart';

@immutable
abstract class DOPNativeAddressAdditionalInfoState implements BlocState {}

class DOPNativeAddressAdditionalInfoInitial implements DOPNativeAddressAdditionalInfoState {}

class DOPNativeAddressAdditionalInfoLoading implements DOPNativeAddressAdditionalInfoState {}

class DOPNativeAddressAdditionalInfoSucceed implements DOPNativeAddressAdditionalInfoState {
  final DOPNativeAdditionalFormDataModel newModel;

  DOPNativeAddressAdditionalInfoSucceed({required this.newModel});
}

class DOPNativeAddressAdditionalInfoFailed implements DOPNativeAddressAdditionalInfoState {
  final ErrorUIModel error;

  const DOPNativeAddressAdditionalInfoFailed(this.error);
}

class DOPNativeGetCardDeliveryTypeSucceed implements DOPNativeAddressAdditionalInfoState {
  final DOPNativeMetadataEntity cardDeliveryTypes;

  DOPNativeGetCardDeliveryTypeSucceed({required this.cardDeliveryTypes});
}

class DOPNativeGetCardDeliveryTypeFailed implements DOPNativeAddressAdditionalInfoState {
  final ErrorUIModel error;

  const DOPNativeGetCardDeliveryTypeFailed({required this.error});
}

class DOPNativeGetResidenceAddressNameSucceed implements DOPNativeAddressAdditionalInfoState {
  final String residenceAddressName;

  DOPNativeGetResidenceAddressNameSucceed({required this.residenceAddressName});
}

class DOPNativeGetResidenceAddressNameFailed implements DOPNativeAddressAdditionalInfoState {
  final ErrorUIModel error;

  const DOPNativeGetResidenceAddressNameFailed({required this.error});
}

class HasValidForm extends DOPNativeAddressAdditionalInfoState {}

class HasInvalidForm extends DOPNativeAddressAdditionalInfoState {}
