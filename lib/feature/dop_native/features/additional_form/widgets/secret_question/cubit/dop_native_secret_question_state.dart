import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../models/additional_form_data_model.dart';

abstract class DOPNativeSecretQuestionState implements BlocState {}

class DOPNativeSecretQuestionInitial extends DOPNativeSecretQuestionState {}

class DOPNativeSecretQuestionLoading extends DOPNativeSecretQuestionState {}

class OnSubmitSucceed extends DOPNativeSecretQuestionState {
  DOPNativeAdditionalFormDataModel model;

  OnSubmitSucceed(this.model);
}

class OnSubmitFailed extends DOPNativeSecretQuestionState {
  final ErrorUIModel error;

  OnSubmitFailed(this.error);
}

class HasValidInput extends DOPNativeSecretQuestionState {}

class HasInvalidInput extends DOPNativeSecretQuestionState {
  final String errorMsg;

  HasInvalidInput(this.errorMsg);
}
