import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../../resources/resources.dart';
import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../util/functions.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../util/credit_assignment/cubit/dop_native_credit_assignment_cubit.dart';
import '../../../widgets/dop_native_status_card_icon_widget.dart';
import 'dop_native_e_sign_intro_base_page.dart';

class DOPNativeESignIntroMWGScreen extends PageBase {
  static void pushNamed() {
    return navigatorContext?.pushNamed(Screen.dopNativeESignIntroMWGScreen.name);
  }

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(Screen.dopNativeESignIntroMWGScreen.name);
  }

  const DOPNativeESignIntroMWGScreen({super.key});

  @override
  State<DOPNativeESignIntroMWGScreen> createState() => _DOPNativeESignIntroMWGScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.dopNativeESignIntroMWGScreen.name);
}

class _DOPNativeESignIntroMWGScreenState
    extends DOPNativeESignIntroBasePage<DOPNativeESignIntroMWGScreen> {
  final DOPNativeCreditAssignmentCubit _creditAssignmentCubit =
      DOPNativeCreditAssignmentCubit(getIt<DOPNativeRepo>());

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _creditAssignmentCubit.getAppraisingCreditAssignment();
    });
  }

  @override
  Widget buildContent() {
    return BlocProvider<DOPNativeCreditAssignmentCubit>(
      create: (_) => _creditAssignmentCubit,
      child: BlocListener<DOPNativeCreditAssignmentCubit, DOPNativeCreditAssignmentState>(
        listener: (BuildContext context, DOPNativeCreditAssignmentState state) {
          _handleCreditAssignmentState(state);
        },
        child: DOPNativeStatusCardIconWidget(
          title: DOPNativeStrings.dopNativeESignIntroTitle,
          descriptionWidget: _buildDescriptionWidget(),
          ctaWidget: buildCTA(),
        ),
      ),
    );
  }

  Widget _buildDescriptionWidget() {
    return Column(
      children: <Widget>[
        _buildTextCredit(),
        const SizedBox(height: 16),
        Text(
          DOPNativeStrings.dopNativeESignIntroGotoSignESign, // Add this line
          style: dopNativeTextStyles.bodyLarge(dopNativeColors.textPassive), // Add this line
          textAlign: TextAlign.center, // Add this line
        ), // Add this line
      ],
    );
  }

  Widget _buildTextCredit() {
    return BlocBuilder<DOPNativeCreditAssignmentCubit, DOPNativeCreditAssignmentState>(
      builder: (_, DOPNativeCreditAssignmentState state) {
        int? credit;

        if (state is DOPNativeCreditAssignmentLoadSuccess) {
          credit = state.creditAmount;
        }

        if (credit == null) {
          return const SizedBox.shrink();
        }

        final String creditString = evoUtilFunction.evoFormatCurrency(
          credit,
          currencySymbol: vietNamCurrencySymbol,
        );

        return Text.rich(
          TextSpan(
            style: dopNativeTextStyles.bodyLarge(dopNativeColors.textPassive),
            children: <InlineSpan>[
              TextSpan(text: DOPNativeStrings.dopNativeESignIntroCreditLimit),
              TextSpan(
                text: ' $creditString',
                style: dopNativeTextStyles.h300(color: dopNativeColors.textPassive),
              ),
            ],
          ),
          textAlign: TextAlign.center,
        );
      },
    );
  }

  void _handleCreditAssignmentState(DOPNativeCreditAssignmentState state) {
    if (state is DOPNativeCreditAssignmentLoading) {
      showDOPLoading();
      return;
    }

    hideDOPLoading();
  }

  @override
  void onPressCTA() {
    eSignIntroCubit.submitESignIntroNext();
  }
}
