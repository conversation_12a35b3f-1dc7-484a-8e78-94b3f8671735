import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../resources/resources.dart';
import '../../../base/cubit/dop_native_application_state_cubit.dart';
import '../../../base/cubit/dop_native_complete_onboarding_cubit.dart';
import '../../../base/dop_native_page_state_base.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../util/card_status/cubit/dop_native_card_status_cubit.dart';
import '../../../util/card_status/cubit/dop_native_card_status_state.dart';
import '../../../util/dop_functions.dart';
import '../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../widgets/card_activate/dop_native_card_status_widget.dart';
import '../../../widgets/dop_native_card_acquisition_related_info_widget.dart';
import '../../../widgets/dop_native_status_card_icon_widget.dart';
import '../dop_native_card_cic_blocked_screen.dart';

class DOPNativeUnderWritingCardIssuedScreen extends PageBase {
  final DOPNativeCardStatusCubit? cardStatusCubit;
  final DOPNativeCompleteOnboardingCubit? completeOnboardingCubit;
  final DOPNativeApplicationStateCubit? applicationStateCubit;

  const DOPNativeUnderWritingCardIssuedScreen({
    super.key,
    this.cardStatusCubit,
    this.completeOnboardingCubit,
    this.applicationStateCubit,
  });

  @override
  DOPNativePageStateBase<DOPNativeUnderWritingCardIssuedScreen> createState() =>
      DOPNativeUnderWritingCardIssuedScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeUnderwritingCardIssuedScreen.routeName);

  static void pushNamed() {
    return navigatorContext?.pushNamed(
      Screen.dopNativeUnderwritingCardIssuedScreen.name,
    );
  }

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeUnderwritingCardIssuedScreen.name,
    );
  }
}

@visibleForTesting
class DOPNativeUnderWritingCardIssuedScreenState
    extends DOPNativePageStateBase<DOPNativeUnderWritingCardIssuedScreen> {
  @visibleForTesting
  late final DOPNativeCardStatusCubit cubit = widget.cardStatusCubit ??
      DOPNativeCardStatusCubit(
        dopNativeRepo: getIt.get<DOPNativeRepo>(),
        appState: getIt.get<AppState>(),
        cardStatusUseCase: CardStatusUseCase.underwritingCardIssued,
      );

  @override
  DOPNativeCompleteOnboardingCubit get dopNativeCompleteOnboardingCubit =>
      widget.completeOnboardingCubit ?? super.dopNativeCompleteOnboardingCubit;

  @override
  DOPNativeApplicationStateCubit get dopNativeApplicationStateCubit =>
      widget.applicationStateCubit ?? super.dopNativeApplicationStateCubit;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      cubit.getCardStatus();
    });
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<DOPNativeCardStatusCubit>(
      create: (_) => cubit,
      child: BlocListener<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        listener: (_, DOPNativeCardStatusState state) {
          onHandleState(state);
        },
        child: BlocBuilder<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
          builder: (_, DOPNativeCardStatusState state) {
            return Scaffold(
              backgroundColor: dopNativeColors.screenBackground,
              appBar: DOPNativeAppBar(
                onExitDOP: state is UnderwritingCardIssuedCardActivated
                    ? dopNativeCompleteOnboardingCubit.onExitDOPFlow
                    : null,
              ),
              body: SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
                  child: bodyScreen(state),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  @visibleForTesting
  Widget bodyScreen(DOPNativeCardStatusState state) {
    if (state is GetCardStatusFailure || state is GetCardStatusBlocked) {
      return const SizedBox.shrink();
    }

    final List<Widget> widgets = <Widget>[];

    if (state is UnderwritingCardIssuedCardActivated) {
      widgets.addAll(activatedCardSuccess());
    } else if (state is UnderwritingCardIssuedCanActivateCard) {
      widgets.addAll(notActivatedCardWidgets(isActiveCard: true));
    } else if (state is UnderwritingNoneOfflineMerchantOrNoneLinkCard ||
        state is UnderwritingCardIssuedCannotActivateCard) {
      widgets.addAll(notActivatedCardWidgets());
    }

    return Column(
      children: widgets,
    );
  }

  @visibleForTesting
  List<Widget> activatedCardSuccess() {
    final List<Widget> widgets = <Widget>[];
    widgets.addAll(<Widget>[
      DOPNativeStatusCardIconWidget(
        title: DOPNativeStrings.dopNativeCardIssuedActivatedCardTitle,
        description: DOPNativeStrings.dopNativeCardIssuedActivatedCardDescription,
        ctaWidget: buildReceiveVoucherCTA(),
      ),
      const SizedBox(height: 20),
      buildNotActivatedCardFooter(),
    ]);
    return widgets;
  }

  @visibleForTesting
  List<Widget> notActivatedCardWidgets({bool isActiveCard = false}) {
    return <Widget>[
      buildNotActivatedCardTitle(isActiveCard: isActiveCard),
      const SizedBox(height: 24),
      buildNotActivatedCardFooter(enableViewEContractCTA: true),
      const SizedBox(height: 8),
    ];
  }

  @visibleForTesting
  Widget buildReceiveVoucherCTA() {
    return CommonButton(
      onPressed: () => dopNativeCompleteOnboardingCubit.onExitDOPFlow(),
      isWrapContent: false,
      style: dopNativeButtonStyles.primary(ButtonSize.medium),
      child: const Text(DOPNativeStrings.dopNativeReceiveVoucher),
    );
  }

  @visibleForTesting
  Widget buildNotActivatedCardTitle({bool isActiveCard = false}) {
    return DOPNativeCardStatusWidget(
      title: DOPNativeStrings.dopNativeCardIssuedTitle,
      description: DOPNativeStrings.dopNativeCardIssuedDescription,
      bannerTitle: isActiveCard
          ? DOPNativeStrings.dopNativeActiveEvoNow
          : DOPNativeStrings.dopNativeDownloadTPBankToActive,
      actionWidget: buildBannerCTAWidget(isActiveCard: isActiveCard),
    );
  }

  @visibleForTesting
  Widget buildBannerCTAWidget({bool isActiveCard = false}) {
    return CommonButton(
      onPressed: () {
        onCTABannerPress(isActiveCard: isActiveCard);
      },
      style: dopNativeButtonStyles.primary(ButtonSize.medium),
      child: Text(
          isActiveCard ? DOPNativeStrings.dopNativeActive : DOPNativeStrings.dopNativeDownload),
    );
  }

  @visibleForTesting
  void onCTABannerPress({bool isActiveCard = false}) {
    // Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/**********/S19.+Card+Activation
    if (isActiveCard) {
      dopUtilFunction.openPOSLimitDialog(onFailed: (String reason) {
        handleDopEvoApiError(ErrorUIModel(verdict: reason));
        return;
      });
    } else {
      dopUtilFunction.openTPBAppLink();
    }
  }

  @visibleForTesting
  Widget buildNotActivatedCardFooter({bool enableViewEContractCTA = false}) {
    return DOPNativeCardAcquisitionRelatedInfoWidget(
      enableViewEContractCTA: enableViewEContractCTA,
    );
  }

  @visibleForTesting
  Future<void> onHandleState(DOPNativeCardStatusState state) async {
    if (state is GetCardStatusLoading) {
      showDOPLoading();
      return;
    }

    hideDOPLoading();

    if (state is GetCardStatusBlocked) {
      DOPNativeCardCICBlockedScreen.pushReplacementNamed();
      return;
    }

    if (state is GetCardStatusFailure) {
      handleDopEvoApiError(state.error);
      return;
    }
  }
}
