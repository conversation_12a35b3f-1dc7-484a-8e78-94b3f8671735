import 'package:flutter/material.dart';

import '../../../resources/resources.dart';
import '../resources/dop_native_images.dart';
import '../resources/dop_native_resources.dart';

class DOPNativeCheckBox extends StatelessWidget {
  const DOPNativeCheckBox({
    required this.value,
    this.onTap,
    super.key,
  });

  final bool value;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 20,
        height: 20,
        padding: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: value
                ? dopNativeColors.dopNativeCheckBoxBorderChecked
                : dopNativeColors.dopNativeCheckBoxBorderUnChecked,
          ),
          color: value
              ? dopNativeColors.dopNativeCheckBoxChecked
              : dopNativeColors.dopNativeCheckBoxUnChecked,
        ),
        child: value
            ? evoImageProvider.asset(
                DOPNativeImages.icCheck,
              )
            : const SizedBox(),
      ),
    );
  }
}
