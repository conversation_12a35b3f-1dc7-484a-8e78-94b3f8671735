import 'package:flutter/material.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

import '../resources/dop_native_resources.dart';

class DOPNativeGradientText extends StatelessWidget {
  DOPNativeGradientText({
    required this.text,
    required this.textStyle,
    List<Color>? colors,
    this.maxLines,
    this.overflow,
    super.key,
  }) : colors = colors ??
            <Color>[
              dopNativeColors.textGradientStart,
              dopNativeColors.textGradientEnd,
            ];

  final String text;
  final TextStyle textStyle;
  final List<Color> colors;
  final int? maxLines;
  final TextOverflow? overflow;

  @override
  Widget build(BuildContext context) {
    return GradientText(
      text,
      style: textStyle,
      colors: colors,
      gradientDirection: GradientDirection.ttb,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}
