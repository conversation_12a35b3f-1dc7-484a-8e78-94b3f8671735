import 'package:flutter_common_package/base/bloc_state.dart';

abstract class DOPNativePdfReviewState implements BlocState {}

class DOPNativePdfReviewInitial extends DOPNativePdfReviewState {}

class DOPNativePdfReviewLoading extends DOPNativePdfReviewState {
  final String? url;

  DOPNativePdfReviewLoading(this.url);
}

class DOPNativePdfReviewLoaded extends DOPNativePdfReviewState {
  final String? url;

  DOPNativePdfReviewLoaded(this.url);
}

class DOPNativePdfReviewError extends DOPNativePdfReviewState {}
