import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../resources/resources.dart';
import '../features/econtract_download/dop_native_econtract_download_screen.dart';
import '../features/web_view/dop_native_webview_screen.dart';
import '../resources/dop_native_images.dart';
import '../resources/dop_native_resources.dart';
import '../resources/dop_native_ui_strings.dart';
import '../resources/dop_native_website_url.dart';
import '../util/dop_functions.dart';

/// The widget contains:
/// - E-contract view detail CTA
/// - TPB contact
/// - TPB warning
class DOPNativeCardAcquisitionRelatedInfoWidget extends StatelessWidget {
  final bool enableViewEContractCTA;
  final bool enableViewTPBWarning;

  const DOPNativeCardAcquisitionRelatedInfoWidget({
    this.enableViewEContractCTA = true,
    this.enableViewTPBWarning = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        if (enableViewEContractCTA) ...<Widget>[
          _buildEContract(),
          const SizedBox(height: 4),
        ],
        _buildTPBContact(),
        const SizedBox(height: 20),
        enableViewTPBWarning ? _buildTPBWarning() : SizedBox.shrink(),
      ],
    );
  }

  Widget _buildEContract() {
    return GestureDetector(
      onTap: _onViewEContract,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10),
        color: Colors.transparent,
        child: Text(
          DOPNativeStrings.viewEContract,
          style: dopNativeTextStyles.bodyMedium(dopNativeColors.primary).copyWith(
                fontWeight: FontWeight.w700,
              ),
        ),
      ),
    );
  }

  Widget _buildTPBContact() {
    return Text.rich(
      textAlign: TextAlign.center,
      TextSpan(
        style: dopNativeTextStyles.h200(),
        children: <TextSpan>[
          const TextSpan(text: '${DOPNativeStrings.tpbContact} '),
          TextSpan(
            text: ContactInfo.tpbHotline.applyStringFormat(
              suffixGroup: 4,
              stringFormatType: StringFormatType.phone,
            ),
            style: dopNativeTextStyles.h200(color: dopNativeColors.primary),
          ),
          const TextSpan(text: ' ${DOPNativeStrings.dopNativeOr}\n'),
          TextSpan(
            text: ContactInfo.dopSupportPhone.applyStringFormat(
              prefixGroup: 4,
              stringFormatType: StringFormatType.phone2digits,
            ),
            style: dopNativeTextStyles.h200(color: dopNativeColors.primary),
          ),
        ],
      ),
    );
  }

  Widget _buildTPBWarning() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: dopNativeColors.background,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            DOPNativeStrings.dopNativeTPBWarning,
            style: dopNativeTextStyles.h300(color: dopNativeColors.textActive).copyWith(
                  height: 1.5,
                  letterSpacing: -0.02,
                ),
          ),
          const SizedBox(height: 16),
          _buildTextWithIcon(
            iconImage: DOPNativeImages.icRejectWithdrawMoneyInvitation,
            text: DOPNativeStrings.dopNativeTPBWarningRejectMoneyWithdrawInvitation,
          ),
          const SizedBox(height: 16),
          _buildTextWithIcon(
            iconImage: DOPNativeImages.icProtectCVV,
            text: DOPNativeStrings.dopNativeTPBWarningProtectCVV,
          ),
          GestureDetector(
            onTap: _viewProtectCVVDetail,
            child: Container(
              padding: const EdgeInsets.only(top: 16),
              color: Colors.transparent,
              child: Text.rich(
                TextSpan(
                  style: dopNativeTextStyles.h200(color: dopNativeColors.textActive),
                  children: <TextSpan>[
                    const TextSpan(
                        text: '${DOPNativeStrings.dopNativeTPBWarningProtectCVVViewDetail}: '),
                    TextSpan(
                      text: DOPNativeStrings.dopNativeTPBWarningProtectCVVViewDetailHere,
                      style: dopNativeTextStyles.bodyMedium(dopNativeColors.primary),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextWithIcon({
    required String iconImage,
    required String text,
  }) {
    return Row(
      children: <Widget>[
        evoImageProvider.asset(
          iconImage,
          width: 20,
          height: 20,
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            text,
            style: dopNativeTextStyles.bodyMedium(dopNativeColors.textPassive),
          ),
        ),
      ],
    );
  }

  void _viewProtectCVVDetail() {
    dopUtilFunction.openDOPWebView(DOPNativeWebViewArg(url: DOPNativeWebsiteUrl.tpbWarning));
  }

  void _onViewEContract() {
    DOPNativeEContractDownloadScreen.pushNamed();
  }
}
