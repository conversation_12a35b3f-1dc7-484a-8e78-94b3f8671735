import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../resources/dop_native_resources.dart';
import 'dop_native_notes_container_item.dart';

class DOPNativeNotesContainerItemWidget extends StatelessWidget {
  final DOPNativeNotesContainerItem item;

  const DOPNativeNotesContainerItemWidget({required this.item, super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Row(
        children: <Widget>[
          evoImageProvider.asset(
            item.imgAsset,
            fit: BoxFit.cover,
            height: 32,
          ),
          const SizedBox(width: 16),
          Text(
            item.description,
            style: dopNativeTextStyles.h200(
              color: dopNativeColors.dopNativeNoteContainerItemTextColor,
            ),
          ),
        ],
      ),
    );
  }
}
