import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:video_player/video_player.dart';

abstract class DOPNativeVideoPlayerState extends BlocState {}

class DOPNativeVideoPlayerInitial extends DOPNativeVideoPlayerState {}

class DOPNativeVideoPlayerLoading extends DOPNativeVideoPlayerState {
  final VideoPlayerController controller;

  DOPNativeVideoPlayerLoading({required this.controller});
}

class DOPNativeVideoPlayerLoaded extends DOPNativeVideoPlayerState {
  final VideoPlayerController controller;
  final bool isPlaying;

  DOPNativeVideoPlayerLoaded({required this.controller, required this.isPlaying});
}

class DOPNativeVideoPlayerError extends DOPNativeVideoPlayerState {}
