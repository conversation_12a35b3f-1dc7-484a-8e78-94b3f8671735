import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/in_app_update/common_in_app_update.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../data/repository/common_repo.dart';
import '../../data/response/force_update_entity.dart';
import '../../model/evo_dialog_id.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/evo_flutter_wrapper.dart';
import '../../util/mock_file_name_utils/mock_common_file_name.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../util/secure_storage_helper/secure_storage_helper_impl.dart';
import '../../util/ui_utils/evo_dialog_helper.dart';
import '../../widget/evo_overlay/evo_overlay_util_functions.dart';
import '../logging/evo_logging_event.dart';
import 'force_update_ui_model.dart';

mixin CheckForceUpdateHandlerMixin {
  static const String urlStoreAndroid =
      'https://play.google.com/store/apps/details?id=vn.goevo.evo';
  static const String urlStoreIos = 'https://apps.apple.com/app/id/**********';

  final EvoLocalStorageHelper _localStorageHelper = getIt.get<EvoLocalStorageHelper>();
  final CommonRepo _commonRepo = getIt.get<CommonRepo>();
  final InAppUpdateWrapper _inAppUpdateWrapper = getIt.get<InAppUpdateWrapper>();
  final LoggingRepo _loggingRepo = getIt.get<LoggingRepo>();

  Future<void> checkForceUpdate() async {
    final ForceUpdateEntity forceUpdateEntity = await _commonRepo.getForceUpdate(
      mockConfig: MockConfig(
        enable: false,
        fileName: getForceUpdateMockFileName(),
      ),
    );

    if (forceUpdateEntity.statusCode == CommonHttpClient.SUCCESS) {
      if (forceUpdateEntity.hasNewerVersion == true) {
        await handleAppUpdate(forceUpdateEntity);
      } else {
        await _localStorageHelper.delete(key: EvoSecureStorageHelperImpl.latestVersionIgnore);
      }
    }
  }

  @visibleForTesting
  Future<void> handleAppUpdate(ForceUpdateEntity entity) async {
    final AppState appState = getIt<AppState>();
    if (entity.latestVersion == appState.appVersion) {
      return;
    }

    final bool isUpdateAvailable = await checkInAppUpdateIfNeed();

    if (!isUpdateAvailable) {
      return;
    }

    if (entity.forceToUpdate == true) {
      await handleForceUpdate();
    } else {
      await handleFlexibleUpdate(entity.latestVersion);
    }
  }

  @visibleForTesting
  Future<bool> checkInAppUpdateIfNeed() async {
    /// Always return true for iOS
    /// Because iOS not support in-app update
    if (evoFlutterWrapper.isIOS()) {
      return true;
    }

    return await _inAppUpdateWrapper.isUpdateAvailable();
  }

  @visibleForTesting
  Future<void> handleForceUpdate() async {
    if (evoFlutterWrapper.isIOS()) {
      await showRequestUpdateUI(isForceUpdate: true);
    } else {
      await handleAndroidForceUpdate();
    }
  }

  @visibleForTesting
  Future<void> handleFlexibleUpdate(String? latestVersion) async {
    final bool isNotIgnoreVersion =
        await _localStorageHelper.getLatestVersionIgnore() != latestVersion;

    if (!isNotIgnoreVersion) {
      return;
    }

    if (evoFlutterWrapper.isIOS()) {
      await showRequestUpdateUI(onNegativeClick: () => handleIgnoreUpdate(latestVersion));
    } else {
      final AppUpdateResult result = await _inAppUpdateWrapper.startFlexibleUpdate();
      await processAndroidFlexibleUpdateResult(result, latestVersion);
    }
  }

  @visibleForTesting
  Future<void> handleAndroidForceUpdate() async {
    final AppUpdateResult result = await _inAppUpdateWrapper.startImmediateUpdate();
    await processAndroidForceUpdateResult(result);
  }

  @visibleForTesting
  Future<void> processAndroidFlexibleUpdateResult(
      AppUpdateResult result, String? latestVersion) async {
    switch (result) {
      case AppUpdateResult.userDeniedUpdate:
        handleIgnoreUpdate(latestVersion);
        break;
      case AppUpdateResult.inAppUpdateFailed:
        logUpdateFailure();
        break;
      case AppUpdateResult.success:
        await _inAppUpdateWrapper.completeFlexibleUpdate();
        break;
    }
  }

  @visibleForTesting
  Future<void> processAndroidForceUpdateResult(AppUpdateResult result) async {
    switch (result) {
      case AppUpdateResult.userDeniedUpdate:
        await showRequestUpdateUI(isForceUpdate: true);
        break;
      case AppUpdateResult.inAppUpdateFailed:
        logUpdateFailure();
        await showRequestUpdateUI(isForceUpdate: true);
        break;
      case AppUpdateResult.success:
        commonLog('In app update - force update success');
        break;
    }
  }

  @visibleForTesting
  void logUpdateFailure() {
    _loggingRepo.logEvent(
      eventType: EvoEventType.appUpdate,
      data: <String, dynamic>{
        'error': 'In app update failed',
      },
    );
  }

  @visibleForTesting
  Future<void> showRequestUpdateUI({
    bool isForceUpdate = false,
    VoidCallback? onNegativeClick,
  }) async {
    final ForceUpdateUIModel forceUpdateUIModel = createForceUpdateUIModel(
      isForceUpdate: isForceUpdate,
      onNegativeClick: onNegativeClick,
    );

    /// With isForceUpdate = true
    /// We will show a overlay that the user can't dismiss by deep link
    if (isForceUpdate) {
      return evoOverlayUtilFunctions.showForceUpdateOverlay(forceUpdateUIModel);
    }

    /// With isForceUpdate = false
    /// We'll show a bottom sheet that the user can dismiss (tap outside, click physical back button)
    /// isDismiss = true
    return EvoDialogHelper().showDialogBottomSheet(
      dialogId: forceUpdateUIModel.dialogId,
      header: forceUpdateUIModel.imageHeader,
      title: forceUpdateUIModel.title,
      textNegative: forceUpdateUIModel.textNegative,
      textPositive: forceUpdateUIModel.textPositive,
      content: forceUpdateUIModel.content,
      onClickNegative: forceUpdateUIModel.onClickNegative,
      onClickPositive: forceUpdateUIModel.onClickPositive,
    );
  }

  @visibleForTesting
  ForceUpdateUIModel createForceUpdateUIModel({
    bool isForceUpdate = false,
    VoidCallback? onNegativeClick,
  }) {
    final Widget imageHeader = evoImageProvider.asset(
      EvoImages.bgAppUpdate,
      fit: BoxFit.fitWidth,
    );

    return ForceUpdateUIModel(
      imageHeader: imageHeader,
      dialogId: EvoDialogId.newAppVersionBottomSheet,
      title: EvoStrings.forceUpdateSubDesc,
      content: EvoStrings.forceUpdateDescription,
      textNegative: isForceUpdate ? null : EvoStrings.forceUpdateSkip,
      textPositive: EvoStrings.forceUpdateAgree,
      onClickNegative: () {
        onNegativeClick?.call();
      },
      onClickPositive: () {
        handleOpenStore();
      },
    );
  }

  @visibleForTesting
  Future<void> handleOpenStore() async {
    if (evoFlutterWrapper.isIOS()) {
      commonUtilFunction.commonLaunchUrlString(urlStoreIos,
          mode: CommonLaunchUrlMode.externalApplication);
    } else if (evoFlutterWrapper.isAndroid()) {
      commonUtilFunction.commonLaunchUrlString(urlStoreAndroid,
          mode: CommonLaunchUrlMode.externalApplication);
    }
  }

  @visibleForTesting
  Future<void> handleIgnoreUpdate(String? latestVersion) async {
    await _localStorageHelper.setLatestVersionIgnore(latestVersion);
    navigatorContext?.pop();
  }
}
