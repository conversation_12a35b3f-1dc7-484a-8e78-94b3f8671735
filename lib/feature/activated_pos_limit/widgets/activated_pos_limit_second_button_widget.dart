import 'package:flutter/material.dart';

import '../../../resources/resources.dart';

class ActivatedPOSLimitSecondButtonWidget extends StatelessWidget {
  const ActivatedPOSLimitSecondButtonWidget({
    required this.title,
    required this.onPressed,
    super.key,
  });

  final String title;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 14),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              title,
              style: evoTextStyles.bodyMedium(EvoColors().activatedCardGuideColor),
            ),
            const SizedBox(width: 8),
            evoImageProvider.asset(
              EvoImages.icActiveCardArrowRight,
              width: 16,
              height: 16,
            ),
          ],
        ),
      ),
    );
  }
}
