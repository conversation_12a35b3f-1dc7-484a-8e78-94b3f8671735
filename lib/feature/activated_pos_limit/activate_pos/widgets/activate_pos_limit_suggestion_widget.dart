import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../cubit/activate_pos_suggestion_cubit.dart';
import 'activate_pos_suggestion_list_widget.dart';

class ActivatePosLimitSuggestionWidget extends StatefulWidget {
  final void Function(int? amount)? onPressItemSuggestion;
  final String? amountUserInputted;
  final int posLimit;

  const ActivatePosLimitSuggestionWidget({
    required this.posLimit,
    this.onPressItemSuggestion,
    this.amountUserInputted,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => ActivatePosLimitSuggestionWidgetState();
}

@visibleForTesting
class ActivatePosLimitSuggestionWidgetState extends State<ActivatePosLimitSuggestionWidget> {
  @visibleForTesting
  final ActivatePosSuggestionCubit cubit = ActivatePosSuggestionCubit();
  final AppState _appState = getIt.get<AppState>();

  @override
  void initState() {
    super.initState();
    cubit.generateSuggestions(
      posLimit: widget.posLimit,
      orderAmount: _appState.activatedPOSLimitState.orderAmount,
      userInput: widget.amountUserInputted,
    );
  }

  @override
  void didUpdateWidget(ActivatePosLimitSuggestionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    cubit.generateSuggestions(
      posLimit: widget.posLimit,
      orderAmount: _appState.activatedPOSLimitState.orderAmount,
      userInput: widget.amountUserInputted,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ActivatePosSuggestionCubit>(
      create: (_) => cubit,
      child: BlocBuilder<ActivatePosSuggestionCubit, ActivatePOSSuggestionState>(
        buildWhen: (_, ActivatePOSSuggestionState state) {
          return state is ActivatePOSSuggestionGeneratedState;
        },
        builder: (_, ActivatePOSSuggestionState state) {
          final List<int> suggestion =
              state is ActivatePOSSuggestionGeneratedState ? state.suggestions : <int>[];

          if (suggestion.isEmpty) {
            return const SizedBox.shrink();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const SizedBox(height: 16),
              ActivatePosSuggestionListWidget(
                suggestions: suggestion,
                onSuggestionTap: (int suggestion) {
                  widget.onPressItemSuggestion?.call(suggestion);
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
