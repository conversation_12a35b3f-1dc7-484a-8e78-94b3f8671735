import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/webview/common_webview_controller.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../resources/resources.dart';
import '../../../data/repository/user_repo.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/evo_appbar.dart';
import '../../../widget/evo_appbar_leading_button.dart';
import '../../payment/utils/payment_navigate_helper_mixin.dart';
import '../../webview/models/evo_webview_arg.dart';
import '../activate_card_base/activate_card_state_base.dart';
import '../card_activation_status_cubit/card_activation_status_cubit.dart';
import '../card_activation_status_cubit/card_activation_status_handle_utils.dart';
import '../card_confirm_activation_cubit/card_confirm_activation_cubit.dart';
import '../models/platform_active_card.dart';
import '../utils/activate_pos_limit_constants.dart';

class ActivatePosLimitThreeDSecureScreenArg extends PageBaseArg {
  final String? url;

  ActivatePosLimitThreeDSecureScreenArg({
    required this.url,
  });
}

class ActivatePosLimitThreeDSecureScreen extends PageBase {
  final ActivatePosLimitThreeDSecureScreenArg? arg;

  static void pushNamed(String? url) {
    return navigatorContext?.pushNamed(
      Screen.activatePosLimitThreeDSecureScreen.name,
      extra: ActivatePosLimitThreeDSecureScreenArg(url: url),
    );
  }

  const ActivatePosLimitThreeDSecureScreen({
    this.arg,
    super.key,
  });

  @override
  State<ActivatePosLimitThreeDSecureScreen> createState() =>
      ActivatePosLimitThreeDSecureScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.activatePosLimitThreeDSecureScreen.routeName);
}

class ActivatePosLimitThreeDSecureScreenState
    extends ActivateCardStateBase<ActivatePosLimitThreeDSecureScreen>
    with PaymentNavigationHelperMixin {
  final CardActivationStatusCubit _cubit = CardActivationStatusCubit(
    userRepo: getIt.get<UserRepo>(),
  );

  final CardConfirmActivationCubit _confirmActivationCubit = CardConfirmActivationCubit(
    userRepo: getIt.get<UserRepo>(),
  );

  CommonWebViewController? webViewController;

  @override
  void initState() {
    super.initState();
    webViewController = CommonWebViewController(
      onRedirectUrl: (Uri? uri, _) {
        handleOnRedirectUrl(uri);
      },
    );
  }

  @override
  void handleBackButton() {
    showDialogConfirmCancel3DSActivatePosLimit(
      onClickPositive: () {
        EvoUiUtils().hideHudLoading();
        navigatorContext?.popUntilNamed(Screen.activatePosLimitScreen.name);
      },
      onClickNegative: () {
        navigatorContext?.pop();
      },
    );
  }

  @override
  bool get enableAppBar => false;

  @override
  Widget buildBody(BuildContext context) {
    return MultiBlocProvider(
      providers: <BlocProvider<dynamic>>[
        BlocProvider<CardActivationStatusCubit>(
          create: (_) => _cubit,
        ),
        BlocProvider<CardConfirmActivationCubit>(
          create: (_) => _confirmActivationCubit,
        ),
      ],
      child: MultiBlocListener(
        listeners: <BlocListener<dynamic, dynamic>>[
          BlocListener<CardActivationStatusCubit, CardActivationStatusState>(
            listener: (_, CardActivationStatusState state) {
              CardActivationStatusHandleUtils().handleCardStatusStateChange(
                state: state,
                onCardStatusActivated: () =>
                    _confirmActivationCubit.confirmActivationCard(platform: PlatformActiveCard.evo),
                onGetCardStatusError: (ErrorUIModel error) => handleEvoApiError(error),
              );
            },
          ),
          BlocListener<CardConfirmActivationCubit, CardConfirmActivationState>(
            listener: (_, CardConfirmActivationState state) {
              _handleConfirmActivation(state);
            },
          ),
        ],
        child: CommonWebView(
          arg: EvoWebViewArg(
            title: '',
            url: widget.arg?.url,
            appBar: EvoAppBar(
              leading: EvoAppBarLeadingButton(
                onPressed: () {
                  handleBackButton();
                },
              ),
            ),
            controller: webViewController,
            errorWidget: (_, __) => const SizedBox.shrink(),
          ),
        ),
      ),
    );
  }

  Future<void> handleOnRedirectUrl(Uri? uri) async {
    final bool isOTPInputtingDone =
        uri.toString().startsWith(ActivatePosLimitConstants.threeDSecureCallbackUrl);

    if (!isOTPInputtingDone) {
      return;
    }

    EvoUiUtils().showHudLoading();

    /// Save current time to local storage
    /// Check and show bottom sheet waiting in confirm and pay screen
    /// To avoid issue send TPB OTP two times in 60s
    getIt
        .get<EvoLocalStorageHelper>()
        .setLastTimeRequest3DSCardActivation(DateTime.now().millisecondsSinceEpoch);

    /// Wait 30s before check card status
    /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3787620432/ES+CO+Dive-in+Card+activation+within+payment+flow#TPB-redirects-to-the-EVO-app.
    await Future<void>.delayed(Duration(seconds: 30));

    _cubit.getCardActivationStatus();
  }

  void _handleConfirmActivation(CardConfirmActivationState state) {
    if (state is CardConfirmActivationLoadingState) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    if (state is CardConfirmActivationSucceedState) {
      activatedPOSLimitFlow.onSuccess(context: context);
      return;
    }

    if (state is CardConfirmActivationFailureState) {
      handleEvoApiError(state.errorUIModel);
      return;
    }
  }
}
