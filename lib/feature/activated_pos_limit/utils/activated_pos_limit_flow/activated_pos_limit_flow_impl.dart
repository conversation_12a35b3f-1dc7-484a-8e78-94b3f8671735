part of 'activated_pos_limit_flow.dart';

class ActivatedPOSLimitFlowImpl extends ActivatedPOSLimitFlow {
  @override
  void prepareActivatePOSLimitFlow({
    required Screen? screenName,
    required ActivatedPOSLimitFlowCallback callback,
    required int? orderAmount,
  }) {
    // Save Info to AppState
    final AppState appState = getIt.get<AppState>();
    appState.activatedPOSLimitState.entryPoint = screenName;
    appState.activatedPOSLimitState.callback = callback;
    appState.activatedPOSLimitState.orderAmount = orderAmount;
  }

  @override
  void onSuccess({
    required BuildContext context,
    ActivatedPOSLimitFlowPayload? payload,
  }) {
    final ActivatedPOSLimitState? activatedPOSLimitState = getActivatedPOSLimitState();
    final Function? onSuccess = activatedPOSLimitState?.callback?.onSuccess;

    popToEntryPoint(activatedPOSLimitState?.entryPoint);

    // clear state
    activatedPOSLimitState?.clear();
    onSuccess?.call(context, payload);
  }

  @override
  void onFailed({
    required BuildContext context,
    required ActivatedPOSLimitFlowFailedReason reason,
    String? userMessage,
  }) {
    final ActivatedPOSLimitState? activatedPOSLimitState = getActivatedPOSLimitState();
    final Function? onFailed = activatedPOSLimitState?.callback?.onFailed;

    popToEntryPoint(activatedPOSLimitState?.entryPoint);

    // clear state
    activatedPOSLimitState?.clear();

    onFailed?.call(context, reason, userMessage);
  }

  @visibleForTesting
  ActivatedPOSLimitState? getActivatedPOSLimitState() {
    final AppState appState = getIt.get<AppState>();
    return appState.activatedPOSLimitState;
  }

  @visibleForTesting
  void popToEntryPoint(Screen? entryPointScreen) {
    final Screen? currentScreen = entryPointScreen;
    if (currentScreen == null) {
      navigatorContext?.pop();
      return;
    }

    // Pop until entry point screen
    navigatorContext?.popUntilNamed(currentScreen.name);
  }
}
