import 'package:flutter/material.dart';

import '../../../../../../data/response/cta_widget_config_entity.dart';
import '../../../../../../resources/resources.dart';

class CardCtaNoticeWidget extends StatelessWidget {
  final CtaWidgetConfigEntity? ctaWidgetConfig;
  final VoidCallback? onReloadClick;

  const CardCtaNoticeWidget({super.key, this.ctaWidgetConfig, this.onReloadClick});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        const SizedBox(width: EvoDimension.profilePageHorizontalMargin),
        evoImageProvider.asset(
          EvoImages.icCardStatusNotice,
          width: 16,
          fit: BoxFit.fitWidth,
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            _isNotLinkAndActivateCard()
                ? EvoStrings.cardStatusTwoRemainingSteps
                : EvoStrings.cardStatusOneRemainingStep,
            style: evoTextStyles.h200(
              color: evoColors.cardStatusRemainingSteps,
            ),
          ),
        ),
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onReloadClick,
            child: Column(
              children: <Widget>[
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.only(
                    left: 16,
                    right: EvoDimension.profilePageHorizontalMargin,
                  ),
                  child: evoImageProvider.asset(
                    EvoImages.icCardStatusReload,
                    width: 16,
                    fit: BoxFit.fitWidth,
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }

  bool _isNotLinkAndActivateCard() =>
      ctaWidgetConfig?.linkedCard != true && ctaWidgetConfig?.activatedCard != true;
}
