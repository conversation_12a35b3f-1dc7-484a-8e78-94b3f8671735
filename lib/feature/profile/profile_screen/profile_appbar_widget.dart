import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../data/response/user_information_entity.dart';
import '../../../resources/resources.dart';
import '../../../widget/evo_image_provider_widget.dart';
import '../model/gender.dart';
import '../profile_detail_screen/other_widgets/default_avatar_user_widget.dart';
import 'card_status/widget/toggleable_masked_text_widget.dart';

class ProfileAppbarWidget extends StatelessWidget implements PreferredSizeWidget {
  final Widget child;

  const ProfileAppbarWidget({required this.child, super.key});

  static const double appBarHeight = 72;

  @override
  Size get preferredSize => const Size(double.infinity, appBarHeight);

  @override
  Widget build(BuildContext context) {
    return child;
  }
}

class ProfileAppbarChildWidget extends StatelessWidget {
  final VoidCallback? onViewProfileDetailClick;
  final UserInformationEntity? user;
  static const double _avatarSize = 50;
  static const double _circularAvatar = 50;

  const ProfileAppbarChildWidget({
    super.key,
    this.onViewProfileDetailClick,
    this.user,
  });

  @override
  Widget build(BuildContext context) {
    final Gender? gender = Gender.formatGenderString(user?.gender);

    final Widget defaultAvatar = DefaultAvatarUserWidget(
      gender: gender,
      cornerRadius: _circularAvatar,
      sizeAvatar: _avatarSize,
    );

    return AppBar(
        toolbarHeight: ProfileAppbarWidget.appBarHeight,
        leadingWidth: 0,
        titleSpacing: 0,
        elevation: 0,
        backgroundColor: evoColors.background,
        title: InkWell(
            onTap: onViewProfileDetailClick,
            child: Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: EvoDimension.profilePageHorizontalMargin, vertical: 8),
                child: Row(children: <Widget>[
                  ClipRRect(
                    borderRadius: BorderRadius.circular(_circularAvatar),
                    child: (user?.avatarUrl?.isNotEmpty == true)
                        ? EvoNetworkImageProviderWidget(
                            user?.avatarUrl,
                            width: _avatarSize,
                            height: _avatarSize,
                            fit: BoxFit.cover,
                            errorWidget: defaultAvatar,
                          )
                        : defaultAvatar,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: _getUserInfoContent())),
                  Padding(
                      padding: const EdgeInsets.only(left: 10, top: 10, bottom: 10),
                      child: evoImageProvider.asset(EvoImages.icArrowRight,
                          width: 24, height: 24, color: evoColors.icon))
                ]))));
  }

  List<Widget> _getUserInfoContent() {
    final List<Widget> content = <Widget>[
      Text(user?.getDisplayName() ?? EvoStrings.unKnowUser,
          style: evoTextStyles.h500().copyWith(fontSize: 22))
    ];

    if (user?.hasFullName() == true) {
      content.add(ToggleableMaskedTextWidget(
          unmaskedText: user?.phoneNumber,
          maskedText: user?.phoneNumber
              ?.hiddenByFormat((user?.phoneNumber?.length ?? 0) - phoneNumberFormatNumOfLastShow)));
    }

    return content;
  }
}
