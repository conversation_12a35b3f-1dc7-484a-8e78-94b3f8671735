import 'package:flutter/material.dart';

import '../../../../data/response/linked_card_entity.dart';
import '../../../../widget/evo_inkwell_network_image.dart';
import '../../../linked_card_detail_screen/linked_card_detail_utils.dart';

class LinkedCardWidget extends StatelessWidget {
  final void Function(LinkedCardEntity?)? onTap;

  const LinkedCardWidget({
    required this.linkedCard,
    super.key,
    this.hasShowMoney = false,
    this.onTap,
  });

  final LinkedCardEntity? linkedCard;
  final bool hasShowMoney;

  @override
  Widget build(BuildContext context) {
    return EvoInkWellNetworkImage(
      linkedCard?.cardImage,
      onTap: onTap != null
          ? () {
              onTap?.call(linkedCard);
            }
          : null,
      width: LinkCardDetailUtil.getLinkedCardWidth(context),
      height: LinkCardDetailUtil.getLinkedCardHeight(context),
      fit: BoxFit.cover,
      cornerRadius: 16,
    );
  }
}
