import 'package:flutter/material.dart';

class EvoInkWellContainer extends StatelessWidget {
  final VoidCallback? onTap;
  final double height;
  final double? width;
  final double cornerRadius;
  final Widget child;

  const EvoInkWellContainer({
    required this.child,
    required this.height,
    super.key,
    this.width,
    this.cornerRadius = 0,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        child,

        /// Create a Layer for displaying Ripple effect when the user's tapping
        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(cornerRadius),
            onTap: onTap,
            child: SizedBox(height: height, width: width ?? double.infinity),
          ),
        ),
      ],
    );
  }
}
