part of 'profile_detail_page_cubit.dart';

abstract class ProfileDetailPageState extends BlocState {}

class ProfileDetailPageLoading extends ProfileDetailPageState {}

class ProfileDetailPageInfoLoaded extends ProfileDetailPageState {
  final UserInformationEntity? user;

  ProfileDetailPageInfoLoaded({this.user});
}

class ProfileDetailPageFail extends ProfileDetailPageState {
  final ErrorUIModel? error;

  ProfileDetailPageFail({this.error});
}
