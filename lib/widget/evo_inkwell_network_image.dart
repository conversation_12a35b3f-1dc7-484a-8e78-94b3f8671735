import 'package:flutter/material.dart';

import 'dynamic_height_carousel_slider/size_reporting_widget.dart';
import 'evo_image_provider_widget.dart';

class EvoInkWellNetworkImage extends StatefulWidget {
  final TypeImage typeImage;
  final String? imageUrl;
  final double? width;
  final double? height;
  final Color? color;
  final BoxFit fit;
  final double cornerRadius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final VoidCallback? onTap;

  const EvoInkWellNetworkImage(
    this.imageUrl, {
    super.key,
    this.onTap,
    this.typeImage = TypeImage.thumbnail,
    this.width,
    this.height,
    this.color,
    this.fit = BoxFit.contain,
    this.cornerRadius = 0,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<EvoInkWellNetworkImage> createState() => _EvoInkWellNetworkImageState();
}

class _EvoInkWellNetworkImageState extends State<EvoInkWellNetworkImage> {
  /// [_heightInkWell] is used to set height of Ripple effect when The user taps with [InkWell]
  /// If [widget.height] is not null, It will be equal to [widget.height]
  /// If [widget.height] is null, It will be calculated by [SizeReportingWidget]
  final ValueNotifier<double?> _heightInkWell = ValueNotifier<double>(0);

  @override
  void initState() {
    super.initState();
    final double? heightImage = widget.height;
    if (heightImage != null) {
      _heightInkWell.value = heightImage;
    }
  }

  @override
  void dispose() {
    _heightInkWell.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: <Widget>[
      SizeReportingWidget(
        onSizeChange: (Size size) {
          if (widget.height != null || _heightInkWell.value == size.height) {
            return;
          }

          _heightInkWell.value = size.height;
        },
        child: EvoNetworkImageProviderWidget(widget.imageUrl,
            typeImage: widget.typeImage,
            height: widget.height,
            width: widget.width,
            color: widget.color,
            fit: widget.fit,
            cornerRadius: widget.cornerRadius,
            placeholder: widget.placeholder,
            errorWidget: widget.errorWidget),
      ),

      /// Create a Layer for displaying Ripple effect when the user's tapping
      Material(
        color: Colors.transparent,
        child: ValueListenableBuilder<double?>(
          builder: (BuildContext context, double? value, Widget? child) {
            return InkWell(
                borderRadius: BorderRadius.circular(widget.cornerRadius),
                onTap: widget.onTap,
                child: SizedBox(height: value, width: widget.width));
          },
          valueListenable: _heightInkWell,
        ),
      ),
    ]);
  }
}
