import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

/// Calculate size of a widget in a screen
/// Refer: https://medium.com/flutterworld/flutter-how-to-get-the-height-of-the-widget-be4892abb1a2
class CalculationWidgetSizeWidget extends StatefulWidget {
  final Widget child;
  final Function onChange;

  const CalculationWidgetSizeWidget({
    required this.onChange,
    required this.child,
    super.key,
  });

  @override
  State<CalculationWidgetSizeWidget> createState() => _CalculationWidgetSizeWidgetState();
}

class _CalculationWidgetSizeWidgetState extends State<CalculationWidgetSizeWidget> {
  final GlobalKey<State<StatefulWidget>> widgetKey = GlobalKey();
  Size? oldSize;

  @override
  Widget build(BuildContext context) {
    SchedulerBinding.instance.addPostFrameCallback(postFrameCallback);
    return Container(
      key: widget<PERSON><PERSON>,
      child: widget.child,
    );
  }

  void postFrameCallback(_) {
    final BuildContext? context = widgetKey.currentContext;
    if (context == null) {
      return;
    }

    final Size? newSize = context.size;
    if (oldSize == newSize) {
      return;
    }

    oldSize = newSize;
    widget.onChange(newSize);
  }
}
