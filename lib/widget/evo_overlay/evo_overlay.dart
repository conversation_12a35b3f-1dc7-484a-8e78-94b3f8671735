import 'dart:async';

import 'package:flutter/material.dart';

import 'evo_overlay_widget.dart';
import 'widgets/evo_overlay_container.dart';
import 'widgets/evo_overlay_entry.dart';

abstract class EvoOverlay {
  @protected
  @visibleForTesting
  Widget? containerOverlay;
  @protected
  @visibleForTesting
  GlobalKey<EvoOverlayContainerState>? keyOverlay;
  @protected
  @visibleForTesting
  EvoOverlayEntry? overlayEntry;

  @mustCallSuper
  TransitionBuilder init({
    TransitionBuilder? builder,
  }) {
    return (BuildContext context, Widget? child) {
      if (builder != null) {
        return builder(
          context,
          _createEvoOverlayWidget(child),
        );
      } else {
        return _createEvoOverlayWidget(child);
      }
    };
  }

  Widget _createEvoOverlayWidget(Widget? child) {
    return EvoOverlayWidget(
      overlayChild: child,
      builder: (BuildContext context) {
        return containerOverlay ?? const SizedBox.shrink();
      },
      overlayEntryCallback: (EvoOverlayEntry entry) {
        overlayEntry = entry;
      },
    );
  }

  @mustCallSuper
  Future<void> show({
    Widget? overlayWidget,
    Color? maskColor,
    // callback when show without duplicated from the previous one
    VoidCallback? handleLogOnShowEvent,
  }) async {
    assert(
      overlayEntry != null,
      'You should call EvoOverlay.init() in your MaterialApp',
    );
    bool isDuplicated = false;
    if (keyOverlay != null) {
      isDuplicated = true;
      await dismiss();
    }

    keyOverlay = GlobalKey<EvoOverlayContainerState>();
    if (overlayWidget != null) {
      containerOverlay = EvoOverlayContainer(
        key: keyOverlay,
        overlayChild: overlayWidget,
        maskColor: maskColor,
      );
    }
    if (isDuplicated == false) {
      handleLogOnShowEvent?.call();
    }
    _markNeedsBuild();
  }

  @mustCallSuper
  Future<void> dismiss() async {
    _reset();
  }

  void _reset() {
    containerOverlay = null;
    keyOverlay = null;
    _markNeedsBuild();
  }

  void _markNeedsBuild() {
    overlayEntry?.markNeedsBuild();
  }
}
