import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import 'pdf_widget_state.dart';

class PdfWidgetCubit extends CommonCubit<PdfWidgetState> {
  @visibleForTesting
  String? pdfUrl;

  PdfWidgetCubit() : super(PdfWidgetInitial());

  void loadPdf(String? url) {
    pdfUrl = url;
    if (pdfUrl == null) {
      emit(PdfWidgetError(ErrorUIModel()));
      return;
    }
    emit(PdfWidgetLoading(pdfUrl));
  }

  void onPdfLoadFailed(String error) {
    if (state is! PdfWidgetError) {
      emit(PdfWidgetError(ErrorUIModel(userMessage: error)));
    }
  }
}
