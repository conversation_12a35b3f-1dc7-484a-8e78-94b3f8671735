import 'dart:async';

import 'package:flutter/material.dart';

part 'countdown_controller.dart';

class CountdownWidgetBuilder extends StatefulWidget {
  const CountdownWidgetBuilder({
    required this.builder,
    required this.controller,
    required this.durationAllProgress,
    this.durationDoEmitEvent,
    super.key,
  });

  final Widget Function(BuildContext context, Duration duration) builder;
  final CountdownController controller;
  final Duration durationAllProgress;
  final Duration? durationDoEmitEvent;

  @override
  State<CountdownWidgetBuilder> createState() => CountdownWidgetBuilderState();
}

@visibleForTesting
class CountdownWidgetBuilderState extends State<CountdownWidgetBuilder> {
  Timer? timer;
  ValueNotifier<Duration>? durationNotifier;
  final Duration _tickDuration = const Duration(seconds: 1);

  @override
  void initState() {
    super.initState();
    durationNotifier = ValueNotifier<Duration>(widget.durationAllProgress);
    _setupController();
  }

  @override
  void dispose() {
    _clearTimer();
    durationNotifier?.dispose();
    durationNotifier = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (durationNotifier != null) {
      return ValueListenableBuilder<Duration>(
        valueListenable: durationNotifier!,
        builder: (BuildContext context, Duration duration, _) {
          return widget.builder(
            context,
            duration,
          );
        },
      );
    }

    return const SizedBox.shrink();
  }

  void _setupController() {
    widget.controller.cancel = cancel;
    widget.controller.pause = _clearTimer;
    widget.controller.resume = _resume;
    widget.controller.start = _start;
  }

  @visibleForTesting
  void cancel() {
    _clearTimer();
    durationNotifier?.value = Duration.zero;
  }

  void _clearTimer() {
    timer?.cancel();
    timer = null;
  }

  void _start() {
    if (!mounted) {
      return;
    }

    durationNotifier?.value = widget.durationAllProgress;

    _resume();
  }

  Future<void> _resume() async {
    if (!mounted) {
      return;
    }

    _clearTimer();

    timer = Timer.periodic(_tickDuration, (_) => _onTick());
  }

  void _onTick() {
    final Duration durationValue = durationNotifier?.value ?? Duration.zero;
    final Duration next = durationValue - _tickDuration;

    _handleEmitEvent(next);

    if (next.isNegative) {
      cancel();
      widget.controller.onDone();
      return;
    }

    durationNotifier?.value = next;
  }

  void _handleEmitEvent(Duration next) {
    /// Check to emit to event
    /// Because we will count pooling time differently than the time display to the user
    final int? secondsInPooling = widget.durationDoEmitEvent?.inSeconds;
    if (secondsInPooling == null) {
      return;
    }

    final bool isTimeToEmitEvent = next.inSeconds != 0 && next.inSeconds % secondsInPooling == 0;
    final bool canEmitEvent = isTimeToEmitEvent && (widget.controller.emittingEventStatus != EmittingEventStatus.emitting);
    if (canEmitEvent) {
      widget.controller.emittingEventStatus = EmittingEventStatus.emitting;
      widget.controller.onEmitEventIfNeed?.call();
    }
  }
}
