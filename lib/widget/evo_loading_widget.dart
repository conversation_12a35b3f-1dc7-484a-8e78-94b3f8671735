import 'package:flutter/material.dart';

import '../resources/animations.dart';
import 'animation/lottie_animation_widget.dart';

class EvoLoadingWidget extends StatelessWidget {
  const EvoLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const FractionallySizedBox(
      widthFactor: 1.0,
      heightFactor: 1.0,
      child: AbsorbPointer(
        child: Center(
          child: LottieAnimationWidget(EvoAnimation.animationHubLoading),
        ),
      ),
    );
  }
}
