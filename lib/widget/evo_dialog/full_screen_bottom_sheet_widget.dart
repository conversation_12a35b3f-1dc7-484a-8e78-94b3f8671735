import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../resources/resources.dart';

class FullScreenBottomSheetWidget extends StatelessWidget {
  final Widget? content;
  final VoidCallback? onClose;

  const FullScreenBottomSheetWidget({
    super.key,
    this.content,
    this.onClose,
  });

  @visibleForTesting
  final double btnCloseSize = 40;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      height: getHeight(context),
      child: Column(
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: _buildCloseButton(context),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              child: Container(
                padding: EdgeInsets.only(bottom: navigatorContext?.screenPadding.bottom ?? 0),
                width: double.infinity,
                color: Colors.white,
                child: content,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @visibleForTesting
  double getHeight(BuildContext context) {
    /// Make sure close button always center of AppBar
    /// By default the bottom sheet with full screen config (isScrollControlled=true) will be equal screen device height.
    /// So we will get device height - statusBarHeight => Close button will be below status bar
    /// To make sure close button vertical center of AppBar,
    /// we need to subtraction the gap kToolbarHeight and _btnCloseSize: ((kToolbarHeight - _btnCloseSize) / 2)
    final double statusBarHeight = navigatorContext?.screenPadding.top ?? 0;
    return context.screenHeight - statusBarHeight - ((kToolbarHeight - btnCloseSize) / 2);
  }

  Widget _buildCloseButton(BuildContext context) {
    return Align(
      alignment: Alignment.topRight,
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(btnCloseSize),
        //  type: MaterialType.transparency,
        child: InkWell(
          borderRadius: BorderRadius.circular(btnCloseSize),
          onTap: () {
            onClose?.call();
            navigatorContext?.pop();
          },
          child: SizedBox(
            width: btnCloseSize,
            height: btnCloseSize,
            child: Icon(
              Icons.close,
              size: 20,
              color: evoColors.icon,
            ),
          ),
        ),
      ),
    );
  }
}
