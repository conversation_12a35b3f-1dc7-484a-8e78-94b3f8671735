import 'dart:developer';

import 'package:chucker_flutter/chucker_flutter.dart';
import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/global.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:go_router/go_router.dart';

import '../../feature/activated_pos_limit/3d_secure/activate_pos_limit_three_d_secure_screen.dart';
import '../../feature/activated_pos_limit/activate_card_guidance/activate_card_guidance_screen.dart';
import '../../feature/activated_pos_limit/activate_card_introduction/activate_card_introduction_screen.dart';
import '../../feature/activated_pos_limit/activate_pos/activate_pos_limit_screen.dart';
import '../../feature/activated_pos_limit/setup_pos_limit_guidance/setup_pos_limit_guidance_screen.dart';
import '../../feature/activated_pos_limit/setup_pos_limit_introduction/setup_pos_limit_introduction_screen.dart';
import '../../feature/announcement/announcement_screen.dart';
import '../../feature/biometric/activate_biometric/active_biometric_page.dart';
import '../../feature/create_evo_card/create_evo_card_page.dart';
import '../../feature/delete_account/attention_notes/attention_notes_screen.dart';
import '../../feature/delete_account/delete_success/delete_account_success_screen.dart';
import '../../feature/delete_account/survey/delete_account_survey_screen.dart';
import '../../feature/delete_account/verify_pin/delete_account_verify_pin_screen.dart';
import '../../feature/dop_native/features/acquisition_reward/dop_native_acquisition_reward_screen.dart';
import '../../feature/dop_native/features/additional_form/dop_native_additional_form_screen.dart';
import '../../feature/dop_native/features/appraising_verification/dop_native_appraising_verification_screen.dart';
import '../../feature/dop_native/features/appraising_verification/dop_native_fourth_appraising_screen.dart';
import '../../feature/dop_native/features/appraising_verification/dop_native_three_step_appraising_verification_screen.dart';
import '../../feature/dop_native/features/card_activation_status/card_activated/dop_native_card_activated_screen.dart';
import '../../feature/dop_native/features/card_activation_status/card_activated_pos_failed/dop_native_card_activated_pos_failed_screen.dart';
import '../../feature/dop_native/features/card_activation_status/card_activated_retry_pos_limit/dop_native_card_activated_retry_pos_limit_screen.dart';
import '../../feature/dop_native/features/card_activation_status/card_active_fail/dop_native_card_active_fail_screen.dart';
import '../../feature/dop_native/features/card_activation_status/card_active_retry/dop_native_card_active_retry_screen.dart';
import '../../feature/dop_native/features/card_activation_status/card_status_information/dop_native_card_status_information_screen.dart';
import '../../feature/dop_native/features/cif_confirm/dop_native_cif_confirm_screen.dart';
import '../../feature/dop_native/features/cif_confirm/dop_native_cif_no_branch_screen.dart';
import '../../feature/dop_native/features/collect_location/dop_native_collect_location_screen.dart';
import '../../feature/dop_native/features/e_success/cic_holding/dop_native_e_success_cic_holding_screen.dart';
import '../../feature/dop_native/features/e_success/dop_native_e_success_screen.dart';
import '../../feature/dop_native/features/e_success/dop_native_e_success_semi_screen.dart';
import '../../feature/dop_native/features/econtract_download/dop_native_econtract_download_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/ekyc_limit_exceed/dop_native_ekyc_limit_exceed_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/face_otp/face_otp_introduction/dop_native_face_otp_introduction_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/face_otp/face_otp_retry/dop_native_face_otp_retry_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/face_otp/face_otp_success/dop_native_face_otp_success_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/id_capture_introduction/dop_native_id_capture_introduction_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/id_card_back_side_verification/dop_native_id_card_back_side_verification_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/id_card_front_side_verification/dop_native_id_card_front_side_verification_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/id_card_qr_code_verification/dop_native_id_card_qr_code_verification_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/id_card_success/dop_native_id_success_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/id_verification_confirm/additional_info_screen/dop_native_ekyc_confirm_additional_info_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/id_verification_confirm/dop_native_ekyc_confirm_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/id_verification_confirm/single/dop_native_single_ekyc_confirm_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/fpt/dop_native_fpt_nfc_reader_introduction_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/tv_ekyc/dop_native_tv_nfc_reader_introduction_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/unsupported/dop_native_nfc_device_unsupported_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/selfie/active/dop_native_selfie_active_introduction_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/selfie/flash/dop_native_selfie_flash_introduction_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/selfie/selfie_verification/dop_native_selfie_verification_screen.dart';
import '../../feature/dop_native/features/ekyc_ui_only/selfie/selfie_verify_success/dop_native_selfie_verify_success_screen.dart';
import '../../feature/dop_native/features/esign/esign_review/america_citizen/dop_native_america_citizen_screen.dart';
import '../../feature/dop_native/features/esign/esign_review/dop_native_esign_review_screen.dart';
import '../../feature/dop_native/features/esign/intro/dop_native_e_sign_intro_mwg_screen.dart';
import '../../feature/dop_native/features/esign/intro/dop_native_e_sign_intro_screen.dart';
import '../../feature/dop_native/features/failure_screen/dop_native_failure_screen.dart';
import '../../feature/dop_native/features/inform_success/dop_native_inform_success_auto_cic_screen.dart';
import '../../feature/dop_native/features/inform_success/dop_native_inform_success_auto_pcb_screen.dart';
import '../../feature/dop_native/features/inform_success/dop_native_inform_success_semi_screen.dart';
import '../../feature/dop_native/features/inform_success/dop_native_inform_success_sophia_screen.dart';
import '../../feature/dop_native/features/introduction/dop_native_introduction_screen.dart';
import '../../feature/dop_native/features/introduction/sub_introduction/dop_native_sub_introduction_screen.dart';
import '../../feature/dop_native/features/pdf_view/dop_native_pdf_screen.dart';
import '../../feature/dop_native/features/qrcode_scanner/non_login_qr_code_scanner_screen.dart';
import '../../feature/dop_native/features/salesman/dop_native_salesman_id_confirm_screen.dart';
import '../../feature/dop_native/features/salesman/dop_native_salesman_id_input_screen.dart';
import '../../feature/dop_native/features/status_screen/dop_native_status_screen.dart';
import '../../feature/dop_native/features/three_d_secure/dop_native_three_d_secure_screen.dart';
import '../../feature/dop_native/features/underwriting_card_status/dop_native_underwriting_card_status_screen.dart';
import '../../feature/dop_native/features/underwriting_sub_flow/dop_native_card_cic_blocked_screen.dart';
import '../../feature/dop_native/features/underwriting_sub_flow/underwriting_card_issued/dop_native_underwriting_card_issued_screen.dart';
import '../../feature/dop_native/features/underwriting_sub_flow/underwriting_inprogress/dop_native_underwriting_in_progress_screen.dart';
import '../../feature/dop_native/features/verify_otp/dop_native_verify_otp_screen.dart';
import '../../feature/dop_native/features/web_view/dop_native_webview_screen.dart';
import '../../feature/ekyc/ekyc_error_screen/ekyc_error_screen.dart';
import '../../feature/ekyc/face_otp/error_screen/face_otp_matching_error_screen.dart';
import '../../feature/ekyc/face_otp/face_matching/face_otp_matching_process_screen.dart';
import '../../feature/ekyc/face_otp/instruction/face_otp_instruction_screen.dart';
import '../../feature/ekyc/face_otp/starter_screen/face_otp_starter_screen.dart';
import '../../feature/ekyc_v2/error_screen/ekyc_v2_error_screen.dart';
import '../../feature/ekyc_v2/face_auth/error_screen/face_auth_matching_error_screen.dart';
import '../../feature/ekyc_v2/face_auth/face_matching/face_auth_matching_process_screen.dart';
import '../../feature/ekyc_v2/face_auth/instruction/face_auth_instruction_screen.dart';
import '../../feature/ekyc_v2/face_auth/starter_screen/face_auth_starter_screen.dart';
import '../../feature/emi_management/detail_screen/emi_management_detail_screen.dart';
import '../../feature/emi_management/management_screen/emi_management_screen.dart';
import '../../feature/error_screen/error_screen.dart';
import '../../feature/feature_toggle.dart';
import '../../feature/feedback_screen/feedback_screen.dart';
import '../../feature/home_screen/non_user/v2/story/widgets/story_web_view.dart';
import '../../feature/linked_card_detail_screen/linked_card_detail_screen.dart';
import '../../feature/logging/evo_navigator_observer.dart';
import '../../feature/login/new_device/input_phone_number/input_phone_number_page.dart';
import '../../feature/login/old_device/login_on_old_device_screen.dart';
import '../../feature/main_screen/main_screen.dart';
import '../../feature/maintenance/maintenance_screen.dart';
import '../../feature/manual_link_card/dop_status/dop_card_status_screen.dart';
import '../../feature/manual_link_card/pre_face_auth/pre_face_auth_screen.dart';
import '../../feature/manual_link_card/pre_face_otp/pre_face_otp_screen.dart';
import '../../feature/manual_link_card/result/manual_link_card_result_screen.dart';
import '../../feature/manual_link_card/submission_status_polling/link_card_submission_status_polling_screen.dart';
import '../../feature/manual_link_card/three_d_polling/link_card_three_d_polling_screen.dart';
import '../../feature/manual_link_card/three_d_secure/link_card_three_d_secure_screen.dart';
import '../../feature/payment/confirm_payment/clone_confirm_payment_screen/clone_confirm_payment_screen.dart';
import '../../feature/payment/confirm_payment/confirm_payment_fail_screen/confirm_payment_fail_screen.dart';
import '../../feature/payment/confirm_payment/update_confirm_payment_screen/update_confirm_payment_screen.dart';
import '../../feature/payment/emi_not_support_screen/emi_not_support_screen.dart';
import '../../feature/payment/emi_option_screen/emi_option_screen.dart';
import '../../feature/payment/input_amount/emi_input_amount/emi_input_amount_screen.dart';
import '../../feature/payment/input_amount/full_payment_input_amount/full_payment_input_amount_screen.dart';
import '../../feature/payment/promotion/payment_promotion_list_screen.dart';
import '../../feature/payment/qrcode_scanner/qrcode_scanner_screen.dart';
import '../../feature/payment/result/payment_result_screen.dart';
import '../../feature/payment/three_d/three_d_secure_screen.dart';
import '../../feature/payment/three_d_polling/three_d_polling_screen.dart';
import '../../feature/pin/create_pin/create_pin_page.dart';
import '../../feature/pin/input_pin/input_pin_screen.dart';
import '../../feature/privacy_policy/privacy_policy_screen.dart';
import '../../feature/profile/profile_detail_screen/profile_detail_page.dart';
import '../../feature/profile/profile_screen/profile_page.dart';
import '../../feature/profile/profile_settings/profile_settings_page.dart';
import '../../feature/referral_program/referral_sharing/referral_sharing_screen.dart';
import '../../feature/splash_screen/splash_screen.dart';
import '../../feature/transaction_detail/transaction_detail_screen.dart';
import '../../feature/tutorial/tutorial_screen.dart';
import '../../feature/verify_otp/verify_otp_page.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/global.dart';

final GoRouter evoRouter = GoRouter(
  initialLocation: Screen.splashScreen.routeName,
  navigatorKey: globalKeyProvider.navigatorKey,
  errorBuilder: (_, GoRouterState state) {
    /// Use-case #1: this callback is called to handle GoRouter.exception
    /// this **SplashScreen** will be displayed when GoRouter.exception is thrown
    /// refer: https://pub.dev/documentation/go_router/latest/topics/Error%20handling-topic.html
    log('evo_router: error builder: ${state.uri.path}');

    return const SplashScreen();
  },
  //Custom codec to fix issue with GoRouter and Widget Inspector, only needed in debug mode.
  extraCodec: kDebugMode ? GoRouterExtraCodecConverter() : null,
  observers: <NavigatorObserver>[
    getIt.get<CommonNavigatorObserver>(),
    getIt.get<EvoNavigatorObserver>(),
    // Firebase Analytics Observer - to send navigation events to Firebase Analytics
    getIt.get<FirebaseAnalyticsWrapper>().analyticsObserver,
    // Observer used by ChuckerFlutter to handle navigation for debugging (e.g., showing screens or dialogs)
    if (kDebugMode) ChuckerFlutter.navigatorObserver,
    DatadogNavigationObserver(datadogSdk: DatadogSdk.instance),
  ],
  routes: <GoRoute>[
    GoRoute(
      name: Screen.splashScreen.name,
      path: Screen.splashScreen.routeName,
      builder: (_, GoRouterState state) {
        return const SplashScreen();
      },
    ),
    GoRoute(
      name: Screen.mainScreen.name,
      path: Screen.mainScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is MainScreenArg) {
          final MainScreenArg arg = state.extra as MainScreenArg;

          return MainScreen(
            isLoggedIn: arg.isLoggedIn,
            initialPage: arg.initialPage,
            initialAction: arg.initialAction,
            initialPromotionTab: arg.initialPromotionTab,
            activateBiometricUseCase: arg.activateBiometricUseCase,
            key: arg.key,
          );
        } else {
          return ErrorPage(errMsg: '${Screen.mainScreen.name} need to pass argument');
        }
      },
    ),
    GoRoute(
      name: Screen.tutorialScreen.name,
      path: Screen.tutorialScreen.routeName,
      builder: (_, GoRouterState state) {
        return const TutorialScreen();
      },
    ),
    GoRoute(
      name: Screen.verifyOtpScreen.name,
      path: Screen.verifyOtpScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is VerifyOtpPageArg) {
          final VerifyOtpPageArg arg = state.extra as VerifyOtpPageArg;
          return VerifyOtpPage(
            phoneNumber: arg.phoneNumber,
            otpResendSecs: arg.otpResendSecs,
            onPopSuccess: arg.onPopSuccess,
            verifyOtpType: arg.verifyOtpType,
            sessionToken: arg.sessionToken,
          );
        } else {
          return ErrorPage(errMsg: '${Screen.mainScreen.name} need to pass argument');
        }
      },
    ),
    GoRoute(
      name: Screen.inputPhoneNumberScreen.name,
      path: Screen.inputPhoneNumberScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is InputPhoneNumberArg) {
          final InputPhoneNumberArg? arg = state.extra as InputPhoneNumberArg?;
          return InputPhoneNumberPage(arg: arg);
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.createEvoCardScreen.name,
      path: Screen.createEvoCardScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is CreateEvoCardArg) {
          final CreateEvoCardArg arg = state.extra as CreateEvoCardArg;
          return CreateEvoCardPage(action: arg.action);
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.feedbackScreen.name,
      path: Screen.feedbackScreen.routeName,
      builder: (_, GoRouterState state) {
        return const FeedBackScreen();
      },
    ),
    GoRoute(
      name: Screen.profileScreen.name,
      path: Screen.profileScreen.routeName,
      builder: (_, GoRouterState state) {
        return const ProfileScreen();
      },
    ),
    GoRoute(
      name: Screen.profileDetailScreen.name,
      path: Screen.profileDetailScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ProfileDetailPageArg) {
          final ProfileDetailPageArg arg = state.extra as ProfileDetailPageArg;
          return ProfileDetailPage(isFinishRegisterDOP: arg.isFinishRegisterDOP);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.createPinScreen.name,
      path: Screen.createPinScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is CreatePinArg) {
          final CreatePinArg arg = state.extra as CreatePinArg;
          return CreatePinScreen(
            phoneNumber: arg.phoneNumber,
            verifyOtpType: arg.verifyOtpType,
            sessionToken: arg.sessionToken,
          );
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.inputPinScreen.name,
      path: Screen.inputPinScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is InputPinArg) {
          final InputPinArg arg = state.extra as InputPinArg;
          return InputPinScreen(
            phoneNumber: arg.phoneNumber,
            sessionToken: arg.sessionToken,
            entryPoint: arg.entryPoint,
          );
        } else {
          return ErrorPage(errMsg: '${Screen.inputPinScreen.name} need to pass argument');
        }
      },
    ),
    GoRoute(
      name: Screen.loginOnOldDeviceScreen.name,
      path: Screen.loginOnOldDeviceScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is LoginOnOldDeviceScreenArg) {
          final LoginOnOldDeviceScreenArg arg = state.extra as LoginOnOldDeviceScreenArg;
          return LoginOnOldDeviceScreen(
            isAutoLoginWithBiometric: arg.isAutoLoginWithBiometric,
          );
        } else {
          return ErrorPage(errMsg: '${Screen.loginOnOldDeviceScreen.name} need to pass argument');
        }
      },
    ),
    GoRoute(
      name: Screen.activeBiometric.name,
      path: Screen.activeBiometric.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ActiveBiometricArg) {
          final ActiveBiometricArg arg = state.extra as ActiveBiometricArg;
          return ActiveBiometricScreen(pinCode: arg.pinCode);
        } else {
          return ErrorPage(errMsg: '${Screen.activeBiometric.name} need to pass argument');
        }
      },
    ),
    GoRoute(
      name: Screen.profileSettingScreen.name,
      path: Screen.profileSettingScreen.routeName,
      builder: (_, GoRouterState state) {
        return const ProfileSettingPage();
      },
    ),
    GoRoute(
      name: Screen.announcementListScreen.name,
      path: Screen.announcementListScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is AnnouncementArg) {
          final AnnouncementArg arg = state.extra as AnnouncementArg;
          return AnnouncementScreen(id: arg.id);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: CommonScreen.webViewPage.name,
      path: CommonScreen.webViewPage.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is CommonWebViewArg) {
          final CommonWebViewArg arg = state.extra as CommonWebViewArg;
          return CommonWebView(arg: arg);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.qrCodeScannerScreen.name,
      path: Screen.qrCodeScannerScreen.routeName,
      builder: (_, GoRouterState state) {
        return const QrCodeScannerScreen();
      },
    ),
    GoRoute(
      name: Screen.paymentInputAmount.name,
      path: Screen.paymentInputAmount.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is FullPaymentInputAmountArg) {
          final FullPaymentInputAmountArg arg = state.extra as FullPaymentInputAmountArg;
          return FullPaymentInputAmountScreen(
            store: arg.store,
            productCode: arg.productCode,
            vnPayQRInfo: arg.vnPayQRInfo,
            shouldShowEMIUnqualifiedBottomSheet: arg.shouldShowEMIUnqualifiedBottomSheet,
          );
        }

        if (state.extra is EmiInputAmountArg) {
          final EmiInputAmountArg arg = state.extra as EmiInputAmountArg;
          return EmiInputAmountScreen(
            store: arg.store,
            productCode: arg.productCode,
            vnPayQRInfo: arg.vnPayQRInfo,
          );
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.confirmPaymentFail.name,
      path: Screen.confirmPaymentFail.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ConfirmPaymentFailArg) {
          final ConfirmPaymentFailArg arg = state.extra as ConfirmPaymentFailArg;
          return ConfirmPaymentFailScreen(arg: arg);
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.threeDPolling.name,
      path: Screen.threeDPolling.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ThreeDPollingArg) {
          final ThreeDPollingArg arg = state.extra as ThreeDPollingArg;
          return ThreeDPollingScreen(orderSession: arg.orderSession);
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.threeDSecurePayment.name,
      path: Screen.threeDSecurePayment.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ThreeDPollingArg) {
          final ThreeDPollingArg arg = state.extra as ThreeDPollingArg;
          return ThreeDSecureScreen(orderSession: arg.orderSession);
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.paymentResultScreen.name,
      path: Screen.paymentResultScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is PaymentResultArg) {
          return PaymentResultScreen(arg: state.extra as PaymentResultArg);
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.paymentPromotionListScreen.name,
      path: Screen.paymentPromotionListScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is PaymentPromotionArg) {
          return PaymentPromotionListScreen(arg: state.extra as PaymentPromotionArg);
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.linkedCardDetailScreen.name,
      path: Screen.linkedCardDetailScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is LinkedCardDetailArgs) {
          return LinkedCardDetailScreen(
            linkedCard: (state.extra as LinkedCardDetailArgs).linkedCard,
          );
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopCardStatusScreen.name,
      path: Screen.dopCardStatusScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPCardStatusScreenArgs) {
          final DOPCardStatusScreenArgs arg = state.extra as DOPCardStatusScreenArgs;
          return DOPCardStatusScreen(linkedCardStatusModel: arg.linkedCardStatusModel);
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.cloneConfirmPayment.name,
      path: Screen.cloneConfirmPayment.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is CloneConfirmPaymentArg) {
          final CloneConfirmPaymentArg arg = state.extra as CloneConfirmPaymentArg;
          return CloneConfirmPaymentScreen(
            orderSessionId: arg.orderSessionId,
            emiPackage: arg.emiPackage,
            selectedVoucher: arg.selectedVoucher,
          );
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.transactionHistoryDetailScreen.name,
      path: Screen.transactionHistoryDetailScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is TransactionDetailArg) {
          return TransactionDetailScreen(
            arg: state.extra as TransactionDetailArg,
          );
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.linkCardThreeDPolling.name,
      path: Screen.linkCardThreeDPolling.routeName,
      builder: (_, GoRouterState state) {
        return const LinkCardThreeDPollingScreen();
      },
    ),
    GoRoute(
      name: Screen.linkCardThreeDSecureScreen.name,
      path: Screen.linkCardThreeDSecureScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is LinkCardThreeDSecureArg) {
          final LinkCardThreeDSecureArg arg = state.extra as LinkCardThreeDSecureArg;
          return LinkCardThreeDSecureScreen(
            linkCardThreeDSecureLink: arg.linkCardThreeDSecureLink,
            linkCardRequestId: arg.linkCardRequestId,
          );
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.manualLinkCardResultScreen.name,
      path: Screen.manualLinkCardResultScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ManualLinkCardResultScreenArg) {
          final ManualLinkCardResultScreenArg arg = state.extra as ManualLinkCardResultScreenArg;
          return ManualLinkCardResultScreen(
            resultModel: arg.resultModel,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.linkCardSubmissionStatusPollingScreen.name,
      path: Screen.linkCardSubmissionStatusPollingScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is LinkCardSubmissionStatusPollingArg) {
          final LinkCardSubmissionStatusPollingArg arg =
              state.extra as LinkCardSubmissionStatusPollingArg;
          return LinkCardSubmissionStatusPollingScreen(linkCardRequestId: arg.linkCardRequestId);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.updateConfirmPaymentScreen.name,
      path: Screen.updateConfirmPaymentScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is UpdateConfirmPaymentArg) {
          final UpdateConfirmPaymentArg arg = state.extra as UpdateConfirmPaymentArg;
          return UpdateConfirmPaymentScreen(
            orderSession: arg.orderSession,
            emiPackage: arg.emiPackage,
            selectedVoucher: arg.selectedVoucher,
            flowType: arg.flowType,
          );
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.faceOTPInstructionScreen.name,
      path: Screen.faceOTPInstructionScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is FaceOTPInstructionScreenArg) {
          final FaceOTPInstructionScreenArg arg = state.extra as FaceOTPInstructionScreenArg;
          return FaceOTPInstructionScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.faceOTPStarterScreen.name,
      path: Screen.faceOTPStarterScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is FaceOtpStarterScreenArg) {
          final FaceOtpStarterScreenArg arg = state.extra as FaceOtpStarterScreenArg;
          return FaceOtpStarterScreen(
            flowType: arg.flowType,
            callback: arg.callback,
            ekycSessionEntity: arg.ekycSessionEntity,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.faceOTPMatchingErrorScreen.name,
      path: Screen.faceOTPMatchingErrorScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is FaceOtpMatchingErrorScreenArg) {
          final FaceOtpMatchingErrorScreenArg arg = state.extra as FaceOtpMatchingErrorScreenArg;
          return FaceOtpMatchingErrorScreen(
            flowType: arg.flowType,
            errorUIModel: arg.errorUIModel,
            errorType: arg.errorType,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.faceOTPMatchingProcessScreen.name,
      path: Screen.faceOTPMatchingProcessScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is FaceOtpMatchingProcessScreenArg) {
          final FaceOtpMatchingProcessScreenArg arg =
              state.extra as FaceOtpMatchingProcessScreenArg;
          return FaceOtpMatchingProcessScreen(
            selfieImageId: arg.selfieImageId,
            flowType: arg.flowType,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.ekycErrorScreen.name,
      path: Screen.ekycErrorScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is EkycErrorScreenArg) {
          final EkycErrorScreenArg arg = state.extra as EkycErrorScreenArg;
          return EkycErrorScreen(
            errorReason: arg.errorReason,
            onActionButtonTap: arg.onActionButtonTap,
            onCloseButtonTap: arg.onCloseButtonTap,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.faceAuthInstructionScreen.name,
      path: Screen.faceAuthInstructionScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is FaceAuthInstructionScreenArg) {
          final FaceAuthInstructionScreenArg arg = state.extra as FaceAuthInstructionScreenArg;
          return FaceAuthInstructionScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.preFaceAuthManualLinkCardScreen.name,
      path: Screen.preFaceAuthManualLinkCardScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is PreFaceAuthManualLinkCardScreenArg) {
          final PreFaceAuthManualLinkCardScreenArg arg =
              state.extra as PreFaceAuthManualLinkCardScreenArg;
          return PreFaceAuthManualLinkCardScreen(
            callback: arg.callback,
            flowType: arg.flowType,
            sessionToken: arg.sessionToken,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.faceAuthStarterScreen.name,
      path: Screen.faceAuthStarterScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is FaceAuthStarterScreenArg) {
          final FaceAuthStarterScreenArg arg = state.extra as FaceAuthStarterScreenArg;
          return FaceAuthStarterScreen(
            flowType: arg.flowType,
            callback: arg.callback,
            sessionToken: arg.sessionToken,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.faceAuthMatchingErrorScreen.name,
      path: Screen.faceAuthMatchingErrorScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is FaceAuthMatchingErrorScreenArg) {
          final FaceAuthMatchingErrorScreenArg arg = state.extra as FaceAuthMatchingErrorScreenArg;
          return FaceAuthMatchingErrorScreen(
            flowType: arg.flowType,
            errorUIModel: arg.errorUIModel,
            errorType: arg.errorType,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.faceAuthMatchingProcessScreen.name,
      path: Screen.faceAuthMatchingProcessScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is FaceAuthMatchingProcessScreenArg) {
          final FaceAuthMatchingProcessScreenArg arg =
              state.extra as FaceAuthMatchingProcessScreenArg;
          return FaceAuthMatchingProcessScreen(
            facialVerificationResult: arg.facialVerificationResult,
            flowType: arg.flowType,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.ekycV2ErrorScreen.name,
      path: Screen.ekycV2ErrorScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is EkycV2ErrorScreenArg) {
          final EkycV2ErrorScreenArg arg = state.extra as EkycV2ErrorScreenArg;
          return EkycV2ErrorScreen(
            onTapRetryButton: arg.onTapRetryButton,
            onTapCloseButton: arg.onTapCloseButton,
            isAbleToRetry: arg.isAbleToRetry,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.preFaceOtpManualLinkCardScreen.name,
      path: Screen.preFaceOtpManualLinkCardScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is PreFaceOtpManualLinkCardScreenArg) {
          final PreFaceOtpManualLinkCardScreenArg arg =
              state.extra as PreFaceOtpManualLinkCardScreenArg;
          return PreFaceOtpManualLinkCardScreen(
            flowType: arg.flowType,
            callback: arg.callback,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.privatePolicyScreen.name,
      path: Screen.privatePolicyScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is PrivacyPolicyArg) {
          final PrivacyPolicyArg arg = state.extra as PrivacyPolicyArg;
          return PrivacyPolicyScreen(
            errorCode: arg.errorCode,
            privatePolicyEntity: arg.privatePolicyEntity,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.attentionNotesScreen.name,
      path: Screen.attentionNotesScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is AttentionNotesArg) {
          final AttentionNotesArg arg = state.extra as AttentionNotesArg;
          return AttentionNotesScreen(
            reasons: arg.reasons,
            sessionToken: arg.sessionToken,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.deleteAccountSuccessScreen.name,
      path: Screen.deleteAccountSuccessScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DeleteAccountSuccessScreen();
      },
    ),
    GoRoute(
      name: Screen.deleteAccountVerifyPinScreen.name,
      path: Screen.deleteAccountVerifyPinScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DeleteAccountVerifyPinArg) {
          final DeleteAccountVerifyPinArg arg = state.extra as DeleteAccountVerifyPinArg;
          return DeleteAccountVerifyPinScreen(sessionToken: arg.sessionToken);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.deleteAccountSurveyScreen.name,
      path: Screen.deleteAccountSurveyScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DeleteAccountSurveyArg) {
          final DeleteAccountSurveyArg arg = state.extra as DeleteAccountSurveyArg;
          return DeleteAccountSurveyScreen(
            reasons: arg.reasons,
            sessionToken: arg.sessionToken,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.referralSharingScreen.name,
      path: Screen.referralSharingScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ReferralSharingScreenArg) {
          final ReferralSharingScreenArg arg = state.extra as ReferralSharingScreenArg;
          return ReferralSharingScreen(
            campaignId: arg.campaignId,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.emiOptionScreen.name,
      path: Screen.emiOptionScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is EmiOptionScreenArg) {
          final EmiOptionScreenArg arg = state.extra as EmiOptionScreenArg;
          return EmiOptionScreen(
            arg: arg,
          );
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.emiNotSupportScreen.name,
      path: Screen.emiNotSupportScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is EmiNotSupportScreenArg) {
          final EmiNotSupportScreenArg arg = state.extra as EmiNotSupportScreenArg;
          return EmiNotSupportScreen(
            arg: arg,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.emiManagementListScreen.name,
      path: Screen.emiManagementListScreen.routeName,
      builder: (_, GoRouterState state) {
        return const EmiManagementScreen();
      },
    ),
    GoRoute(
      name: Screen.emiManagementDetailScreen.name,
      path: Screen.emiManagementDetailScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is EmiManagementDetailScreenArg) {
          final EmiManagementDetailScreenArg arg = state.extra as EmiManagementDetailScreenArg;
          return EmiManagementDetailScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.activateCardScreen.name,
      path: Screen.activateCardScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ActivateCardIntroductionScreenArg) {
          final ActivateCardIntroductionScreenArg arg =
              state.extra as ActivateCardIntroductionScreenArg;

          return ActivateCardIntroductionScreen(arg: arg);
        }
        return ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.activateCardGuidanceScreen.name,
      path: Screen.activateCardGuidanceScreen.routeName,
      builder: (_, GoRouterState state) {
        return ActivateCardGuidanceScreen();
      },
    ),
    GoRoute(
      name: Screen.setupPosLimitScreen.name,
      path: Screen.setupPosLimitScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is SetupPosLimitIntroductionScreenArg) {
          final SetupPosLimitIntroductionScreenArg arg =
              state.extra as SetupPosLimitIntroductionScreenArg;

          return SetupPosLimitIntroductionScreen(arg: arg);
        }
        return ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.setupPosLimitGuidanceScreen.name,
      path: Screen.setupPosLimitGuidanceScreen.routeName,
      builder: (_, GoRouterState state) {
        return SetupPosLimitGuidanceScreen();
      },
    ),
    GoRoute(
      name: Screen.storyWebViewScreen.name,
      path: Screen.storyWebViewScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is StoryWebViewArg) {
          final StoryWebViewArg arg = state.extra as StoryWebViewArg;
          return StoryWebView(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.activatePosLimitScreen.name,
      path: Screen.activatePosLimitScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ActivatePosLimitScreenArg) {
          final ActivatePosLimitScreenArg arg = state.extra as ActivatePosLimitScreenArg;
          return ActivatePosLimitScreen(arg: arg);
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.activatePosLimitThreeDSecureScreen.name,
      path: Screen.activatePosLimitThreeDSecureScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ActivatePosLimitThreeDSecureScreenArg) {
          final ActivatePosLimitThreeDSecureScreenArg arg =
              state.extra as ActivatePosLimitThreeDSecureScreenArg;
          return ActivatePosLimitThreeDSecureScreen(arg: arg);
        }

        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.maintenanceScreen.name,
      path: Screen.maintenanceScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is MaintenanceArg) {
          final MaintenanceArg arg = state.extra as MaintenanceArg;
          return MaintenanceScreen(arg: arg);
        }

        return const ErrorPage();
      },
    ),

    /// DOP native
    GoRoute(
      name: Screen.dopNativeStatusScreen.name,
      path: Screen.dopNativeStatusScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeStatusScreenArg) {
          final DOPNativeStatusScreenArg arg = state.extra as DOPNativeStatusScreenArg;
          return DOPNativeStatusScreen(
            icon: arg.icon,
            title: arg.title,
            description: arg.description,
            ctaWidget: arg.ctaWidget,
            descriptionWidget: arg.descriptionWidget,
            iconHeight: arg.iconHeight,
            noticeWidget: arg.noticeWidget,
            secondaryCTAWidget: arg.secondaryCTAWidget,
            errorReason: arg.errorReason,
            enableLogging: arg.enableLogging,
            screenId: arg.eventTrackingScreenId,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeVerifyOtpScreen.name,
      path: Screen.dopNativeVerifyOtpScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeVerifyOtpScreenArg) {
          final DOPNativeVerifyOtpScreenArg arg = state.extra as DOPNativeVerifyOtpScreenArg;
          return DOPNativeVerifyOtpScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeIntroductionScreen.name,
      path: Screen.dopNativeIntroductionScreen.routeName,
      builder: (_, GoRouterState state) {
        DOPNativeIntroductionScreenArg? arg;
        if (state.extra is DOPNativeIntroductionScreenArg) {
          arg = state.extra as DOPNativeIntroductionScreenArg;
        }

        return DOPNativeIntroductionScreen(arg: arg);
      },
    ),
    GoRoute(
      name: Screen.dopNativeSubIntroductionScreen.name,
      path: Screen.dopNativeSubIntroductionScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeSubIntroductionScreenArg) {
          final DOPNativeSubIntroductionScreenArg arg =
              state.extra as DOPNativeSubIntroductionScreenArg;
          return DOPNativeSubIntroductionScreen(
            url: arg.url,
            showFooter: arg.showFooter,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeIdCardCaptureIntroductionScreen.name,
      path: Screen.dopNativeIdCardCaptureIntroductionScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeIDCaptureIntroductionScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeIdCardFrontSideVerificationScreen.name,
      path: Screen.dopNativeIdCardFrontSideVerificationScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeIdCardFrontSideVerificationScreenArg) {
          final DOPNativeIdCardFrontSideVerificationScreenArg arg =
              state.extra as DOPNativeIdCardFrontSideVerificationScreenArg;
          return DOPNativeIdCardFrontSideVerificationScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeIdCardQRCodeVerificationScreen.name,
      path: Screen.dopNativeIdCardQRCodeVerificationScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeIdCardQRCodeVerificationScreenArg) {
          final DOPNativeIdCardQRCodeVerificationScreenArg arg =
              state.extra as DOPNativeIdCardQRCodeVerificationScreenArg;
          return DOPNativeIdCardQRCodeVerificationScreen(
            qrCodeBase64Image: arg.qrCodeBase64Image,
            cardType: arg.cardType,
            onSuccess: arg.onSuccess,
            onFailed: arg.onFailed,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeIdCardBackSideVerificationScreen.name,
      path: Screen.dopNativeIdCardBackSideVerificationScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeIdCardBackSideVerificationScreenArg) {
          final DOPNativeIdCardBackSideVerificationScreenArg arg =
              state.extra as DOPNativeIdCardBackSideVerificationScreenArg;
          return DOPNativeIdCardBackSideVerificationScreen(
            cardBackSideBase64Image: arg.cardBackSideBase64Image,
            onSuccess: arg.onSuccess,
            onFailed: arg.onFailed,
          );
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeIdCardSuccessScreen.name,
      path: Screen.dopNativeIdCardSuccessScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeIdCardSuccessScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeSelfieFlashIntroductionScreen.name,
      path: Screen.dopNativeSelfieFlashIntroductionScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeSelfieFlashIntroductionScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeSelfieActiveIntroductionScreen.name,
      path: Screen.dopNativeSelfieActiveIntroductionScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeSelfieActiveIntroductionScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeSelfieVerificationScreen.name,
      path: Screen.dopNativeSelfieVerificationScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeSelfieVerificationScreenArg) {
          final DOPNativeSelfieVerificationScreenArg arg =
              state.extra as DOPNativeSelfieVerificationScreenArg;
          return DOPNativeSelfieVerificationScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeEkycConfirmAdditionalInfoScreen.name,
      path: Screen.dopNativeEkycConfirmAdditionalInfoScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeEkycConfirmAdditionalInfoScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeSelfieVerificationSuccessScreen.name,
      path: Screen.dopNativeSelfieVerificationSuccessScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeSelfieVerifySuccessScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeEKYCConfirmScreen.name,
      path: Screen.dopNativeEKYCConfirmScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeEkycConfirmScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeSingleEKYCConfirmScreen.name,
      path: Screen.dopNativeSingleEKYCConfirmScreen.routeName,
      builder: (_, GoRouterState state) {
        return DOPNativeSingleEKYCConfirmScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeAppraisingVerificationScreen.name,
      path: Screen.dopNativeAppraisingVerificationScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeApprisingVerificationScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeThreeStepAppraisingVerificationScreen.name,
      path: Screen.dopNativeThreeStepAppraisingVerificationScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeThreeStepApprisingVerificationScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeInformSuccessSemiScreen.name,
      path: Screen.dopNativeInformSuccessSemiScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeInformSuccessSemiScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeInformSuccessAutoCICScreen.name,
      path: Screen.dopNativeInformSuccessAutoCICScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeInformSuccessAutoCICScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeInformSuccessAutoPCBScreen.name,
      path: Screen.dopNativeInformSuccessAutoPCBScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeInformSuccessAutoPCBScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeCifConfirmScreen.name,
      path: Screen.dopNativeCifConfirmScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeCifConfirmScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeCifNoBranchScreen.name,
      path: Screen.dopNativeCifNoBranchScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeCIFNoBranchScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativePDFViewScreen.name,
      path: Screen.dopNativePDFViewScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativePDFViewArg) {
          final DOPNativePDFViewArg arg = state.extra as DOPNativePDFViewArg;
          return DOPNativePDFViewScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeFailureScreen.name,
      path: Screen.dopNativeFailureScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeFailureScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeWebViewScreen.name,
      path: Screen.dopNativeWebViewScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeWebViewArg) {
          final DOPNativeWebViewArg arg = state.extra as DOPNativeWebViewArg;
          return DOPNativeWebViewScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeAppFormAdditionalInfoScreen.name,
      path: Screen.dopNativeAppFormAdditionalInfoScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeAdditionalFormScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeEKYCLimitExceedScreen.name,
      path: Screen.dopNativeEKYCLimitExceedScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeEKYCLimitExceedScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeCardActivatedScreen.name,
      path: Screen.dopNativeCardActivatedScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeCardActivatedScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeCardActivatedRetryPosLimitScreen.name,
      path: Screen.dopNativeCardActivatedRetryPosLimitScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeCardActivatedRetryPosLimitScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeCardActivatedPosFailedScreen.name,
      path: Screen.dopNativeCardActivatedPosFailedScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeCardActivatedPosFailedScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeCardActiveRetryScreen.name,
      path: Screen.dopNativeCardActiveRetryScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeCardActiveRetryScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeCardActiveFailScreen.name,
      path: Screen.dopNativeCardActiveFailScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeCardActiveFailScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeESignReviewScreen.name,
      path: Screen.dopNativeESignReviewScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeESignReviewScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeEContractDownloadScreen.name,
      path: Screen.dopNativeEContractDownloadScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeEContractDownloadScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeAmericaCitizenScreen.name,
      path: Screen.dopNativeAmericaCitizenScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeAmericaCitizenArg) {
          final DOPNativeAmericaCitizenArg arg = state.extra as DOPNativeAmericaCitizenArg;
          return DOPNativeAmericaCitizenScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeESuccessScreen.name,
      path: Screen.dopNativeESuccessScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeESuccessScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeCardStatusCICBlockScreen.name,
      path: Screen.dopNativeCardStatusCICBlockScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeCardCICBlockedScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeESignOTPScreen.name,
      path: Screen.dopNativeESignOTPScreen.routeName,
      builder: (_, GoRouterState state) {
        final DOPNativeVerifyOtpScreenArg eSignOtpArg =
            DOPNativeVerifyOtpScreenArg(verifyOtpType: DOPNativeVerifyOtpType.eSign);
        return DOPNativeVerifyOtpScreen(arg: eSignOtpArg);
      },
    ),
    GoRoute(
      name: Screen.dopNativeESignIntroScreen.name,
      path: Screen.dopNativeESignIntroScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeESignIntroScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeESuccessSemiScreen.name,
      path: Screen.dopNativeESuccessSemiScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeESuccessSemiScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeCardStatusInformationScreen.name,
      path: Screen.dopNativeCardStatusInformationScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeCardStatusInformationScreenArg) {
          final DOPNativeCardStatusInformationScreenArg arg =
              state.extra as DOPNativeCardStatusInformationScreenArg;
          return DOPNativeCardStatusInformationScreen(arg: arg);
        }
        return DOPNativeCardStatusInformationScreen(
          arg: DOPNativeCardStatusInformationScreenArg(),
        );
      },
    ),
    GoRoute(
      name: Screen.dopNativeUnderwritingCardIssuedScreen.name,
      path: Screen.dopNativeUnderwritingCardIssuedScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeUnderWritingCardIssuedScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeThreeDSecureScreen.name,
      path: Screen.dopNativeThreeDSecureScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeThreeDSecureArg) {
          final DOPNativeThreeDSecureArg arg = state.extra as DOPNativeThreeDSecureArg;
          return DOPNativeThreeDSecureScreen(url: arg.url);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeUnderwritingCardStatusScreen.name,
      path: Screen.dopNativeUnderwritingCardStatusScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeUnderwritingCardStatusScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeUnderwritingInProgressScreen.name,
      path: Screen.dopNativeUnderwritingInProgressScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeUnderwritingInProgressScreen();
      },
    ),
    GoRoute(
      name: Screen.nonLoginQrCodeScannerScreen.name,
      path: Screen.nonLoginQrCodeScannerScreen.routeName,
      builder: (_, GoRouterState state) {
        return const NonLoginQrCodeScannerScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeNFCReaderIntroductionScreen.name,
      path: Screen.dopNativeNFCReaderIntroductionScreen.routeName,
      builder: (_, GoRouterState state) {
        /// Redirect to the NFC Reader screen by Feature Toggle
        final NfcSdkProvider nfcSdkProvider = getIt<FeatureToggle>().nfcSdkProvider;
        switch (nfcSdkProvider) {
          case NfcSdkProvider.trustVision:
            if (state.extra is DOPNativeTvNFCReaderIntroductionScreenArg) {
              return DOPNativeTvNFCReaderIntroductionScreen(
                arg: state.extra as DOPNativeTvNFCReaderIntroductionScreenArg,
              );
            }
            return DOPNativeTvNFCReaderIntroductionScreen(
              arg: DOPNativeTvNFCReaderIntroductionScreenArg(
                controller: DOPNativeTvNFCReaderIntroductionController(),
              ),
            );
          case NfcSdkProvider.fpt:
            if (state.extra is DOPNativeFptNFCReaderIntroductionScreenArg) {
              return DOPNativeFptNFCReaderIntroductionScreen(
                arg: state.extra as DOPNativeFptNFCReaderIntroductionScreenArg,
              );
            }
            return DOPNativeFptNFCReaderIntroductionScreen(
              arg: DOPNativeFptNFCReaderIntroductionScreenArg(
                controller: DOPNativeFptNFCReaderIntroductionController(),
              ),
            );
        }
      },
    ),
    GoRoute(
      name: Screen.dopNativeSalesmanScreen.name,
      path: Screen.dopNativeSalesmanScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeSalesmanIDInputScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeSalesmanConfirmScreen.name,
      path: Screen.dopNativeSalesmanConfirmScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeSalesmanIDConfirmScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeAcquisitionRewardScreen.name,
      path: Screen.dopNativeAcquisitionRewardScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeAcquisitionRewardScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeFaceOtpIntroductionScreen.name,
      path: Screen.dopNativeFaceOtpIntroductionScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeFaceOtpIntroductionScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeFaceOtpSuccessScreen.name,
      path: Screen.dopNativeFaceOtpSuccessScreen.routeName,
      builder: (_, GoRouterState state) {
        return const DOPNativeFaceOtpSuccessScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeFaceOtpRetryScreen.name,
      path: Screen.dopNativeFaceOtpRetryScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeFaceOtpRetryScreenArg) {
          final DOPNativeFaceOtpRetryScreenArg arg = state.extra as DOPNativeFaceOtpRetryScreenArg;
          return DOPNativeFaceOtpRetryScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeNFCDeviceUnsupportedScreen.name,
      path: Screen.dopNativeNFCDeviceUnsupportedScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeNFCDeviceUnsupportedScreenArg) {
          final DOPNativeNFCDeviceUnsupportedScreenArg arg =
              state.extra as DOPNativeNFCDeviceUnsupportedScreenArg;
          return DOPNativeNFCDeviceUnsupportedScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeCollectLocationScreen.name,
      path: Screen.dopNativeCollectLocationScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is DOPNativeCollectLocationScreenArg) {
          final DOPNativeCollectLocationScreenArg arg =
              state.extra as DOPNativeCollectLocationScreenArg;
          return DOPNativeCollectLocationScreen(arg: arg);
        }
        return const ErrorPage();
      },
    ),
    GoRoute(
      name: Screen.dopNativeESuccessCICHoldingScreen.name,
      path: Screen.dopNativeESuccessCICHoldingScreen.routeName,
      builder: (_, GoRouterState state) {
        return DOPNativeESuccessCICHoldingScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeESignIntroMWGScreen.name,
      path: Screen.dopNativeESignIntroMWGScreen.routeName,
      builder: (_, GoRouterState state) {
        return DOPNativeESignIntroMWGScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeFourthAppraisingScreen.name,
      path: Screen.dopNativeFourthAppraisingScreen.routeName,
      builder: (_, GoRouterState state) {
        return DOPNativeFourthApprisingScreen();
      },
    ),
    GoRoute(
      name: Screen.dopNativeInformSuccessSophiaScreen.name,
      path: Screen.dopNativeInformSuccessSophiaScreen.routeName,
      builder: (_, GoRouterState state) {
        return DOPNativeInformSuccessSophiaScreen();
      },
    ),
  ],
);
