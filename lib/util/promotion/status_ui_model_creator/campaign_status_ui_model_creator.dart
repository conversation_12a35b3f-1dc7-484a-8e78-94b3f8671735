import '../../../model/promotion_expired_date.dart';
import '../../../model/promotion_status_ui_model.dart';
import '../../../resources/resources.dart';
import '../../extension.dart';
import '../promotion_constants.dart';
import '../status_ui_data_creator_factory/promotion_source_data.dart';
import 'promotion_status_ui_model_creator.dart';

class CampaignStatusUIModelCreator extends PromotionStatusUIModelCreator<CampaignSourceData> {
  CampaignStatusUIModelCreator({required super.sourceData});

  @override
  PromotionStatusUIModel? createPromotionStatusUIData() {
    final DateTime? validFromDate = sourceData.validFromDate;

    /// Campaign is coming:
    ///  - text: Bắt đầu vào dd/MM/yyyy
    ///  - color: 0xFF09B364
    if (validFromDate != null) {
      final bool isCampaignComing = validFromDate.isCampaignComing(
        today: sourceData.compareDate,
        configDisplayCampaignInDays: sourceData.configDisplayCampaignInDays,
      );

      if (isCampaignComing) {
        final String formattedDate = validFromDate.toStringFormatDate(
          PromotionConstants.promotionDateFormat,
        );
        return PromotionStatusUIModel(
          color: evoColors.campaignIsComing,
          title: '${EvoStrings.promotionStartAt} $formattedDate',
        );
      }
    }

    final DateTime? validToDate = sourceData.validToDate;
    if (validToDate == null) {
      return null;
    }

    final PromotionExpiredDate? campaignExpiredDate = validToDate.toPromotionExpiredDate(
      compareDate: sourceData.compareDate,
      configRunningOutTimeInHours: sourceData.configRunningOutTimeInHours,
      configHotTimeInHours: sourceData.configHotTimeInHours,
    );

    /// Expired
    /// - text: "Đã kết thúc"
    /// - color: 0xFF5E5E5E
    if (campaignExpiredDate?.status == ExpiredDateStatus.expired) {
      return PromotionStatusUIModel(
        title: EvoStrings.campaignTimeOut,
        color: evoColors.promotionTimeout,
        hasOpacityTitle: true,
      );
    }

    /// Before running out: Expiry time >= 96h
    /// - text: "Kết thúc vào dd/MM/yyyy"
    /// - color: 0xFFF5A70B
    if (campaignExpiredDate?.status == ExpiredDateStatus.beforeRunningOut) {
      final String formattedDate = validToDate.toStringFormatDate(
        PromotionConstants.promotionDateFormat,
      );
      return PromotionStatusUIModel(
        title: '${EvoStrings.campaignEndAt} $formattedDate',
        color: evoColors.campaignBeforeRunningOutTime,
      );
    }

    final String? campaignDateTitle =
        campaignExpiredDate?.runningOutTimeInfo?.getFormatRunningOutExpiredDateTitle();

    /// Hot time: Expiry time < 24h
    ///   - text:
    ///     + Expiry time < 1h: "Còn x phút"
    ///     + Expiry time < 24h: "Còn x giờ" OR "Còn x giờ, x phút"
    ///   - color: 0xFFE54D2E
    if (campaignExpiredDate?.status == ExpiredDateStatus.hotTime) {
      return PromotionStatusUIModel(
        title: campaignDateTitle,
        color: evoColors.promotionHotTime,
      );
    }

    /// Running out time: 96h > Expiry time >= 24h
    /// Voucher & Campaign:
    ///   - text: "Còn x ngày" OR "Còn x ngày, x giờ"
    ///   - color: 0xFFF5A70B
    return PromotionStatusUIModel(
      title: campaignDateTitle,
      color: evoColors.promotionRunningOutTime,
    );
  }
}
