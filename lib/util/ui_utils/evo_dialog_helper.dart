import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/common_dialog_bottom_sheet.dart';

import '../../feature/feature_toggle.dart';
import '../../feature/profile/profile_detail_screen/other_widgets/list_action_bottom_sheet.dart';
import '../../model/evo_dialog_id.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../widget/evo_dialog/dialog_confirm.dart';
import '../../widget/evo_dialog/full_screen_bottom_sheet_widget.dart';
import '../evo_flutter_wrapper.dart';

class EvoDialogHelper {
  static EvoDialogHelper? _instance;

  static final EvoDialogHelper _originalInstance = EvoDialogHelper._internal();

  factory EvoDialogHelper() {
    return _instance ??= _originalInstance;
  }

  EvoDialogHelper._internal();

  // Method to replace the singleton instance (for testing only)
  @visibleForTesting
  static void setInstanceForTesting(EvoDialogHelper instance) {
    _instance = instance;
  }

  // Method to reset the singleton instance (for testing only)
  @visibleForTesting
  static void resetToOriginalInstance() {
    _instance = _originalInstance;
  }

  Future<void> showFullScreenBottomSheet(
    BuildContext context, {
    Widget? content,
    VoidCallback? onClose,
    bool isDismissible = true,
  }) async {
    await showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      enableDrag: false,
      isDismissible: isDismissible,
      builder: (_) => FullScreenBottomSheetWidget(content: content),
    );
  }

  void showMultiActionBottomSheet(BuildContext context, List<ActionBottomSheet> actions,
      {required void Function(ActionBottomSheet) onSelectedItem}) {
    showModalBottomSheet<void>(
        context: context,
        backgroundColor: evoColors.background,
        shape: const RoundedRectangleBorder(
            borderRadius:
                BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24))),
        builder: (BuildContext context) =>
            ListActionBottomSheet(list: actions, onPress: onSelectedItem));
  }

  Future<void> showDialogBottomSheet({
    required String textPositive,
    required EvoDialogId dialogId,
    String? content,
    String? title,
    String? textNegative,
    Widget? footer,
    VoidCallback? onClickPositive,
    VoidCallback? onClickNegative,
    Widget? header,
    EdgeInsets? headerPadding,
    bool isDismissible = true,
    ButtonStyle? positiveButtonStyle,
    ButtonStyle? negativeButtonStyle,
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
    VoidCallback? onClickClose,
    bool isShowButtonClose = false,
    Widget? buttonClose,
    Map<String, dynamic>? loggingEventMetaData,
    ButtonListOrientation? buttonListOrientation,
    Map<String, dynamic>? loggingEventOnShowMetaData,
    TextAlign? titleTextAlign,
    TextAlign? contentTextAlign,
    double? contentSpacing,
  }) async {
    final BuildContext? navigatorCtx = navigatorContext;
    if (navigatorCtx == null) {
      return Future<void>.value();
    }

    return commonUtilFunction.showDialogBottomSheet(
      navigatorCtx,
      textPositive: textPositive,
      content: content,
      title: title,
      textNegative: textNegative,
      footer: footer,
      onClickPositive: onClickPositive,
      onClickNegative: onClickNegative,
      header: header,
      headerPadding: headerPadding,
      isDismissible: isDismissible,
      positiveButtonStyle:
          positiveButtonStyle ?? getIt.get<CommonButtonStyles>().primary(ButtonSize.xLarge),
      negativeButtonStyle:
          negativeButtonStyle ?? getIt.get<CommonButtonStyles>().tertiary(ButtonSize.xLarge),
      titleTextStyle: titleTextStyle,
      contentTextStyle: contentTextStyle,
      onClickClose: onClickClose,
      isShowButtonClose: isShowButtonClose,
      buttonClose: buttonClose,
      isEnableLoggingEvent: getIt<FeatureToggle>().enableEventTrackingFeature,
      dialogId: dialogId.id,
      eventTrackingScreenId: getIt<AppState>().currentScreenId.name,
      loggingEventOnShowMetaData: loggingEventOnShowMetaData,
      loggingEventMetaData: loggingEventMetaData,
      buttonListOrientation: buttonListOrientation,
      titleTextAlign: titleTextAlign,
      contentTextAlign: contentTextAlign,
      contentSpacing: contentSpacing,
    );
  }

  Future<void> showDialogConfirm({
    required String textPositive,
    required EvoDialogId dialogId,
    String? content,
    String? title,
    String? textNegative,
    Widget? footer,
    VoidCallback? onClickPositive,
    VoidCallback? onClickNegative,
    Widget? imageHeader,
    bool isDismissible = true,
    ButtonStyle? positiveButtonStyle,
    ButtonStyle? negativeButtonStyle,
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
    bool isShowButtonClose = false,
    TextAlign? titleTextAlign,
    TextAlign? contentTextAlign,
    Key? key,
    ButtonListOrientation? buttonListOrientation,
    Map<String, dynamic>? loggingEventMetaData,
    Map<String, dynamic>? loggingEventOnShowMetaData,
  }) async {
    return await evoFlutterWrapper.showDialog<void>(
      barrierDismissible: isDismissible,
      builder: (_) => createBuilderForDialogConfirm(
        key: key,
        content: content,
        textPositive: textPositive,
        title: title,
        textNegative: textNegative,
        footer: footer,
        onClickPositive: onClickPositive,
        onClickNegative: onClickNegative,
        imageHeader: imageHeader,
        isDismissible: isDismissible,
        positiveButtonStyle: positiveButtonStyle,
        negativeButtonStyle: negativeButtonStyle,
        titleTextStyle: titleTextStyle,
        contentTextStyle: contentTextStyle,
        dialogId: dialogId,
        loggingEventMetaData: loggingEventMetaData,
        loggingEventOnShowMetaData: loggingEventOnShowMetaData,
        isShowButtonClose: isShowButtonClose,
        titleTextAlign: titleTextAlign,
        contentTextAlign: contentTextAlign,
        buttonListOrientation: buttonListOrientation,
      ),
    );
  }

  @visibleForTesting
  Widget createBuilderForDialogConfirm({
    required String textPositive,
    required EvoDialogId dialogId,
    String? content,
    String? title,
    String? textNegative,
    Widget? footer,
    VoidCallback? onClickPositive,
    VoidCallback? onClickNegative,
    Widget? imageHeader,
    bool isDismissible = true,
    ButtonStyle? positiveButtonStyle,
    ButtonStyle? negativeButtonStyle,
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
    bool isEnableLoggingEvent = true,
    Map<String, dynamic>? loggingEventMetaData,
    bool isShowButtonClose = false,
    TextAlign? titleTextAlign,
    TextAlign? contentTextAlign,
    Key? key,
    ButtonListOrientation? buttonListOrientation,
    Map<String, dynamic>? loggingEventOnShowMetaData,
  }) {
    return PopScope(
      canPop: isDismissible,
      child: EvoDialogConfirm(
        key: key,
        content: content,
        textPositive: textPositive,
        title: title,
        textNegative: textNegative,
        footer: footer,
        onClickPositive: onClickPositive,
        onClickNegative: onClickNegative,
        imageHeader: imageHeader,
        positiveButtonStyle:
            positiveButtonStyle ?? getIt.get<CommonButtonStyles>().primary(ButtonSize.xLarge),
        negativeButtonStyle:
            negativeButtonStyle ?? getIt.get<CommonButtonStyles>().tertiary(ButtonSize.xLarge),
        titleTextStyle: titleTextStyle,
        contentTextStyle: contentTextStyle,
        dialogId: dialogId.id,
        loggingEventOnShowMetaData: loggingEventOnShowMetaData,
        loggingEventMetaData: loggingEventMetaData,
        isShowButtonClose: isShowButtonClose,
        titleTextAlign: titleTextAlign,
        contentTextAlign: contentTextAlign,
        buttonListOrientation: buttonListOrientation,
      ),
    );
  }
}
