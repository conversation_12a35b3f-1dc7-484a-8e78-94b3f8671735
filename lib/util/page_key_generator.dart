import 'dart:math';
import 'package:flutter/material.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// Custom page key generator utility class
class PageKeyGenerator {
  static final Random _random = Random();
  
  /// Generate a simple random alphanumeric page key
  /// Length: 16 characters (default)
  /// Characters: a-z, A-Z, 0-9
  static ValueKey<String> generateSimpleKey({int length = 16}) {
    const String chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return ValueKey<String>(
      String.fromCharCodes(
        Iterable.generate(
          length,
          (_) => chars.codeUnitAt(_random.nextInt(chars.length)),
        ),
      ),
    );
  }

  /// Generate a UUID-like page key (without hyphens)
  /// Length: 32 characters
  /// Characters: 0-9, a-f (hexadecimal)
  static ValueKey<String> generateUuidLikeKey() {
    const String hexChars = '0123456789abcdef';
    return ValueKey<String>(
      String.fromCharCodes(
        Iterable.generate(
          32,
          (_) => hexChars.codeUnitAt(_random.nextInt(hexChars.length)),
        ),
      ),
    );
  }

  /// Generate a timestamp-based page key
  /// Format: timestamp_randomSuffix
  /// Example: 1704067200000_abc123
  static ValueKey<String> generateTimestampKey({int suffixLength = 6}) {
    final int timestamp = DateTime.now().millisecondsSinceEpoch;
    const String chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final String suffix = String.fromCharCodes(
      Iterable.generate(
        suffixLength,
        (_) => chars.codeUnitAt(_random.nextInt(chars.length)),
      ),
    );
    return ValueKey<String>('${timestamp}_$suffix');
  }

  /// Generate a hash-based page key using screen name and timestamp
  /// This ensures uniqueness while being deterministic for the same input
  static ValueKey<String> generateHashKey(String screenName) {
    final String input = '$screenName${DateTime.now().millisecondsSinceEpoch}${_random.nextInt(999999)}';
    final List<int> bytes = utf8.encode(input);
    final Digest digest = sha256.convert(bytes);
    // Take first 16 characters of the hash
    return ValueKey<String>(digest.toString().substring(0, 16));
  }

  /// Generate a prefix-based page key
  /// Format: prefix_randomString
  /// Example: screen_abc123def456
  static ValueKey<String> generatePrefixKey(String prefix, {int randomLength = 12}) {
    const String chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final String randomPart = String.fromCharCodes(
      Iterable.generate(
        randomLength,
        (_) => chars.codeUnitAt(_random.nextInt(chars.length)),
      ),
    );
    return ValueKey<String>('${prefix}_$randomPart');
  }

  /// Generate a numeric-only page key
  /// Length: 16 digits (default)
  static ValueKey<String> generateNumericKey({int length = 16}) {
    return ValueKey<String>(
      String.fromCharCodes(
        Iterable.generate(
          length,
          (_) => '0'.codeUnitAt(0) + _random.nextInt(10),
        ),
      ),
    );
  }

  /// Generate a mixed case alphabetic page key (no numbers)
  /// Length: 20 characters (default)
  /// Characters: a-z, A-Z only
  static ValueKey<String> generateAlphaKey({int length = 20}) {
    const String chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    return ValueKey<String>(
      String.fromCharCodes(
        Iterable.generate(
          length,
          (_) => chars.codeUnitAt(_random.nextInt(chars.length)),
        ),
      ),
    );
  }

  /// Generate a secure random page key using cryptographically secure random
  /// Length: 24 characters (default)
  /// Characters: a-z, A-Z, 0-9
  static ValueKey<String> generateSecureKey({int length = 24}) {
    const String chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final Random secureRandom = Random.secure();
    return ValueKey<String>(
      String.fromCharCodes(
        Iterable.generate(
          length,
          (_) => chars.codeUnitAt(secureRandom.nextInt(chars.length)),
        ),
      ),
    );
  }

  /// Generate a page key with custom character set
  /// You can specify exactly which characters to use
  static ValueKey<String> generateCustomKey({
    required String characterSet,
    int length = 16,
  }) {
    if (characterSet.isEmpty) {
      throw ArgumentError('Character set cannot be empty');
    }
    
    return ValueKey<String>(
      String.fromCharCodes(
        Iterable.generate(
          length,
          (_) => characterSet.codeUnitAt(_random.nextInt(characterSet.length)),
        ),
      ),
    );
  }

  /// Generate a readable page key using words and numbers
  /// Format: word1-word2-number
  /// Example: blue-sky-123
  static ValueKey<String> generateReadableKey() {
    const List<String> adjectives = [
      'red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'brown',
      'black', 'white', 'gray', 'silver', 'gold', 'bright', 'dark', 'light'
    ];
    
    const List<String> nouns = [
      'cat', 'dog', 'bird', 'fish', 'tree', 'flower', 'star', 'moon',
      'sun', 'cloud', 'mountain', 'river', 'ocean', 'forest', 'desert', 'island'
    ];
    
    final String adjective = adjectives[_random.nextInt(adjectives.length)];
    final String noun = nouns[_random.nextInt(nouns.length)];
    final int number = _random.nextInt(9999);
    
    return ValueKey<String>('$adjective-$noun-$number');
  }
}

/// Extension to add page key generation to existing utility function
extension PageKeyGeneratorExtension on Object {
  /// Quick access to generate a simple page key
  ValueKey<String> generatePageKey() => PageKeyGenerator.generateSimpleKey();
  
  /// Quick access to generate a secure page key
  ValueKey<String> generateSecurePageKey() => PageKeyGenerator.generateSecureKey();
}
