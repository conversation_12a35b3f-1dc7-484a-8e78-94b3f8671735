import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/intl.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/common_adaptive_text_selection_toolbar_impl.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../data/response/payment_info_entity.dart';
import '../data/response/payment_method_entity.dart';
import '../feature/biometric/model/biometric_ui_model.dart';
import '../feature/biometric/utils/bio_auth_result.dart';
import '../feature/feature_toggle.dart';
import '../feature/logging/evo_logging_event.dart';
import '../feature/main_screen/main_screen.dart';
import '../flavors/flavors_type.dart';
import '../prepare_for_app_initiation.dart';
import '../resources/resources.dart';
import '../widget/evo_pin_code/evo_pin_code_config.dart';
import 'evo_flutter_wrapper.dart';
import 'evo_snackbar.dart';
import 'secure_storage_helper/secure_storage_helper.dart';

final EvoUtilFunction evoUtilFunction = getIt.get<EvoUtilFunction>();

class EvoUtilFunction with LogErrorMixin {
  @visibleForTesting
  bool moveToMainScreen = false;

  /// This method only is worked on iOS
  /// On Android, It always returns false
  Future<bool> detectReinstallAppOnIOSDevice() async {
    if (evoFlutterWrapper.isAndroid()) {
      return false;
    }

    /*
    * EvoLocalStorageHelper save data to keychain on iOS.
    * First install: save deviceId to keychain
    * Re-install: check newDeviceId == deviceId (in key chain)
    * true -> next step
    * false -> delete all data in keychain and go Tutorial page
    */
    final String? deviceIdLocal = await getIt.get<EvoLocalStorageHelper>().getDeviceId();

    /*
    * Document identifierForVendor:
    * https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor
    * The value in this property remains the same while the app
    * (or another app from the same vendor) is installed on the iOS device.
    * The value changes when the user deletes all of that vendor’s apps from the device
    * and subsequently re-installs one or more of them.
    * */
    final String? newDeviceId = await getNewDeviceId();
    return newDeviceId != deviceIdLocal;
  }

  Future<void> deleteAllData() async {
    await getIt<EvoLocalStorageHelper>().deleteAllData();
  }

  Future<String?> getNewDeviceId() async {
    if (evoFlutterWrapper.isAndroid()) {
      return null;
    }

    final String? newDeviceId = (await commonUtilFunction.getIosInfo()).identifierForVendor;
    return newDeviceId;
  }

  Future<void> setNewDeviceId() async {
    final String? newDeviceId = await getNewDeviceId();
    await getIt<EvoLocalStorageHelper>().setDeviceId(newDeviceId);
  }

  String? encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((MapEntry<String, String> e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  String? formatBirthday(String? birthday) {
    if (birthday == null) {
      return null;
    }

    try {
      final DateTime parsedDate = DateTime.parse(birthday);
      final DateFormat dateBirthdayFormat = DateFormat('dd/MM/yyyy');
      return dateBirthdayFormat.format(parsedDate);
    } on Exception catch (ex) {
      commonLog('Exception format Birthday: $ex');
      logException(
        eventType: EvoEventType.commonPlatformException,
        methodName: 'formatBirthday',
        exception: ex,
      );
      return null;
    }
  }

  String getPhoneFromPasteClipboard(String valueSelection) {
    return (valueSelection.startsWith(defaultTextPhoneNumber)
            ? valueSelection.replaceFirst(defaultTextPhoneNumber, '')
            : valueSelection)
        .let((String number) => number.startsWith(defaultPrefixNationalPhone)
            ? number.replaceFirst(defaultPrefixNationalPhone, defaultPrefixLocalPhone)
            : number)
        .replaceAll(' ', '');
  }

  bool validateMaxLengthPin(String value) {
    return value.length == EvoPinCodeConfig.maxPinCodeLength;
  }

  bool handleBackOrGoHomeScreen() {
    /// If back stack has only this page, will navigate to home page with non-user,
    /// else back to previous screen
    final BuildContext? navigatorCtx = navigatorContext;
    if (navigatorCtx?.maybePop() ?? false) {
      navigatorCtx?.popBack();
    } else {
      MainScreen.goNamed(isLoggedIn: false);
      moveToMainScreen = true;
    }
    return false;
  }

  String evoFormatCurrency(int? value, {String? currencySymbol}) {
    if (value == null) {
      return '-';
    }

    final String currencyFormatted = commonUtilFunction.currencyFormat.format(value);

    if (currencySymbol != null) {
      return '$currencyFormatted$currencySymbol';
    }

    return currencyFormatted;
  }

  int? getAmountFromStr(String amount, {String currencySuffix = ''}) {
    /// Replace dot: 1.000 => 1000
    amount = amount.replaceAll('.', '');

    if (currencySuffix.isNotEmpty) {
      amount = amount.replaceFirst(currencySuffix, '');
    }

    /// Try to parse to num
    return int.tryParse(amount);
  }

  DateTime getCurrentTime() => DateTime.now();

  String getCurrentTimeString() => getCurrentTime().toString();

  PaymentMethodEntity? getDefaultPaymentMethod(PaymentInfoEntity? paymentInfo) {
    return paymentInfo?.paymentMethods?.firstWhereOrNull(
      (PaymentMethodEntity? item) =>
          item?.id?.isNotEmpty == true && item?.id == paymentInfo.paymentMethodId,
    );
  }

  void handleBioError(BioAuthError? bioError) {
    if (bioError?.isBiometricLocked() == true) {
      final BiometricTypeUIModel bioTypeInfo = getIt.get<AppState>().bioTypeInfo;
      final String errMsg = EvoStrings.biometricLocked
          .replaceVariableByValue(<String>[bioTypeInfo.biometricTypeName.uppercaseFirstLetter()]);
      getIt.get<EvoSnackBar>().show(
            errMsg,
            typeSnackBar: SnackBarType.error,
            durationInSec: SnackBarDuration.short.value,
          );
    }
  }

  String cropStringWithMaxLength(String content, int maxLength, {bool ellipsis = true}) {
    if (maxLength <= 0) {
      return content;
    }
    return content.length > maxLength
        ? '${content.substring(0, maxLength)}${ellipsis ? ' ...' : ''}'
        : content;
  }

  bool validateMinMaxLengthNationalId(String nationalId) {
    return nationalId.length == minLengthNationalId || nationalId.length == maxLengthNationalId;
  }

  String getNationalIdWithMaxLength(String nationalId) {
    if (nationalId.length > maxLengthNationalId) {
      return nationalId.substring(0, maxLengthNationalId);
    } else {
      return nationalId;
    }
  }

  String convertStringToObscureText(String value, String obscureText) => obscureText * value.length;

  String getFileNamesFromDownloadPath(String downloadUrl, {String? fileExtension}) {
    final String fileNameFromUrl = downloadUrl.split('/').last;
    if (fileExtension != null && !fileNameFromUrl.endsWith(fileExtension)) {
      return '$fileNameFromUrl$fileExtension';
    }
    return fileNameFromUrl;
  }

  /// Apple App Store - Evo App ID
  String get iOSEvoAppId {
    final String flavorName = FlavorConfig.instance.flavor;
    if (flavorName == FlavorType.stag.name) {
      // Staging https://appstoreconnect.apple.com/apps/1662848066/distribution/
      return '1662848066';
    } else if (flavorName == FlavorType.uat.name) {
      // UAT https://appstoreconnect.apple.com/apps/1665449377/distribution/
      return '1665449377';
    } else if (flavorName == FlavorType.prod.name) {
      // Production https://appstoreconnect.apple.com/apps/1665449531/distribution/
      return '1665449531';
    } else {
      // Staging https://appstoreconnect.apple.com/apps/1662848066/distribution/
      return '1662848066';
    }
  }

  AdaptiveTextSelectionToolbar createPasteClipboard({
    required EditableTextState editableTextState,
    ValueChanged<TextSelectionDelegate>? onPaste,
  }) {
    final CommonAdaptiveTextSelectionToolbar commonAdaptiveTextSelectionToolbar =
        CommonAdaptiveTextSelectionToolbar();

    return commonAdaptiveTextSelectionToolbar.handleSelectionToolbar(
      editableTextState: editableTextState,
      isShownPasteOnToolbar: true,
      onPaste: onPaste,
    );
  }

  void updateProcessUserStatus(String? status) {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();
    if (featureToggle.enableDeleteAccountFeatureVersion == DeleteAccountFeatureVersion.version_2) {
      final AppState appState = getIt.get<AppState>();
      appState.loginSharedData.setUserStatus(status);
    }
  }

  FacialVerificationVersion getFacialVerificationVersion() {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();
    return featureToggle.facialVerificationVersion;
  }

  bool isGoEvoURL(String url) {
    // Specific subdomains to exclude
    // Ref: https://trustingsocial1.atlassian.net/browse/EMA-6677
    final List<String> excludedSubdomains = <String>[
      'test-merchant-api.goevo.vn',
      'merchant-api.goevo.vn',
    ];

    // first, check for excluded subdomains
    for (final String excludedSubdomain in excludedSubdomains) {
      // Check if the URL contains these exact subdomains (with https:// prefix)
      if (url.startsWith('https://$excludedSubdomain') ||
          url.startsWith('https://www.$excludedSubdomain')) {
        return false;
      }
    }

    // regex to check is an correct https URL start with "goevo.vn"
    // - "^https:\/\/" -> start with "https://", not allow http or another schema
    // - "([a-zA-Z0-9-]+\.)*" -> support non or multiple sub-domains
    // - "goevo\.vn" -> match the literal domain "goevo.vn"
    // - "(/.*)?$" -> support non or multiple suffix path, query params, ...
    // Ref: https://trustingsocial1.atlassian.net/browse/EMA-3845
    final RegExp regExp = RegExp(r'^https:\/\/([a-zA-Z0-9-]+\.)*goevo\.vn(\/.*)?$');
    return regExp.hasMatch(url);
  }

  String getDDMMYYFormatFromDateTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('dd/MM/yyyy');

    return formatter.format(dateTime);
  }
}
