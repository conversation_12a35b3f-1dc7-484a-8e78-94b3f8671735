import '../../data/response/linked_card_status_checking_entity.dart';
import '../../feature/manual_link_card/model/manual_link_card_result_model.dart';

enum MockDeactivateAccountStatus {
  success,
  fail,
}

String getUserInfoMockFileName() {
  return 'user_information.json';
}

String createPinMockFileName() {
  return 'pin_code_verify.json';
}

String getBiometricTokenByPinMockFileName({String? pin, int numberOfVerifiedFailed = 0}) {
  if (pin == '123456') {
    return 'get_biometric_token_success.json';
  } else if (pin == '111111') {
    return 'get_biometric_token_success_with_challenge_type.json';
  } else {
    if (numberOfVerifiedFailed <= 3) {
      return 'get_biometric_with_pin_verify_error.json';
    } else if (numberOfVerifiedFailed == 4) {
      return 'get_biometric_with_pin_warning_verify_error.json';
    } else {
      return 'get_biometric_with_pin_block_due_to_verify_error.json';
    }
  }
}

String getDeactivateAccountMockFileName({MockDeactivateAccountStatus? mockStatus}) {
  if (mockStatus == MockDeactivateAccountStatus.fail) {
    return 'deactivate_user_fail.json';
  }

  return 'deactivate_user_success.json';
}

String getLinkedCardsMockFileName() {
  return 'get_linked_card_list_success.json';
}

String getPaymentMethodsMockFileName() {
  return 'payment_method_list_empty.json';
}

String checkLinkedCardsStatusMockFileName({String? mockVerdict}) {
  switch (mockVerdict) {
    case LinkedCardStatusCheckingEntity.verdictUnfulfilledCard:
      return 'check_linked_cards_unfulfilled.json';

    case LinkedCardStatusCheckingEntity.verdictWaitingForCardIssuing:
      return 'check_linked_cards_waiting_for_issuing.json';

    case LinkedCardStatusCheckingEntity.verdictUnqualifiedCard:
      return 'check_linked_cards_unqualified.json';

    case LinkedCardStatusCheckingEntity.verdictSuccess:
      return 'check_linked_cards_success.json';

    case LinkedCardStatusCheckingEntity.verdictDuplicatedLinkRequest:
      return 'check_linked_cards_duplicate_request.json';

    case LinkedCardStatusCheckingEntity.verdictUnqualifiedUserInformation:
      return 'check_linked_cards_unqualified_user_information.json';

    // PREPARE LINK
    case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidPhoneNumber:
      return 'check_linked_cards_invalid_phone_number.json';

    case LinkedCardStatusCheckingEntity.verdictLinkCardAlreadyLinked:
      return 'check_linked_cards_already_linked.json';

    case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidParameters:
      return 'check_linked_card_invalid_parameters.json';

    case LinkedCardStatusCheckingEntity.verdictLinkCardLinkRequestIsProcessing:
      return 'check_linked_card_link_request_is_processing.json';

    case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidBankCode:
      return 'check_linked_card_link_card_invalid_bank_code.json';

    case LinkedCardStatusCheckingEntity.verdictLinkCardNotFoundLinkInfo:
      return 'check_linked_card_link_card_not_found_link_info.json';

    case LinkedCardStatusCheckingEntity.verdictLinkCardBankProductNotSupported:
      return 'check_linked_card_link_card_bank_product_not_supported.json';

    case LinkedCardStatusCheckingEntity.verdictLinkCardFailure:
      return 'check_linked_card_link_card_failure.json';

    case LinkedCardStatusCheckingEntity.verdictFailureAll:
      return 'check_linked_card_failure.json';

    default:
      return 'check_linked_cards_unknown.json';
  }
}

String submitLinkCardMockFileName({String? mockFileName}) {
  return mockFileName ?? 'submit_link_card_open_three_d_secure.json';
}

String checkLinkCardSubmissionStatusMockFileName({ManualLinkCardResultType? mockTypeVerdict}) {
  switch (mockTypeVerdict) {
    case ManualLinkCardResultType.succeeded:
      return 'get_link_card_submission_status_case_succeed.json';

    case ManualLinkCardResultType.processing:
      return 'get_link_card_submission_status_case_processing.json';

    case ManualLinkCardResultType.failed:
      return 'get_link_card_submission_status_case_failed.json';

    default:
      return 'get_link_card_submission_status_case_unknown.json';
  }
}
