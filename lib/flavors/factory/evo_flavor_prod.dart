import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:datadog_tracking_http_client/datadog_tracking_http_client.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';

import '../evo_flavor_value_config.dart';
import 'evo_flavor.dart';

class EvoFlavorProd extends EvoFlavor {
  @override
  CommonFlavorValues getFlavorValue() {
    return CommonFlavorValues(
      baseUrl: EvoFlavorValueConfig.baseUrlProd,
      initializeFirebaseSdk: true,
      // Production - Onesignal account management: <EMAIL>
      oneSignalAppId: EvoFlavorValueConfig.oneSignalAppIdProd,
      commonDataDogConfig: CommonDataDogConfig(
        datadogConfiguration: DatadogConfiguration(
            clientToken: EvoFlavorValueConfig.dataDogClientTokenProd,
            env: EvoFlavorValueConfig.dataDogEnvProd,
            site: DatadogSite.us1,
            loggingConfiguration: DatadogLoggingConfiguration(),
            firstPartyHosts: <String>[EvoFlavorValueConfig.baseUrlProd],
            rumConfiguration: DatadogRumConfiguration(
              applicationId: EvoFlavorValueConfig.dataDogAppIdProd,
            ))
          ..enableHttpTracking(),
        logCustomTags: EvoFlavorValueConfig.dataDogLoggerTagsProd,
      ),
    );
  }
}
