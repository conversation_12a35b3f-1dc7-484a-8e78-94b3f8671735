import 'package:flutter_common_package/data/http_client/mock_config.dart';

import '../../feature/biometric_pin_confirm/biometric_pin_data.dart';
import '../request/order_flow_type.dart';
import '../request/transaction_history_request.dart';
import '../request/transaction_history_request_v2.dart';
import '../response/cancel_transaction_entity.dart';
import '../response/clone_order_entity.dart';
import '../response/confirm_and_pay_order_entity.dart';
import '../response/create_order_entity.dart';
import '../response/payment_result_entity.dart';
import '../response/transaction_history_entity.dart';
import '../response/update_order_entity.dart';
import '../response/vn_pay_qr_info_entity.dart';

enum PaymentService {
  outrightPurchase('outright_purchase'),
  emi('emi');

  final String value;

  const PaymentService(this.value);
}

abstract class CheckOutRepo {
  Future<CreateOrderEntity> createOrder({
    required String? storeId,
    required String? productCode,
    required int? amount,
    required String? desc,
    required VNPayQrInfoEntity? vnPayQrInfo,
    PaymentService paymentService = PaymentService.outrightPurchase,
    MockConfig? mockConfig,
  });

  Future<CreateOrderEntity> createOrderByOrderId({
    required String? storeId,
    required String? productCode,
    required String? merchantId,
    required String? orderId,
    MockConfig? mockConfig,
  });

  Future<ConfirmAndPayOrderEntity> confirmAndPay({
    required AuthenticateType authType,
    String? sessionId,
    String? idempotencyKey,
    int? userChargeAmount,
    String? biometricToken,
    String? paymentMethodId,
    String? pin,
    List<int?>? voucherIds,
    String? emiOfferId,
    MockConfig? mockConfig,
  });

  Future<CreateOrderEntity> getCheckOutDetail(String? id, {MockConfig? mockConfig});

  Future<PaymentResultEntity> getTransactionDetail({
    required String? transactionId,
    MockConfig? mockConfig,
  });

  Future<CancelTransactionEntity> cancelTransaction(String? id, {MockConfig? mockConfig});

  Future<UpdateOrderEntity> updateOrder({
    String? sessionId,
    String? paymentMethodId,
    List<int?>? voucherIds,
    OrderFlowType? flowType,
    String? emiOfferId,
    MockConfig? mockConfig,
  });

  Future<TransactionHistoryEntity> getTransactionsHistory({
    required TransactionHistoryRequest request,
    MockConfig? mockConfig,
  });

  Future<TransactionHistoryEntity> getTransactionsHistoryV2({
    required TransactionHistoryRequestV2 request,
    MockConfig? mockConfig,
  });

  Future<CloneOrderEntity> cloneOrder({
    required String? sessionId,
    MockConfig? mockConfig,
  });
}
