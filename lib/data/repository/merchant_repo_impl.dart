import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../response/store_entity.dart';
import 'base_repo.dart';
import 'merchant_repo.dart';

class MerchantRepoImpl extends BaseRepo implements MerchantRepo {
  // API urls
  static const String storeUrl = 'store';

  MerchantRepoImpl(super.client);

  @override
  Future<StoreEntity> getStore(String? id, {MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get('$storeUrl/$id', mockConfig: mockConfig);
    final StoreEntity storeEntity = commonUtilFunction.serialize(
            () => StoreEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        StoreEntity.unserializable();
    return storeEntity;
  }
}
