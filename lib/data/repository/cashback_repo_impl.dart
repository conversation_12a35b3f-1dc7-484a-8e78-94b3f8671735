import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../request/cashback_records_request.dart';
import '../response/cashback_result_entity.dart';
import '../response/records_cashback_entity.dart';
import 'base_repo.dart';
import 'cashback_repo.dart';

class CashbackRepoImpl extends BaseRepo implements CashbackRepo {
  // API urls
  static const String listCashbackUrl = 'cashbacks';

  static const String cashbackUrl = 'cashback';

  CashbackRepoImpl(super.client);

  @override
  Future<RecordsCashbackEntity> getCashbackRecords({
    required CashbackRecordsRequest request,
    MockConfig? mockConfig,
  }) async {
    final BaseResponse baseResponse = await client.get(
      listCashbackUrl,
      params: request.toJson(),
      mockConfig: mockConfig,
    );

    final RecordsCashbackEntity cashbackItemEntity = commonUtilFunction.serialize(
            () => RecordsCashbackEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        RecordsCashbackEntity.unserializable();

    return cashbackItemEntity;
  }

  @override
  Future<CashBackResultEntity> getCashbackResult({
    required String? transactionId,
    MockConfig? mockConfig,
  }) async {
    final BaseResponse baseResponse = await client.get(
      cashbackUrl,
      params: <String, dynamic>{'txn_id': transactionId},
      mockConfig: mockConfig,
    );
    final CashBackResultEntity result = commonUtilFunction.serialize(
          () => CashBackResultEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        CashBackResultEntity.unserializable();
    return result;
  }
}
