import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class AnnouncementStatusEntity extends BaseEntity {
  AnnouncementStatusEntity({
    this.id,
    this.status,
  });

  final int? id;
  final String? status;

  AnnouncementStatusEntity.unserializable()
      : id = null,
        status = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  AnnouncementStatusEntity.fromBaseResponse(BaseResponse super.response)
      : id = response.data?['id'] as int?,
        status = response.data?['status'] as String?,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'id': id,
      'status': status,
    });
    return json;
  }
}
