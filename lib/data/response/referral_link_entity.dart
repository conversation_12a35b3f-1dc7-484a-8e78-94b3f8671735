import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class ReferralLinkEntity extends BaseEntity {
  static const String verdictSuccess = 'success';
  static const String verdictUserNotQualified = 'user_not_qualified';
  static const String verdictInvalidCampaign = 'invalid_campaign';

  final String? referralLink;
  final String? shareContent;

  ReferralLinkEntity({
    this.referralLink,
    this.shareContent,
  });

  ReferralLinkEntity.unserializable()
      : referralLink = null,
        shareContent = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  ReferralLinkEntity.fromBaseResponse(BaseResponse super.response)
      : referralLink = response.data?['referral_link'] as String?,
        shareContent = response.data?['share_content'] as String?,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'referral_link': referralLink,
      'share_content': shareContent,
    });
    return json;
  }
}
