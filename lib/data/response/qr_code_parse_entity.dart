import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'vn_pay_qr_info_entity.dart';

/// Refer to: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3654778899/ES+CO+Payment+VNPay+QR#API-Spec
class QrCodeParseEntity extends BaseEntity {
  /// Status code [CommonHttpClient.BAD_REQUEST]
  static const String verdictInvalidQRFormat = 'invalid_qr_format';
  static const String verdictOrderNotFound = 'order_not_found';
  static const String verdictInvalidOrderStatus = 'invalid_order_status';
  static const String verdictInvalidProductCode = 'invalid_product_code';
  static const String verdictOrderExpired = 'order_expired';
  static const String verdictOrderInPayment = 'order_in_payment';
  static const String verdictPayTimesExceeded = 'pay_times_exceeded';
  static const String verdictStoreInactive = 'store_inactive';
  static const String verdictMerchantInactive = 'merchant_inactive';
  static const String verdictStoreNotFound = 'store_not_found';
  static const String verdictOrderSucceeded = 'order_succeeded';
  static const String verdictOrderFailed = 'order_failed';
  static const String verdictOrderPending = 'order_requires_manual';
  static const String verdictOrderCancelled = 'order_cancelled';

  final String? merchantId;
  final String? storeId;
  final String? productCode;
  final String? type;
  final String? orderId;
  final VNPayQrInfoEntity? vnPayQrInfo;

  QrCodeParseEntity({
    this.type,
    this.productCode,
    this.storeId,
    this.merchantId,
    this.orderId,
    this.vnPayQrInfo,
  });

  QrCodeParseEntity.unserializable()
      : merchantId = null,
        storeId = null,
        productCode = null,
        type = null,
        orderId = null,
        vnPayQrInfo = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  QrCodeParseEntity.fromBaseResponse(BaseResponse super.response)
      : merchantId = response.data?['merchant_id'] as String?,
        storeId = response.data?['store_id'] as String?,
        productCode = response.data?['product_code'] as String?,
        orderId = response.data?['order_id'] as String?,
        type = response.data?['type'] as String?,
        vnPayQrInfo = (response.data?['vnpay_qr_info'] as Map<String, dynamic>?) != null
            ? VNPayQrInfoEntity.fromJson(response.data?['vnpay_qr_info'] as Map<String, dynamic>)
            : null,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'merchant_id': merchantId,
      'store_id': storeId,
      'product_code': productCode,
      'order_id': orderId,
      'type': type,
      'vnpay_qr_info': vnPayQrInfo?.toJson(),
    });
    return json;
  }

  @override
  String toString() {
    return 'QrCodeParseEntity{merchantId: $merchantId, storeId: $storeId, productCode: $productCode, orderId: $orderId, type: $type, vnPayQrInfo: ${vnPayQrInfo.toString()}}';
  }
}
