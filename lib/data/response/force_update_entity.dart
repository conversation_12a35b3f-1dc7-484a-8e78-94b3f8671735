import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class ForceUpdateEntity extends BaseEntity {
  bool? forceToUpdate;
  bool? hasNewerVersion;
  String? latestVersion;

  ForceUpdateEntity.unserializable()
      : forceToUpdate = null,
        hasNewerVersion = null,
        latestVersion = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  ForceUpdateEntity({
    this.forceToUpdate,
    this.hasNewerVersion,
    this.latestVersion,
  });

  ForceUpdateEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : forceToUpdate = baseResponse.data?['force_to_update'] as bool?,
        hasNewerVersion = baseResponse.data?['has_newer_version'] as bool?,
        latestVersion = baseResponse.data?['latest_version'] as String?,
        super.fromBaseResponse();
}
