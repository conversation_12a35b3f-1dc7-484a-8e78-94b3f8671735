import 'package:flutter_common_package/util/functions.dart';

import '../repository/checkout_repo.dart';
import 'emi_info_entity.dart';
import 'emi_package_entity.dart';
import 'merchant_info_entity.dart';
import 'order_info_entity.dart';
import 'payment_method_entity.dart';
import 'promotion_info_entity.dart';
import 'store_info_entity.dart';
import 'transaction_error_entity.dart';

enum TransactionType {
  purchase('purchase'),
  cashback('cashback');

  const TransactionType(this.value);

  final String value;

  static TransactionType fromString(String? value) {
    switch (value) {
      case 'cashback':
        return TransactionType.cashback;
      case 'purchase':
      default:
        return TransactionType.purchase;
    }
  }
}

class PaymentResultTransactionEntity {
  final String? id;
  final String? createdAt;
  final String? updatedAt;
  final OrderInfoEntity? orderInfo;
  final PaymentMethodEntity? paymentMethod;
  final String? status;
  final StoreInfoEntity? storeInfo;
  final MerchantInfoEntity? merchant;
  final TransactionErrorEntity? lastError;
  final int? fee;
  final int? orderAmount;
  final int? promotionAmount;
  final int? userChargeAmount;
  final String? paymentService;
  final PromotionInfoEntity? promotionInfo;
  final TransactionType? type;
  final int? cashbackAmount;

  final EmiInfoEntity? emiInfo;

  DateTime? get createdAtDateTime => commonUtilFunction.toDateTime(createdAt);

  String? get createdAtHeader => commonUtilFunction.toDateTime(createdAt)?.month.toString();

  DateTime? get updatedAtDateTime => commonUtilFunction.toDateTime(updatedAt);

  String? get updatedAtHeader => commonUtilFunction.toDateTime(updatedAt)?.month.toString();

  EmiPackageEntity? get emiPackage => emiInfo?.emiPackage;

  PaymentResultTransactionEntity({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.orderInfo,
    this.paymentMethod,
    this.status,
    this.merchant,
    this.storeInfo,
    this.lastError,
    this.fee,
    this.orderAmount,
    this.promotionAmount,
    this.userChargeAmount,
    this.emiInfo,
    this.paymentService,
    this.promotionInfo,
    this.type,
    this.cashbackAmount,
  });

  PaymentResultTransactionEntity.fromJson(Map<String, dynamic> json)
      : id = json['id'] as String?,
        fee = json['fee'] as int?,
        orderAmount = json['order_amount'] as int?,
        promotionAmount = json['promotion_amount'] as int?,
        userChargeAmount = json['user_charge_amount'] as int?,
        createdAt = json['created_at'] as String?,
        updatedAt = json['updated_at'] as String?,
        paymentService = json['payment_service'] as String?,
        emiInfo = (json['emi_info'] as Map<String, dynamic>?) != null
            ? EmiInfoEntity.fromJson(json['emi_info'] as Map<String, dynamic>)
            : null,
        orderInfo = (json['order_info'] as Map<String, dynamic>?) != null
            ? OrderInfoEntity.fromJson(json['order_info'] as Map<String, dynamic>)
            : null,
        paymentMethod = (json['payment_method'] as Map<String, dynamic>?) != null
            ? PaymentMethodEntity.fromJson(json['payment_method'] as Map<String, dynamic>)
            : null,
        status = json['status'] as String?,
        merchant = (json['merchant_info'] as Map<String, dynamic>?) != null
            ? MerchantInfoEntity.fromJson(json['merchant_info'] as Map<String, dynamic>)
            : null,
        storeInfo = (json['store_info'] as Map<String, dynamic>?) != null
            ? StoreInfoEntity.fromJson(json['store_info'] as Map<String, dynamic>)
            : null,
        lastError = (json['last_error'] as Map<String, dynamic>?) != null
            ? TransactionErrorEntity.fromJson(json['last_error'] as Map<String, dynamic>)
            : null,
        promotionInfo = (json['promotion_info'] as Map<String, dynamic>?) != null
            ? PromotionInfoEntity.fromJson(
                json['promotion_info'] as Map<String, dynamic>,
              )
            : null,
        type = TransactionType.fromString(json['type']) as TransactionType?,
        cashbackAmount = json['amount'] as int?;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'fee': fee,
        'order_amount': orderAmount,
        'promotion_amount': promotionAmount,
        'user_charge_amount': userChargeAmount,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'order_info': orderInfo?.toJson(),
        'payment_method': paymentMethod?.toJson(),
        'status': status,
        'merchant_info': merchant?.toJson(),
        'store_info': storeInfo?.toJson(),
        'last_error': lastError?.toJson(),
        'emi_info': emiInfo?.toJson(),
        'payment_service': paymentService,
        'promotion_info': promotionInfo?.toJson(),
        'type': type?.value,
        'amount': cashbackAmount,
      };
}

extension PaymentResultTransactionEntityEx on PaymentResultTransactionEntity {
  bool get isPosLimitError => lastError?.verdict == TransactionErrorEntity.verdictPosLimitError;

  bool get isEmiTransaction => paymentService == PaymentService.emi.value;
}
