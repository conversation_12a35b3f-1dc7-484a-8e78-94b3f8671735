import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class DecreeVersionEntity extends BaseEntity {
  final int? id;
  final int? version;
  final String? url;
  final String? effectiveFrom;

  DecreeVersionEntity.unserializable()
      : id = null,
        version = null,
        url = null,
        effectiveFrom = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  DecreeVersionEntity({
    this.url,
    this.id,
    this.version,
    this.effectiveFrom,
  });

  DecreeVersionEntity.fromJson(Map<dynamic, dynamic>? json)
      : id = json?['id'] as int?,
        url = json?['url'] as String?,
        version = json?['version'] as int?,
        effectiveFrom = json?['effective_from'] as String?;

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'id': id,
      'url': url,
      'version': version,
      'effective_from': effectiveFrom,
    });
    return json;
  }
}
