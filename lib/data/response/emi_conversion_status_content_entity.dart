class EmiInfoConversionStatusContentEntity {
  final String? title;
  final String? shortTitle;
  final String? subTitle;
  final String? description;
  final String? userMessage;

  EmiInfoConversionStatusContentEntity({
    this.title,
    this.shortTitle,
    this.subTitle,
    this.description,
    this.userMessage,
  });

  EmiInfoConversionStatusContentEntity.fromJson(Map<String, dynamic> json)
      : title = json['title'],
        shortTitle = json['short_title'],
        subTitle = json['sub_title'],
        description = json['description'],
        userMessage = json['user_message'];

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['short_title'] = shortTitle;
    data['sub_title'] = subTitle;
    data['description'] = description;
    data['user_message'] = userMessage;
    return data;
  }
}
