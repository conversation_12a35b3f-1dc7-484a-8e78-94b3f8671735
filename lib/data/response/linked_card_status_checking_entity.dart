import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'action_entity.dart';

enum LinkCardStatusChallengeType {
  faceOtp('face_otp'),
  faceAuth('face_auth'),
  none('none');

  final String value;

  const LinkCardStatusChallengeType(this.value);
}

class LinkedCardStatusCheckingEntity extends BaseEntity {
  static const String verdictSuccess = 'success';
  static const String verdictUnfulfilledCard = 'unfulfilled_card';
  static const String verdictWaitingForCardIssuing = 'waiting_for_card_issuing';
  static const String verdictUnqualifiedCard = 'unqualified_card';
  static const String verdictDuplicatedLinkRequest = 'duplicated_link_request';
  static const String verdictUnqualifiedUserInformation = 'unqualified_user_information';

  // PREPARE LINK CARD
  static const String verdictLinkCardInvalidPhoneNumber = 'link_card_invalid_phone_number';
  static const String verdictLinkCardAlreadyLinked = 'link_card_already_linked';
  static const String verdictLinkCardUnqualifiedUserInformation = 'unqualified_user_information';
  static const String verdictLinkCardInvalidParameters = 'link_card_invalid_parameters';
  static const String verdictLinkCardLinkRequestIsProcessing =
      'link_card_link_request_is_processing';
  static const String verdictLinkCardInvalidBankCode = 'link_card_invalid_bank_code';
  static const String verdictLinkCardNotFoundLinkInfo = 'link_card_not_found_link_info';
  static const String verdictLinkCardBankProductNotSupported =
      'link_card_bank_product_not_supported';
  static const String verdictLinkCardFailure = 'link_card_failure';
  static const String verdictFailureAll = 'failure';

  final String? challengeType;
  final String? sessionToken;
  final ActionEntity? action;
  final String? linkCardRequestId;

  LinkedCardStatusCheckingEntity({
    this.challengeType,
    this.sessionToken,
    this.action,
    this.linkCardRequestId,
  });

  LinkedCardStatusCheckingEntity.unserializable()
      : challengeType = null,
        sessionToken = null,
        action = null,
        linkCardRequestId = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  LinkedCardStatusCheckingEntity.fromBaseResponse(BaseResponse super.response)
      : challengeType = response.data?['challenge_type'] as String?,
        sessionToken = response.data?['session_token'] as String?,
        action = (response.data?['action'] as Map<String, dynamic>?) != null
            ? ActionEntity.fromJson(response.data?['action'] as Map<String, dynamic>)
            : null,
        linkCardRequestId = response.data?['link_card_request_id'] as String?,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json['challenge_type'] = challengeType;
    json['session_token'] = sessionToken;
    json['link_card_request_id'] = linkCardRequestId;
    json['action'] = action?.toJson();
    return json;
  }
}
