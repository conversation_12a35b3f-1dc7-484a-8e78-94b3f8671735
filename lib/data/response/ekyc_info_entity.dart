import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class EKYCSessionEntity extends BaseEntity {
  static const String verdictLimitExceeded = 'face_otp_limit_exceeded';

  final String? accessKey;
  final String? eKYCClientUrl;
  final String? expiredAt;
  final int? maxAttempt;
  final int? remainAttempt;
  final String? secretKey;
  final String? sessionToken;
  final String? xLenderRequestId;
  final String? xRequestId;

  EKYCSessionEntity({
    this.accessKey,
    this.eKYCClientUrl,
    this.expiredAt,
    this.maxAttempt,
    this.remainAttempt,
    this.secretKey,
    this.sessionToken,
    this.xLenderRequestId,
    this.xRequestId,
  });

  EKYCSessionEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : accessKey = baseResponse.data?['access_key'] as String?,
        eKYCClientUrl = baseResponse.data?['ekyc_client_url'] as String?,
        expiredAt = baseResponse.data?['expired_at'] as String?,
        maxAttempt = baseResponse.data?['max_attempt'] as int?,
        remainAttempt = baseResponse.data?['remain_attempt'] as int?,
        secretKey = baseResponse.data?['secret_key'] as String?,
        sessionToken = baseResponse.data?['session_token'] as String?,
        xLenderRequestId = baseResponse.data?['x_lender_request_id'] as String?,
        xRequestId = baseResponse.data?['x_request_id'] as String?,
        super.fromBaseResponse();

  /// This method is used to re-use EKYCSessionEntity for FaceOtp flow
  EKYCSessionEntity.fromBaseResponseForFaceOtp(
      BaseResponse super.baseResponse, Map<String, dynamic> json)
      : accessKey = json['access_key'] as String?,
        eKYCClientUrl = json['ekyc_client_url'] as String?,
        expiredAt = json['expired_at'] as String?,
        maxAttempt = json['max_attempt'] as int?,
        remainAttempt = json['remain_attempt'] as int?,
        secretKey = json['secret_key'] as String?,
        // Get session token from base response
        sessionToken = baseResponse.data?['session_token'] as String?,
        xLenderRequestId = json['x_lender_request_id'] as String?,
        xRequestId = json['x_request_id'] as String?,
        super.fromBaseResponse();

  EKYCSessionEntity.unserializable()
      : accessKey = null,
        eKYCClientUrl = null,
        expiredAt = null,
        maxAttempt = null,
        remainAttempt = null,
        secretKey = null,
        sessionToken = null,
        xLenderRequestId = null,
        xRequestId = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'access_key': accessKey,
      'ekyc_client_url': eKYCClientUrl,
      'expire_at': expiredAt,
      'max_attempt': maxAttempt,
      'secret_key': secretKey,
      'session_token': sessionToken,
      'x_lender_request_id': xLenderRequestId,
      'x_request_id': xRequestId,
    });
    return json;
  }
}
