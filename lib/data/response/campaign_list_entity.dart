import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'campaign_entity.dart';

class CampaignListEntity extends BaseEntity {
  final List<CampaignEntity>? campaigns;

  CampaignListEntity({this.campaigns});

  CampaignListEntity.unserializable()
      : campaigns = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  CampaignListEntity.fromBaseResponse(BaseResponse super.response)
      : campaigns = (response.data?['campaigns'] as List<dynamic>?)
            ?.map((dynamic e) => CampaignEntity.fromJson(e as Map<String, dynamic>))
            .toList(),
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(
        <String, dynamic>{'campaigns': campaigns?.map((CampaignEntity v) => v.toJson()).toList()});
    return json;
  }
}
