import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../feature/profile/model/gender.dart';
import '../../resources/global.dart';
import '../../resources/ui_strings.dart';
import '../../util/functions.dart';

class UserInformationEntity extends Equatable {
  const UserInformationEntity({
    this.fullName,
    this.gender,
    this.birthday,
    this.identityCardIssueDate,
    this.identityCardNumber,
    this.phoneNumber,
    this.email,
    this.avatarUrl,
  });

  final String? fullName;
  final String? gender;
  final String? identityCardIssueDate;
  final String? identityCardNumber;
  final String? phoneNumber;
  final String? birthday;
  final String? email;
  final String? avatarUrl;

  factory UserInformationEntity.fromJson(Map<String, dynamic> json) => UserInformationEntity(
        fullName: json['full_name'] as String?,
        gender: json['gender'] as String?,
        birthday: json['birthday'] as String?,
        identityCardIssueDate: json['identity_card_issue_date'] as String?,
        identityCardNumber: json['identity_card_number'] as String?,
        phoneNumber: json['phone_number'] as String?,
        email: json['email'] as String?,
        avatarUrl: json['avatar_url'] as String?,
      );

  Map<String, dynamic> toJson() => <String, dynamic>{
        'full_name': fullName,
        'gender': gender,
        'birthday': birthday,
        'identity_card_issue_date': identityCardIssueDate,
        'identity_card_number': identityCardNumber,
        'phone_number': phoneNumber,
        'email': email,
        'avatar_url': avatarUrl,
      };

  @override
  String toString() {
    return 'UserInformation{fullName: $fullName, gender: $gender, identityCardIssueDate: $identityCardIssueDate, identityCardNumber: $identityCardNumber, phoneNumber: $phoneNumber}';
  }

  @override
  List<Object?> get props => <Object?>[
        fullName,
        gender,
        birthday,
        identityCardIssueDate,
        identityCardNumber,
        phoneNumber,
        email,
        avatarUrl,
      ];
}

extension UserInformationExtension on UserInformationEntity {
  String getGivenName() {
    String prefix = '';
    switch (Gender.formatGenderString(gender)) {
      case Gender.male:
        prefix = EvoStrings.prefixMale;
        break;
      case Gender.female:
        prefix = EvoStrings.prefixFemale;
        break;
      default:
        prefix = '';
        break;
    }

    if (fullName != null && fullName!.isNotEmpty) {
      final String name = fullName!.trim().split(' ').last;
      return '$prefix $name'.trim().uppercaseFirstLetterEachWord();
    } else {
      return EvoStrings.unknownGivenName;
    }
  }

  bool hasFullName() => fullName?.isNotEmpty == true;

  String? getDisplayName() {
    if (hasFullName()) {
      return fullName;
    }

    return phoneNumber?.hiddenByFormat((phoneNumber?.length ?? 0) - phoneNumberFormatNumOfLastShow);
  }

  String? getFormattedBirthday() {
    return evoUtilFunction.formatBirthday(birthday);
  }
}
