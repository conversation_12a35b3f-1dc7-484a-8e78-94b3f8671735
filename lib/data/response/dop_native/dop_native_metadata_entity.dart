import 'package:collection/collection.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';

import 'dop_native_metadata_item_entity.dart';

class DOPNativeMetadataEntity {
  static const String verdictMissingParameters = 'missing_parameters';
  static const String verdictForbiddenParameters = 'forbidden_parameters';
  static const String verdictInvalidParameters = 'invalid_parameters';

  final List<DOPNativeMetadataItemEntity>? metadata;
  final int? statusCode;
  final String? message;
  final String? time;
  final String? verdict;

  DOPNativeMetadataEntity({
    this.metadata,
    this.statusCode,
    this.message,
    this.time,
    this.verdict,
  });

  DOPNativeMetadataEntity.unserializable()
      : metadata = null,
        statusCode = CommonHttpClient.INVALID_FORMAT,
        message = null,
        time = null,
        verdict = null;

  DOPNativeMetadataEntity.fromJson(BaseResponse response)
      : metadata = (response.response?['data'] as List<dynamic>?)
            ?.map((dynamic e) => DOPNativeMetadataItemEntity.fromJson(e as Map<String, dynamic>))
            .toList(),
        statusCode = response.statusCode,
        message = response.response?['message'] as String?,
        time = response.response?['time'] as String?,
        verdict = response.response?['verdict'] as String?;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json.addAll(<String, dynamic>{
      'data': metadata?.map((DOPNativeMetadataItemEntity v) => v.toJson()).toList(),
      'statusCode': statusCode,
      'message': message,
      'time': time,
      'verdict': verdict,
    });
    return json;
  }
}

// ignore_for_file: constant_identifier_names
/// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/DOP+-+API+SPEC#Metadata-type
enum MetadataType {
  ACCOMMODATIONS_ID('ACCOMMODATIONS_ID'),
  APPROVED_FLOW_TYPE('APPROVED_FLOW_TYPE'),
  AUTO_PAYMENT_BANK('AUTO_PAYMENT_BANK'),
  BANK('BANK'),
  BANK_BRANCH('BANK_BRANCH'),
  BANK_BRAND('BANK_BRAND'),
  BANK_PROVINCE('BANK_PROVINCE'),
  BRANCH('BRANCH'),
  BRANCH_DISTRICT('BRANCH_DISTRICT'),
  BRANCH_PROVINCE('BRANCH_PROVINCE'),
  BRAND_TRADE('BRAND_TRADE'),
  CARD_DELIVERY_BRANCH('CARD_DELIVERY_BRANCH'),
  CARD_DELIVERY_TYPE('CARD_DELIVERY_TYPE'),
  CARD_DESIGN('CARD_DESIGN'),
  CARD_DESIGN_GROUP('CARD_DESIGN_GROUP'),
  CARD_ISSUE_PLACE('CARD_ISSUE_PLACE'),
  CARD_OPEN_PURPOSE('CARD_OPEN_PURPOSE'),
  CARD_TYPE('CARD_TYPE'),
  CAREER_ID('CAREER_ID'),
  COMPANY_TYPE('COMPANY_TYPE'),
  COMPANY_TYPE_ID('COMPANY_TYPE_ID'),
  CONTACT_TIME_RANGE('CONTACT_TIME_RANGE'),
  CREDIT_CARD_CATEGORY('CREDIT_CARD_CATEGORY'),
  DELIVERY_CARD_ADDR('DELIVERY_CARD_ADDR'),
  DISTRICT('DISTRICT'),
  DUE_DATE('DUE_DATE'),
  EDUCATION('EDUCATION'),
  EDUCATION_ID('EDUCATION_ID'),
  EMPLOYMENT('EMPLOYMENT'),
  EMPLOYMENT_CAREER('EMPLOYMENT_CAREER'),
  EMPLOYMENT_POS('EMPLOYMENT_POS'),
  EMPLOYMENT_STATUS('EMPLOYMENT_STATUS'),
  EMPLOYMENT_TYPE('EMPLOYMENT_TYPE'),
  ETHNIC('ETHNIC'),
  EXTRA_SERVICE('EXTRA_SERVICE'),
  FEEDBACK_USER_INSIGHT('FEEDBACK_USER_INSIGHT'),
  GENDER('GENDER'),
  HOUSE_OWNERSHIP('HOUSE_OWNERSHIP'),
  ID_CARD_TYPE('ID_CARD_TYPE'),
  LABOR_CONTRACT('LABOR_CONTRACT'),
  LOAN_CATEGORY('LOAN_CATEGORY'),
  LOAN_PURPOSE('LOAN_PURPOSE'),
  LOAN_PURPOSE_ID('LOAN_PURPOSE_ID'),
  MAIL_ADDRESS_ID('MAIL_ADDRESS_ID'),
  MARITAL('MARITAL'),
  MARRIED_ID('MARRIED_ID'),
  NATIONALITY('NATIONALITY'),
  NB_LENDER_RELATIONSHIP('NB_LENDER_RELATIONSHIP'),
  PAYMENT_DATE('PAYMENT_DATE'),
  PAYMENT_TYPE('PAYMENT_TYPE'),
  PIN_MAILER('PIN_MAILER'),
  POSITION_ID('POSITION_ID'),
  PROVINCE('PROVINCE'),
  REG_ADDRESS_STATUS('REG_ADDRESS_STATUS'),
  RELATIONSHIP('RELATIONSHIP'),
  RELATIONSHIP_1('RELATIONSHIP_1'),
  RELATIONSHIP_2('RELATIONSHIP_2'),
  RELATIONSHIP_EMPLOYEE('RELATIONSHIP_EMPLOYEE'),
  RELATIONSHIP_STUDENT('RELATIONSHIP_STUDENT'),
  RESIDENTIAL_PERIOD('RESIDENTIAL_PERIOD'),
  SALE_OFFICE('SALE_OFFICE'),
  SECURITY_QUESTION('SECURITY_QUESTION'),
  STATEMENT_DATE('STATEMENT_DATE'),
  SUBSCRIBE_CHANNEL('SUBSCRIBE_CHANNEL'),
  TITLE('TITLE'),
  WARD('WARD'),
  WORKING_ADDRESS('WORKING_ADDRESS'),
  WORKING_TYPE_ID('WORKING_TYPE_ID'),
  ACQUISITION_REWARD('ACQUISITION_REWARD'),
  ACQUISITION_REWARD_TC('ACQUISITION_REWARD_TC');

  final String value;

  const MetadataType(this.value);

  static MetadataType? byValue(String? value) {
    return MetadataType.values.firstWhereOrNull((MetadataType type) => type.value == value);
  }
}
