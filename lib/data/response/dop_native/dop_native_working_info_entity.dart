class DOPNativeWorkingInfoEntity {
  final String? employmentId;
  final String? employmentStatusId;
  final int? income;
  final String? salaryPaymentMethodId;

  const DOPNativeWorkingInfoEntity({
    this.employmentId,
    this.employmentStatusId,
    this.income,
    this.salaryPaymentMethodId,
  });

  DOPNativeWorkingInfoEntity.fromJson(Map<String, dynamic> json)
      : employmentId = json['employmentId'] as String?,
        employmentStatusId = json['employmentStatusId'] as String?,
        income = json['income'] as int?,
        salaryPaymentMethodId = json['salaryPaymentMethodId'] as String?;

  /// Returned json should exclude null value field
  /// Refer: https://trustingsocial.slack.com/archives/C06BBSRR99P/p1714891289576959
  Map<String, dynamic>? toJson() {
    final Map<String, dynamic> json = <String, dynamic>{
      'employmentId': employmentId,
      'employmentStatusId': employmentStatusId,
      'income': income,
      'salaryPaymentMethodId': salaryPaymentMethodId,
    };
    json.removeWhere((_, dynamic value) {
      return value == null;
    });
    if (json.isEmpty) {
      return null;
    }
    return json;
  }
}
