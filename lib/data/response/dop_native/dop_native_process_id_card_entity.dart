import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class DOPNativeProcessIDCardEntity extends BaseEntity {
  final String? jobId;

  DOPNativeProcessIDCardEntity.unserializable()
      : jobId = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  DOPNativeProcessIDCardEntity.fromBaseResponse(BaseResponse super.response)
      : jobId = response.data?['job_id'],
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'job_id': jobId,
    };
  }
}
