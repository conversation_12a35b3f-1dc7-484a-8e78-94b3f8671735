import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'dop_native_ocr_data_entity.dart';

class DOPNativeOCRBaseDataEntity extends BaseEntity {
  final DOPNativeOCRDataEntity? ocr;

  DOPNativeOCRBaseDataEntity({
    this.ocr,
  });

  DOPNativeOCRBaseDataEntity.unserializable()
      : ocr = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  DOPNativeOCRBaseDataEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : ocr = (baseResponse.data?['ocr'] as Map<dynamic, dynamic>?) != null
            ? DOPNativeOCRDataEntity.fromJson(baseResponse.data?['ocr'] as Map<String, dynamic>)
            : null,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'ocr': ocr?.toJson(),
    });
    return json;
  }
}
