import 'emi_conversion_status_info_entity.dart';
import 'emi_package_entity.dart';

class EmiInfoEntity {
  final String? id;
  final EmiConversionStatusInfoEntity? conversionStatusInfo;
  final EmiPackageEntity? emiPackage;

  EmiInfoEntity({
    this.id,
    this.conversionStatusInfo,
    this.emiPackage,
  });

  EmiInfoEntity.fromJson(Map<String, dynamic> json)
      : id = json['id'] as String?,
        conversionStatusInfo = json['conversion_status_info'] != null
            ? EmiConversionStatusInfoEntity.fromJson(json['conversion_status_info'])
            : null,
        emiPackage = (json['package'] as Map<String, dynamic>?) != null
            ? EmiPackageEntity.fromJson(json['package'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'package': emiPackage?.toJson(),
        'conversion_status_info': conversionStatusInfo?.toJson(),
      };
}
