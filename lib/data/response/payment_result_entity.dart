import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'payment_result_transaction_entity.dart';

class PaymentResultEntity extends BaseEntity {
  PaymentResultTransactionEntity? transaction;

  PaymentResultEntity({this.transaction});

  PaymentResultEntity.fromBaseResponse(BaseResponse super.response)
      : transaction = (response.data?['transaction'] as Map<String, dynamic>?) != null
            ? PaymentResultTransactionEntity.fromJson(
                response.data?['transaction'] as Map<String, dynamic>)
            : null,
        super.fromBaseResponse();

  PaymentResultEntity.unserializable()
      : transaction = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{'transaction': transaction?.toJson()});
    return json;
  }
}
