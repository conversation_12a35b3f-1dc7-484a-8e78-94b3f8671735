class StoreInfoEntity {
  final String? address;
  final String? banner;
  final String? id;
  final String? largeBanner;
  final String? merchantId;
  final String? merchantName;
  final String? status;
  final String? thumbnail;
  final String? name;
  final PaymentConfigEntity? payment;

  StoreInfoEntity({
    this.address,
    this.banner,
    this.id,
    this.largeBanner,
    this.merchantId,
    this.merchantName,
    this.status,
    this.thumbnail,
    this.name,
    this.payment,
  });

  factory StoreInfoEntity.fromJson(Map<String, dynamic> json) => StoreInfoEntity(
        address: json['address'] as String?,
        banner: json['banner'] as String?,
        id: json['id'] as String?,
        largeBanner: json['large_banner'] as String?,
        merchantId: json['merchant_id'] as String?,
        merchantName: json['merchant_name'] as String?,
        status: json['status'] as String?,
        thumbnail: json['thumbnail'] as String?,
        name: json['name'] as String?,
        payment: (json['payment'] as Map<String, dynamic>?) != null
            ? PaymentConfigEntity.fromJson(json['payment'] as Map<String, dynamic>)
            : null,
      );

  Map<String, dynamic> toJson() => <String, dynamic>{
        'address': address,
        'banner': banner,
        'id': id,
        'large_banner': largeBanner,
        'merchant_id': merchantId,
        'merchant_name': merchantName,
        'status': status,
        'thumbnail': thumbnail,
        'name': name,
        'payment': payment?.toJson(),
      };
}

class PaymentConfigEntity {
  final int? minOrderAmount;
  final int? maxOrderAmount;

  PaymentConfigEntity({
    this.minOrderAmount,
    this.maxOrderAmount,
  });

  PaymentConfigEntity.fromJson(Map<String, dynamic> json)
      : minOrderAmount = json['min_order_amount'] as int?,
        maxOrderAmount = json['max_order_amount'] as int?;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'min_order_amount': minOrderAmount,
        'max_order_amount': maxOrderAmount,
      };
}
